import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { And, Any, FindOperator, Like, MoreThan, Repository } from 'typeorm'
import { DEFAULT_PAGINATION_MAX_LIMIT } from '@wisemen/pagination'
import { UnNumber } from '../../entities/un-number.entity.js'
import { ViewUnNumberIndexResponse } from './view-un-number-index.response.js'
import { ViewUnNumberIndexQuery } from './query/view-un-number-index.query.js'

@Injectable()
export class ViewUnNumberIndexUseCase {
  constructor (
    @InjectRepository(UnNumber)
    private readonly unNumberRepository: Repository<UnNumber>
  ) {}

  public async execute (query: ViewUnNumberIndexQuery): Promise<ViewUnNumberIndexResponse> {
    let whereNumberConditions: FindOperator<string> | undefined = undefined
    if (query.pagination?.key?.number !== undefined) {
      whereNumberConditions = MoreThan(query.pagination.key.number)
    }
    if (query.search !== undefined) {
      if (whereNumberConditions !== undefined) {
        whereNumberConditions = And(whereNumberConditions, Like(`%${query.search}%`))
      } else {
        whereNumberConditions = Like(`%${query.search}%`)
      }
    }

    const unNumbers = await this.unNumberRepository.find({
      where: {
        number: whereNumberConditions,
        key: query.filter?.keys ? Any(query.filter.keys) : undefined
      },
      order: {
        number: 'asc'
      },
      take: query.pagination?.limit ?? DEFAULT_PAGINATION_MAX_LIMIT
    })

    return new ViewUnNumberIndexResponse(unNumbers)
  }
}
