import { ArrayMinSize, <PERSON>rray<PERSON>nique, Equals, <PERSON><PERSON><PERSON>y, IsNotEmpty, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { FilterQuery, PaginatedKeysetSearchQuery } from '@wisemen/pagination'
import { IsUndefinable } from '@wisemen/validators'
import { Type } from 'class-transformer'
import { ViewUnNumberIndexPaginationQuery } from './view-un-number-index.pagination-query.js'

export class ViewUnNumberFilterQuery extends FilterQuery {
  @ApiProperty({ type: String, required: false, isArray: true })
  @IsUndefinable()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  keys?: string[]
}

export class ViewUnNumberIndexQuery extends PaginatedKeysetSearchQuery {
  @Equals(undefined)
  sort?: never

  @ApiProperty({ type: ViewUnNumberFilterQuery, required: false })
  @Type(() => ViewUnNumberFilterQuery)
  @IsUndefinable()
  @IsObject()
  @ValidateNested()
  filter?: ViewUnNumberFilterQuery

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  search?: string

  @ApiProperty({ type: ViewUnNumberIndexPaginationQuery, required: false })
  @IsUndefinable()
  @Type(() => ViewUnNumberIndexPaginationQuery)
  @ValidateNested()
  @IsObject()
  pagination?: ViewUnNumberIndexPaginationQuery
}
