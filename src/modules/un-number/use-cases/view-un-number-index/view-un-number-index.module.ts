import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { UnNumber } from '../../entities/un-number.entity.js'
import { ViewUnNumberIndexController } from './view-un-number-index.controller.js'
import { ViewUnNumberIndexUseCase } from './view-un-number-index.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([UnNumber])
  ],
  controllers: [
    ViewUnNumberIndexController
  ],
  providers: [
    ViewUnNumberIndexUseCase
  ],
  exports: [
    ViewUnNumberIndexUseCase
  ]
})
export class ViewUnNumberIndexModule {}
