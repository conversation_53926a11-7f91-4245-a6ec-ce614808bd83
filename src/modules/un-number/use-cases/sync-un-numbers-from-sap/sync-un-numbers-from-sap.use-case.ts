import { Injectable, Logger } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { CronjobUseCase } from '../../../cronjobs/cronjob.use-case.js'
import { SapGetUnNumberIndexUseCase } from '../../../sap/use-cases/get-un-number-index/get-un-number-index.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetUnNumberIndexResponse } from '../../../sap/use-cases/get-un-number-index/get-un-number-index.response.js'
import { UnNumber } from '../../entities/un-number.entity.js'
import { MapUnNumberSapService } from '../../services/map-un-number-sap.service.js'

@Injectable()
export class SyncUnNumbersFromSapUseCase implements CronjobUseCase {
  constructor (
    private readonly sapGetUnNumberIndexUseCase: SapGetUnNumberIndexUseCase,
    private readonly dataSource: DataSource,
    @InjectRepository(UnNumber)
    private readonly unNumberRepository: Repository<UnNumber>
  ) {}

  async execute (): Promise<void> {
    const sapQuery = new SapQuery<SapGetUnNumberIndexResponse>()

    const sapUnNumbers = await this.sapGetUnNumberIndexUseCase.execute(sapQuery)

    let unNumbers = MapUnNumberSapService.mapResultsToUnNumbers(sapUnNumbers)

    // Filter out unNumbers with empty numbers
    unNumbers = unNumbers.filter(unNumber => unNumber.number.trim() !== '')

    // Filter out any duplicates
    const seenUnNumberKeys = new Set<string>()
    const seenUnNumberDgKeys = new Set<string>()
    unNumbers = unNumbers.filter((unNumber) => {
      const key = `${unNumber.number}|${unNumber.packingGroup}`
      const dgKey = unNumber.key
      if (seenUnNumberKeys.has(key)) {
        Logger.log('warn', `Duplicate UN number found: ${unNumber.number} with packing group ${unNumber.packingGroup}. Skipping.`)
        return false
      }
      if (dgKey !== null && seenUnNumberDgKeys.has(dgKey)) {
        Logger.log('warn', `Duplicate UN number DgKey found: ${dgKey}. Skipping.`)
        return false
      }

      seenUnNumberKeys.add(key)
      if (dgKey !== null) {
        seenUnNumberDgKeys.add(dgKey)
      }
      return true
    })

    await transaction(this.dataSource, async () => {
      await this.unNumberRepository.deleteAll()
      await this.unNumberRepository.upsert(unNumbers, {
        conflictPaths: {
          number: true,
          packingGroup: true
        }
      })
    })
  }
}
