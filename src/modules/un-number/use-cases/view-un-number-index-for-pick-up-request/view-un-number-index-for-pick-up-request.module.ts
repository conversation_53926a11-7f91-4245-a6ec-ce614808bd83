import { Module } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { ViewUnNumberIndexModule } from '../view-un-number-index/view-un-number-index.module.js'
import { ViewUnNumberIndexForPickUpRequestUseCase } from './view-un-number-index-for-pick-up-request.use-case.js'
import { ViewUnNumberIndexForPickUpRequestController } from './view-un-number-index-for-pick-up-request.controller.js'

@Module({
  imports: [
    SapModule,
    ViewUnNumberIndexModule
  ],
  controllers: [
    ViewUnNumberIndexForPickUpRequestController
  ],
  providers: [
    ViewUnNumberIndexForPickUpRequestUseCase
  ]
})
export class ViewUnNumberIndexForPickUpRequestModule {}
