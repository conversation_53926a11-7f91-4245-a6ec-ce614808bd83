import { before, describe, it } from 'node:test'
import Sinon, { assert, SinonStubbedInstance } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewUnNumberIndexForPickUpRequestUseCase } from '../view-un-number-index-for-pick-up-request.use-case.js'
import { SapGetUnNumberIndexForPickUpRequestUseCase } from '../../../../sap/use-cases/get-un-number-index-for-pick-up-request/get-un-number-index-for-pick-up-request.use-case.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { ViewUnNumberIndexUseCase } from '../../view-un-number-index/view-un-number-index.use-case.js'
import { GetUnNumberIndexForPickUpRequestResponseBuilder } from '../../../../sap/use-cases/get-un-number-index-for-pick-up-request/tests/get-un-number-index-for-pick-up-request.response.builder.js'
import { ViewUnNumberIndexResponse } from '../../view-un-number-index/view-un-number-index.response.js'
import { ViewUnNumberIndexForPickUpRequestQueryBuilder } from './view-un-number-index-for-pick-up-request.query.builder.js'
import { ViewUnNumberIndexForPickUpRequestFilterQueryBuilder } from './view-un-number-index-for-pick-up-request.filter.builder.js'

describe('View UN number index for pick-up request use-case unit test', () => {
  let useCase: ViewUnNumberIndexForPickUpRequestUseCase

  let authContext: SinonStubbedInstance<AuthContext>
  let sapGetUnNumberIndex: SinonStubbedInstance<SapGetUnNumberIndexForPickUpRequestUseCase>
  let viewUnNumberIndexUseCase: SinonStubbedInstance<ViewUnNumberIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    authContext = Sinon.createStubInstance(AuthContext, {
      getSelectedCustomerId: null
    })
    sapGetUnNumberIndex = Sinon.createStubInstance(SapGetUnNumberIndexForPickUpRequestUseCase)
    viewUnNumberIndexUseCase = Sinon.createStubInstance(ViewUnNumberIndexUseCase)

    useCase = new ViewUnNumberIndexForPickUpRequestUseCase(
      authContext,
      sapGetUnNumberIndex,
      viewUnNumberIndexUseCase
    )
  })

  it('Calls all methods once', async () => {
    sapGetUnNumberIndex.execute.resolves(
      [new GetUnNumberIndexForPickUpRequestResponseBuilder().build()]
    )
    viewUnNumberIndexUseCase.execute.resolves(new ViewUnNumberIndexResponse([]))

    const query = new ViewUnNumberIndexForPickUpRequestQueryBuilder()
      .withFilter(
        new ViewUnNumberIndexForPickUpRequestFilterQueryBuilder().build()
      )
      .build()

    await useCase.execute(query)

    assert.calledOnce(authContext.getSelectedCustomerId)
    assert.calledOnce(sapGetUnNumberIndex.execute)
    assert.calledOnce(viewUnNumberIndexUseCase.execute)
  })
})
