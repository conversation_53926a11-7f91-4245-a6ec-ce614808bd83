import { Injectable } from '@nestjs/common'
import { SapGetUnNumberIndexForPickUpRequestUseCase } from '../../../sap/use-cases/get-un-number-index-for-pick-up-request/get-un-number-index-for-pick-up-request.use-case.js'
import { SapGetUnNumberIndexForPickUpRequestResponse } from '../../../sap/use-cases/get-un-number-index-for-pick-up-request/get-un-number-index-for-pick-up-request.response.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewUnNumberIndexUseCase } from '../view-un-number-index/view-un-number-index.use-case.js'
import { ViewUnNumberIndexForPickUpRequestResponse } from './view-un-number-index-for-pick-up-request.response.js'
import { ViewUnNumberIndexForPickUpRequestQuery } from './query/view-un-number-index-for-pick-up-request.query.js'

@Injectable()
export class ViewUnNumberIndexForPickUpRequestUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly sapGetUnNumberIndex: SapGetUnNumberIndexForPickUpRequestUseCase,
    private readonly viewUnNumberIndexUseCase: ViewUnNumberIndexUseCase
  ) {}

  public async execute (
    query: ViewUnNumberIndexForPickUpRequestQuery
  ): Promise<ViewUnNumberIndexForPickUpRequestResponse> {
    // 1. Get the restricted UN numbers using a separate SAP endpoint
    const sapQuery = this.getSapQuery(query)
    const sapResponse = await this.sapGetUnNumberIndex.execute(sapQuery)

    const unNumberKeys = sapResponse
      .map(unNumber => unNumber.Dgkey)
      .filter((dgKey): dgKey is string => dgKey !== undefined && dgKey.trim() !== '')

    if (unNumberKeys.length === 0) return new ViewUnNumberIndexForPickUpRequestResponse([])

    // 2. Retrieve the matching UN numbers from the database
    const uniqueUnNumberKeys = Array.from(new Set(unNumberKeys))
    const unNumbers = await this.viewUnNumberIndexUseCase.execute({
      filter: {
        keys: uniqueUnNumberKeys
      }
    })

    return new ViewUnNumberIndexForPickUpRequestResponse(unNumbers.items)
  }

  private getSapQuery (
    query: ViewUnNumberIndexForPickUpRequestQuery
  ): SapQuery<SapGetUnNumberIndexForPickUpRequestResponse> {
    const sapQuery = new SapQuery<SapGetUnNumberIndexForPickUpRequestResponse>()
      .addSelect([
        'Dgkey'
      ])
      .where('Vbeln', query.filter.contractNumber.padStart(10, '0'))
      .andWhere('Posnr', query.filter.contractItem)

    if (query.filter.tcNumber !== undefined) {
      sapQuery.andWhere('ActionNr', query.filter.tcNumber)
    }

    const selectedCustomer = this.authContext.getSelectedCustomerId()
    if (selectedCustomer != null) {
      sapQuery.andWhere('KunnrAg', selectedCustomer.padStart(10, '0'))
    }

    return sapQuery
  }
}
