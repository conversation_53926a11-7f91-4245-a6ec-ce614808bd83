import { randomUUID } from 'crypto'
import { SapGetUnNumberIndexResponse } from '../../sap/use-cases/get-un-number-index/get-un-number-index.response.js'
import { SAP_ENDPOINTS } from '../../sap/constants/endpoint.constants.js'
import { UnNumber } from '../entities/un-number.entity.js'
import { mapSapIValueToPackingGroup, PackingGroup } from '../../waste-inquiry/enums/packaging-group.enum.js'
import { DEFAULT_LANGUAGE } from '../../localization/constants/defaults.constant.js'

export class MapUnNumberSapService {
  static mapResultToUnNumber (response: SapGetUnNumberIndexResponse): Omit<UnNumber, 'createdAt' | 'updatedAt'> {
    return {
      key: response.Dgkey,
      number: response.Dgnu,
      packingGroup: response.Pgro !== ''
        ? mapSapIValueToPackingGroup(response.Pgro)
        : PackingGroup.NOT_APPLICABLE,
      dangerLabel1: response.Dgcl1 !== '' ? response.Dgcl1 : null,
      dangerLabel2: response.Dgcl2 !== '' ? response.Dgcl2 : null,
      dangerLabel3: response.Dgcl3 !== '' ? response.Dgcl3 : null,
      // TODO check for other languages once coupling with SAP https://linear.app/wisemen/issue/IND-207/as-a-user-i-can-select-hazard-inducers-for-some-un-codes#comment-bf85e37f
      isHazardous: response.Dgna.includes('N.O.S.'),
      description: response.Dgna,
      language: DEFAULT_LANGUAGE // TODO https://linear.app/wisemen/issue/IND-324/api-retrieve-un-numbers-from-sap#comment-c689ce72
    }
  }

  static mapResultsToUnNumbers (responses: SapGetUnNumberIndexResponse[] = []): Omit<UnNumber, 'createdAt' | 'updatedAt'>[] {
    return responses.map(response => this.mapResultToUnNumber(response))
  }

  static mapUnNumberToResult (unNumber: UnNumber): SapGetUnNumberIndexResponse {
    const id = randomUUID()

    return {
      __metadata: {
        id,
        uri: `https://services.odata.org/OData${SAP_ENDPOINTS.UN_NUMBER.INDEX}/${id}`,
        type: 'ZMDX_TAOF_SRV.un'
      },
      Dgnu: unNumber.number,
      Dgna: unNumber.description,
      Pgro: unNumber.packingGroup ?? '',
      Dgcl1: unNumber.dangerLabel1 ?? '',
      Dgcl2: unNumber.dangerLabel2 ?? '',
      Dgcl3: unNumber.dangerLabel3 ?? '',
      Dgkey: '',
      Hnu: '',
      DgnuRemark: ''
    }
  }

  static mapUnNumbersToResults (unNumbers: UnNumber[]): SapGetUnNumberIndexResponse[] {
    return unNumbers.map(unNumber => this.mapUnNumberToResult(unNumber))
  }
}
