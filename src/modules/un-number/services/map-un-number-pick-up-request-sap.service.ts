import assert from 'node:assert'
import { mapSapIValueToPackingGroup, PackingGroup } from '../../waste-inquiry/enums/packaging-group.enum.js'
import { SapGetUnNumberIndexForPickUpRequestResponse } from '../../sap/use-cases/get-un-number-index-for-pick-up-request/get-un-number-index-for-pick-up-request.response.js'
import { UnNumber } from '../types/un-number.type.js'

export class MapUnNumberPickUpRequestSapService {
  static mapResultToUnNumber (
    response: SapGetUnNumberIndexForPickUpRequestResponse
  ): UnNumber {
    assert(response.Dgnu !== undefined)
    assert(response.Psn !== undefined)

    return {
      number: response.Dgnu,
      packingGroup: response.Pgro !== undefined && response.Pgro !== ''
        ? mapSapIValueToPackingGroup(response.Pgro)
        : PackingGroup.NOT_APPLICABLE,
      dangerLabel1: response.Hpn1 !== undefined && response.Hpn1 !== ''
        ? response.Hpn1
        : null,
      dangerLabel2: response.Hpn2 !== undefined && response.Hpn2 !== ''
        ? response.Hpn2
        : null,
      dangerLabel3: response.Hpn3 !== undefined && response.Hpn3 !== ''
        ? response.Hpn3
        : null,
      isHazardous: null, // Only relevant for waste request, not for pick-up request
      description: response.Psn
    }
  }

  static mapResultsToUnNumbers (
    responses: SapGetUnNumberIndexForPickUpRequestResponse[] = []
  ): UnNumber[] {
    return responses.map(response => this.mapResultToUnNumber(response))
  }
}
