import { <PERSON>umn, CreateDate<PERSON>olumn, <PERSON>tity, PrimaryColumn, Unique, UpdateDateColumn } from 'typeorm'
import { PackingGroup } from '../../waste-inquiry/enums/packaging-group.enum.js'
import { Locale } from '../../localization/enums/locale.enum.js'
import { DEFAULT_LANGUAGE } from '../../localization/constants/defaults.constant.js'

@Entity()
@Unique(['key', 'language'])
export class UnNumber {
  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @Column({ type: 'varchar', nullable: true })
  key: string | null

  @PrimaryColumn('char', { length: 4 })
  number: string

  @PrimaryColumn({ type: 'enum', enum: PackingGroup, enumName: 'PackingGroup' })
  packingGroup: PackingGroup

  @Column({ type: 'varchar', nullable: true })
  dangerLabel1: string | null

  @Column({ type: 'varchar', nullable: true })
  dangerLabel2: string | null

  @Column({ type: 'varchar', nullable: true })
  dangerLabel3: string | null

  @Column({ type: 'boolean', default: false })
  isHazardous: boolean

  @Column({ type: 'varchar' })
  description: string

  @Column({ type: 'enum', enum: Locale, enumName: 'Locale', default: DEFAULT_LANGUAGE })
  language: Locale
}
