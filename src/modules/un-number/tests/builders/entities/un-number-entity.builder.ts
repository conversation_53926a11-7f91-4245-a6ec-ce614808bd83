import { randomUUID } from 'crypto'
import { randBoolean, randNumber, randSentence } from '@ngneat/falso'
import { UnNumber } from '../../../entities/un-number.entity.js'
import { getRandomEnumValue } from '../../../../../../test/utils/get-random-enum-value.js'
import { PackingGroup } from '../../../../waste-inquiry/enums/packaging-group.enum.js'
import { DEFAULT_LANGUAGE } from '../../../../localization/constants/defaults.constant.js'

export class UnNumberEntityBuilder {
  private unNumber: UnNumber

  constructor () {
    this.reset()
  }

  reset (): this {
    this.unNumber = new UnNumber()

    this.unNumber.key = randomUUID()
    this.unNumber.number = randNumber({ min: 1000, max: 9999 }).toString()
    this.unNumber.packingGroup = getRandomEnumValue(PackingGroup)
    this.unNumber.dangerLabel1 = randNumber({ min: 1, max: 9 }).toString()
    this.unNumber.dangerLabel2 = randNumber({ min: 1, max: 9 }).toString()
    this.unNumber.dangerLabel3 = randNumber({ min: 1, max: 9 }).toString()
    this.unNumber.isHazardous = randBoolean()
    this.unNumber.description = randSentence()
    this.unNumber.language = DEFAULT_LANGUAGE

    return this
  }

  build (): UnNumber {
    const result = this.unNumber

    this.reset()

    return result
  }
}
