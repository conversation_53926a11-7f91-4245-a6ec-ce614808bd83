{"announcement": {"announcement_not_found": "Ankündigung nicht gefunden"}, "auth": {"azure_unauthorized_id": "Azure Entra ID ist erforderlich", "azure_unauthorized_id_auth_context": "Azure Entra ID ist im Auth-Kontext nicht verfügbar", "azure_unauthorized_roles": "", "azure_unauthorized_upn": "Azure Entra UPN ist erforderlich", "azure_unauthorized_upn_auth_context": "Azure Entra UPN ist im Auth-Kontext nicht verfügbar", "customer_not_found": "Ausgewählter Kunde nicht gefunden", "impersonated_user_not_found": "Simulierter <PERSON>er nicht gefunden", "selected_customer_required": "Ausgewählter Kunde ist erforderlich"}, "contact": {"contact_already_exists": "Ein Kontakt mit dieser E-Mail existiert bereits", "contact_not_found": "Kontakt nicht gefunden"}, "contract-line": {"empty_contract_lines_selection": "<PERSON><PERSON> Vertragszeilen ausgewählt", "contract_line_not_accessible": "Dieser Vertragsgegenstand ist nicht mehr verfügbar. Bitte kontaktieren Sie Ihren Indaver-Vertreter.", "contract_line_not_found": "Die Vertragszeile wurde nicht gefunden", "contract_line_not_of_customer": "Dieser Vertragsgegenstand gehört nicht zum angegebenen Kunden.", "contract_line_not_of_pick_up_addresses": "Die Vertragszeile gehört nicht zu den angegebenen Abholadressen", "contract_line_not_of_waste_producers": "Die Vertragszeile gehört nicht zu den angegebenen Abfallerzeugern"}, "customer": {"customer_not_accessible": "Dieser Kunde ist nicht zugänglich", "customer_not_found": "Kunde mit ID {customerId} nicht gefunden", "customer_not_provided": "Der Kunde muss angegeben werden", "selected_customer_filter_mismatch": "Der ausgewählte Kunde stimmt nicht mit dem Filter überein"}, "delivery-address": {"delivery-address_not_accessible": "Diese Lieferadresse ist nicht zugänglich", "delivery-address_not_found": "Lieferadresse mit der ID {deliveryAddressId} wurde nicht gefunden", "delivery-address_not_provided": "Diese Lieferadresse ist nicht angegeben"}, "dynamic-tables": {"column_not_filterable": "Diese Spalte kann nicht gefiltert werden", "column_not_found": "Die Spalte wurde nicht gefunden", "column_not_hidable": "Die Spalte {columnName} kann nicht ausgeblendet werden", "column_not_sortable": "Diese Spalte kann nicht sortiert werden", "duplicate_column": "Eine Spalte ist mehrfach enthalten", "duplicate_view_name": "Der Name der Ansicht darf nicht identisch mit einer globalen oder aktuellen Ansicht sein", "dynamic_table_view_not_found": "Dynamische Tabellenansicht nicht gefunden", "global_default_view_not_deletable": "Die globale Standardansicht einer dynamischen Tabelle kann nicht gelöscht werden", "invalid_global_default": "Die Tabellenansicht muss global sein, um als globaler Standard festgelegt zu werden", "last_global_view_not_deletable": "Die letzte globale Ansicht einer dynamischen Tabelle kann nicht gelö<PERSON>t werden", "unauthorized_manage_global_view": "Nur Systemadministratoren können globale Ansichten verwalten", "unauthorized_delete_view": "<PERSON><PERSON> der Ersteller kann diese Ansicht löschen", "unauthorized_update_view": "Nur der Ersteller kann diese Ansicht aktualisieren"}, "ewc-code": {"ewc_code_not_found": "Dieser EWC-Code existiert nicht", "missing_ewc_levels": "Es fehlen die erforderlichen EWC-Ebenen"}, "exceptions": {"internal_server_error": "Der Server konnte Ihre Anfrage nicht ausführen. Bitte versuchen Sie es später erneut.", "service_unavailable": "Der Server konnte Ihre Anfrage nicht ausführen. Bitte versuchen Sie es später erneut."}, "files": {"file_not_accessible": "Diese Date<PERSON> ist nicht zugänglich", "file_not_found": "Datei mit UUID {fileUuid} nicht gefunden"}, "generic": {"date_must_be_after": "Das Datum muss nach {date} liegen", "field_must_be_null": "<PERSON><PERSON> muss leer sein", "missing_required_field": "<PERSON><PERSON> ist erford<PERSON>lich", "not_found": "Nicht gefunden", "unauthorized": "Nicht autorisiert"}, "invoice": {"invalid_invoice_status_combination_default": "Ungültige Kombination von Rechnungsstatus", "not_found": "Rechnung nicht gefunden", "non_approve_or_rejectable_draft_invoice": "Kann <PERSON>chnungen nur mit Status to_Be_Annust genehmigen oder ablehnen"}, "nats": {"nats_cache_unavailable": "Der NATS-<PERSON><PERSON> ist nicht konfiguriert", "nats_client_unavailable": "Der NATS-Client ist nicht konfiguriert"}, "news": {"news-item-translation-exists": "Diese Übersetzung existiert bereits", "news-item-translation-expected": "Mindestens eine Nachrichtenübersetzung wird erwartet", "no-start-date-or-end-date-expected": "<PERSON><PERSON>- oder Enddatum erwartet"}, "packaging-request": {"packaging_request_already_submitted": "Verpackungsanfrage wurde bereits übermittelt", "packaging_request_not_found": "Verpackungsanfrage nicht gefunden", "invalid-transport-mode": "Muss Transportmodus verpackt mit Schiebeplanen sein", "submit_only_packaging_request": "Nur Verpackungsanfragen können auf diese Weise eingereicht werden", "delete_only_packaging_request": "Nur Verpackungsanfragen können auf diese Weise gelöscht werden", "update_only_packaging_request": "Nur Verpackungsanfragen können auf diese Weise aktualisiert werden", "copy_non_submitted_packaging_request": "Kopieren Sie nur bei eingereichten Verpackungsanfragen zulässig", "invalid-packaging-request-copy": "Angeforderte Kopie ist keine Verpackungsanforderung"}, "pick-up-address": {"not_found": "Abholadresse mit ID {pickUpAddressId} nicht gefunden", "pick_up_address_not_accessible": "<PERSON>se Abholadresse ist nicht zugänglich", "pick_up_address_not_provided": "<PERSON>se Abholadresse wurde nicht angegeben"}, "pick-up-request": {"contract-line-id-mismatch": "Die Vertragszeilen-ID muss identisch sein", "exactly_one_contract_line_expected": "Es wird genau eine Vertragszeile erwartet", "invalid_pick_up_request_status_combination": "Der Status 'Entwurf' kann nicht mit anderen Status kombiniert werden", "invalid_pick_up_request_status_combination_default": "Ungültige Kombination von Abholanfrage-Status", "invalid_total_quantity_pallets": "Die Gesamtmenge der Paletten muss größer oder gleich der Materialmenge sein", "invalid_transport_mode": "{transportMode} sollte nicht in der Wochenplanungsanfrage enthalten sein", "invalid_transport_mode_default": "Ungültiger Transportmodus", "pick_up_request_already_submitted": "Die Abholanfrage wurde bereits eingereicht", "pick_up_request_not_found": "Abholanfrage nicht gefunden", "too_many_contract_lines": "Sie haben zu viele Vertragszeilen ausgewählt", "copy_non_submitted_pick_up_request": "Ko<PERSON>ren Sie nur bei eingereichten Abholanfragen zulässig", "invalid-indascan-submit-status": "SAP -Abholanfragen können nur eingereicht werden, wenn es den Status von Indascan hat", "contract-line-not-found": "Vertragslinie an Position {position} wurde nicht für die Abholanforderung {number} gefunden", "invalid-sap-waste-materials-update": "Kann keine Abfälle auf einer SAP -Abholanforderung hinzufügen oder entfernen", "invalid-pick-up-request-copy": "Angeforderte Kopie ist keine Abholanforderung"}, "roles": {"role_name_already_in_use": "Rollenname {name} wird bereits von einer anderen Rolle verwendet", "role_not_editable": "<PERSON>se <PERSON>e kann nicht bearbeitet werden", "role_not_found": "<PERSON><PERSON> {roleUuid} nicht gefunden"}, "template": {"not-submittable": "Eine Vorlage ist nicht einreichbar"}, "user": {"user_not_found": "<PERSON><PERSON><PERSON> {userUuid} nicht gefunden"}, "waste-inquiry": {"invalid_stable_temperature": "Die angegebene stabile Temperatur ist ungültig", "invalid_waste_inquiry_status_combination": "Der Status 'Entwurf' kann nicht mit anderen Status kombiniert werden", "invalid_waste_inquiry_status_combination_default": "Ungültige Kombination von Abfallanfrage-Status", "min_temperature_must_be_less_than_max": "Die Mindesttemperatur muss niedriger als die Maximaltemperatur sein", "no_analysis_report_files_expected": "<PERSON><PERSON>-<PERSON><PERSON>", "no_option_expected_when_none_selected": "<PERSON><PERSON> <PERSON><PERSON><PERSON>' ausgew<PERSON>hlt ist, darf keine andere Option gewählt werden", "no_sds_files_expected": "<PERSON>ine <PERSON>B-<PERSON><PERSON>", "no_svhc_extra_expected": "<PERSON><PERSON> zusätzlichen SVHC-Informationen erwartet", "temperature_does_not_to_be_provided": "Bei Umgebungstemperatur dürfen keine Min-, <PERSON><PERSON> <PERSON>der Durchschnittstemperatur angegeben werden", "temperature_must_be_provided": "Entweder Min- und Maxtemperatur oder Durchschnittstemperatur muss angegeben werden", "transport_options_invalid": "Transportoptionen sind ungültig", "waste_inquiry_already_submitted": "Die Abfallanfrage wurde bereits eingereicht", "not_found": "Abfallanfrage nicht gefunden"}, "waste-producer": {"waste_producer_not_accessible": "<PERSON>ser Abfallerzeuger ist nicht zugänglich", "not_found": "Abfallerzeuger mit ID {wasteProducerId} nicht gefunden"}, "weekly-planning-request": {"needs-exactly-one-material": "Die Abholanfrage benötigt genau ein Material", "not_submitted_error": "Die Abholanfrage wurde noch nicht eingereicht", "pick-up-request-id-mismatch": "Es gibt eine Unstimmigkeit mit der ID in der Abholanfrage", "weekly-planning-contract-sap-not-found": "", "weekly-planning-id-mismatch": "Es gibt eine Unstimmigkeit mit der ID in der Wochenplanungsanfrage", "weekly_planning_request_already_submitted_error": "Die Wochenplanungsanfrage wurde bereits eingereicht", "weekly_planning_request_not_found": "Wochenplanungsanfrage nicht gefunden"}, "document": {"not-found": "Dokument nicht gefunden"}}