{"announcement": {"announcement_not_found": "Anuncio no encontrado"}, "auth": {"azure_unauthorized_id": "Se requiere el ID de Azure Entra", "azure_unauthorized_id_auth_context": "El ID de Azure Entra no está disponible en el contexto de autenticación", "azure_unauthorized_roles": "", "azure_unauthorized_upn": "Se requiere el UPN de Azure Entra", "azure_unauthorized_upn_auth_context": "El UPN de Azure Entra no está disponible en el contexto de autenticación", "customer_not_found": "Cliente seleccionado no encontrado", "impersonated_user_not_found": "Usuario suplantado no encontrado", "selected_customer_required": "Se requiere un cliente seleccionado"}, "contact": {"contact_already_exists": "Ya existe un contacto con este correo electrónico", "contact_not_found": "Contacto no encontrado"}, "contract-line": {"empty_contract_lines_selection": "No se han seleccionado líneas de contrato", "contract_line_not_accessible": "Este artículo de contrato ya no está disponible. Por favor, contacte a su representante de Indaver.", "contract_line_not_found": "No se puede encontrar la línea de contrato", "contract_line_not_of_customer": "Este artículo de contrato no pertenece al cliente especificado.", "contract_line_not_of_pick_up_addresses": "La línea de contrato no pertenece a las direcciones de recogida especificadas", "contract_line_not_of_waste_producers": "La línea de contrato no pertenece a los productores de residuos especificados"}, "customer": {"customer_not_accessible": "Este cliente no es accesible", "customer_not_found": "Cliente con id {customerId} no encontrado", "customer_not_provided": "Se debe proporcionar el cliente", "selected_customer_filter_mismatch": "El cliente seleccionado no coincide con el filtro"}, "delivery-address": {"delivery-address_not_accessible": "Esta dirección de entrega no es accesible", "delivery-address_not_found": "Dirección de entrega con id {deliveryAddressId} no encontrada", "delivery-address_not_provided": "Esta dirección de entrega no está proporcionada"}, "dynamic-tables": {"column_not_filterable": "Esta columna no se puede filtrar", "column_not_found": "No se pudo encontrar la columna", "column_not_hidable": "La columna {columnName} no se puede ocultar", "column_not_sortable": "Esta columna no se puede ordenar", "duplicate_column": "Una columna está incluida más de una vez", "duplicate_view_name": "El nombre de la vista no puede ser igual a una vista global o actual", "dynamic_table_view_not_found": "Vista de tabla dinámica no encontrada", "global_default_view_not_deletable": "La vista predeterminada global de una tabla dinámica no se puede eliminar", "invalid_global_default": "La vista de tabla debe ser global para establecerla como predeterminada global", "last_global_view_not_deletable": "La última vista global de una tabla dinámica no se puede eliminar", "unauthorized_manage_global_view": "Solo los administradores del sistema pueden gestionar vistas globales", "unauthorized_delete_view": "Solo el creador puede eliminar esta vista", "unauthorized_update_view": "Solo el creador puede actualizar esta vista"}, "ewc-code": {"ewc_code_not_found": "Este código EWC no existe", "missing_ewc_levels": "No tiene todos los niveles EWC requeridos"}, "exceptions": {"internal_server_error": "El servidor no pudo completar su solicitud. Por favor, inténtelo más tarde.", "service_unavailable": "El servidor no pudo completar su solicitud. Por favor, inténtelo más tarde."}, "files": {"file_not_accessible": "Este archivo no es accesible", "file_not_found": "Archivo con uuid {fileUuid} no encontrado"}, "generic": {"date_must_be_after": "La fecha debe ser posterior a {date}", "field_must_be_null": "Este campo debe estar vacío", "missing_required_field": "Este campo es obligatorio", "not_found": "No encontrado", "unauthorized": "No autorizado"}, "invoice": {"invalid_invoice_status_combination_default": "Combinación de estados de factura no válida", "not_found": "Factura no encontrada", "non_approve_or_rejectable_draft_invoice": "Solo puede aprobar o rechazar las facturas de borrador con el estado to_be_approved"}, "nats": {"nats_cache_unavailable": "El caché NATS no está configurado", "nats_client_unavailable": "El cliente NATS no está configurado"}, "news": {"news-item-translation-exists": "Esta traducción ya existe", "news-item-translation-expected": "Se espera al menos 1 traducción de noticia", "no-start-date-or-end-date-expected": "No se espera fecha de inicio o fin"}, "packaging-request": {"packaging_request_already_submitted": "La solicitud de embalaje ya está enviada", "packaging_request_not_found": "Solicitud de embalaje no encontrada", "invalid-transport-mode": "Debe ser modo de transporte embalado con lona corrediza", "submit_only_packaging_request": "Solo la solicitud de embalaje puede enviarse de esta manera", "delete_only_packaging_request": "Solo la solicitud de embalaje puede eliminarse de esta manera", "update_only_packaging_request": "Solo la solicitud de embalaje puede actualizarse de esta manera", "copy_non_submitted_packaging_request": "Copia solo permitida en las solicitudes de embalaje enviadas", "invalid-packaging-request-copy": "La copia solicitada no es una solicitud de empaque"}, "pick-up-address": {"not_found": "Dirección de recogida con id {pickUpAddressId} no encontrada", "pick_up_address_not_accessible": "Esta dirección de recogida no es accesible", "pick_up_address_not_provided": "No se ha proporcionado esta dirección de recogida"}, "pick-up-request": {"contract-line-id-mismatch": "El id de línea de contrato debe ser el mismo", "exactly_one_contract_line_expected": "Se espera exactamente una línea de contrato", "invalid_pick_up_request_status_combination": "No se puede combinar el estado 'borrador' con otros estados", "invalid_pick_up_request_status_combination_default": "Combinación inválida de estados de solicitud de recogida", "invalid_total_quantity_pallets": "La cantidad total de palés debe ser igual o mayor que la cantidad en materiales", "invalid_transport_mode": "No debería tener {transportMode} en la solicitud de planificación semanal", "invalid_transport_mode_default": "Modo de transporte no válido", "pick_up_request_already_submitted": "La solicitud de recogida ya ha sido enviada", "pick_up_request_not_found": "Solicitud de recogida no encontrada", "too_many_contract_lines": "Has seleccionado demasiadas líneas de contrato", "copy_non_submitted_pick_up_request": "Copia solo permitida en las solicitudes de recogida enviadas", "invalid-indascan-submit-status": "Las solicitudes de recogida de SAP solo se pueden enviar cuando tiene el estado de Indascan", "contract-line-not-found": "La línea de contrato en la posición {position} no se encontró para la solicitud de recogida {number}", "invalid-sap-waste-materials-update": "No puede agregar ni eliminar los materiales de desecho en una solicitud de recogida de SAP", "invalid-pick-up-request-copy": "La copia solicitada no es una solicitud de recogida"}, "roles": {"role_name_already_in_use": "El nombre del rol {name} ya está en uso por otro rol", "role_not_editable": "Este rol no es editable", "role_not_found": "Rol {roleUuid} no encontrado"}, "template": {"not-submittable": "Una plantilla no es enviable"}, "user": {"user_not_found": "Usuario {userUuid} no encontrado"}, "waste-inquiry": {"invalid_stable_temperature": "La temperatura estable proporcionada no es válida", "invalid_waste_inquiry_status_combination": "No se puede combinar el estado 'borrador' con otros estados", "invalid_waste_inquiry_status_combination_default": "Combinación inválida de estados de consulta de residuos", "min_temperature_must_be_less_than_max": "La temperatura mínima debe ser menor que la temperatura máxima", "no_analysis_report_files_expected": "No se esperan archivos de informe de análisis", "no_option_expected_when_none_selected": "No se debe seleccionar ninguna otra opción cuando se selecciona 'Ninguno'", "no_sds_files_expected": "No se esperan archivos SDS", "no_svhc_extra_expected": "No se espera información adicional SVHC", "temperature_does_not_to_be_provided": "No se debe proporcionar temperatura mínima, máxima o promedio cuando el tipo es ambiente", "temperature_must_be_provided": "Se debe proporcionar temperatura mínima y máxima o temperatura promedio", "transport_options_invalid": "Opciones de transporte no válidas", "waste_inquiry_already_submitted": "La consulta de residuos ya ha sido enviada", "not_found": "Consulta de residuos no encontrada"}, "waste-producer": {"waste_producer_not_accessible": "Este productor de residuos no es accesible", "not_found": "Productor de residuos con id {wasteProducerId} no encontrado"}, "weekly-planning-request": {"needs-exactly-one-material": "La solicitud de recogida necesita exactamente un material", "not_submitted_error": "La solicitud de recogida aún no ha sido enviada", "pick-up-request-id-mismatch": "Hay una discrepancia con el id en la solicitud de recogida", "weekly-planning-contract-sap-not-found": "", "weekly-planning-id-mismatch": "Hay una discrepancia con el id en la solicitud de planificación semanal", "weekly_planning_request_already_submitted_error": "La solicitud de planificación semanal ya ha sido enviada", "weekly_planning_request_not_found": "Solicitud de planificación semanal no encontrada"}, "document": {"not-found": "Documento no encontrado"}}