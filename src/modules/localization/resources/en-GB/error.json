{"announcement": {"announcement_not_found": "Announcement not found"}, "auth": {"azure_unauthorized_id": "Azure Entra ID is required", "azure_unauthorized_id_auth_context": "Azure Entra ID is not available in the auth context", "azure_unauthorized_roles": "Azure Entra roles are required", "azure_unauthorized_upn": "Azure Entra UPN is required", "azure_unauthorized_upn_auth_context": "Azure Entra UPN is not available in the auth context", "customer_not_found": "Selected customer not found", "impersonated_user_not_found": "Impersonated user not found", "selected_customer_required": "Selected customer is required"}, "contact": {"contact_already_exists": "A contact with this email already exists", "contact_not_found": "Contact not found"}, "contract-line": {"empty_contract_lines_selection": "No contract lines selected", "contract_line_not_accessible": "This contract item is no longer available. Please contact your Indaver representative.", "contract_line_not_found": "The contract line can not be found", "contract_line_not_of_customer": "This contract item is not of the given customer.", "contract_line_not_of_pick_up_addresses": "The contract line is not of the given pick up addresses", "contract_line_not_of_waste_producers": "The contract line is not of the given waste producers"}, "customer": {"customer_not_accessible": "This customer is not accessible", "customer_not_found": "Customer with id {customerId} not found", "customer_not_provided": "The customer must be provided", "selected_customer_filter_mismatch": "The selected customer does not match the filter"}, "delivery-address": {"delivery-address_not_accessible": "This delivery address is not accessible", "delivery-address_not_found": "DeliveryAddress with id {deliveryAddressId} not found", "delivery-address_not_provided": "This delivery address is not provided"}, "document": {"not-found": "Document not found"}, "dynamic-tables": {"column_not_filterable": "This column is not filterable", "column_not_found": "Could not find the column", "column_not_hidable": "The column {columnName} is not hidable", "column_not_sortable": "This column is not sortable", "duplicate_column": "A column is included more than once", "duplicate_view_name": "The name of the view can not be the same as a global or a current view", "dynamic_table_view_not_found": "Dynamic table view not found", "global_default_view_not_deletable": "The global default view from a dynamic table is not deletable", "invalid_global_default": "Table view needs to be global to set as global default", "last_global_view_not_deletable": "The last global view from a dynamic table is not deletable", "unauthorized_manage_global_view": "Only system admins can manage global views", "unauthorized_delete_view": "Only the creator can delete this view", "unauthorized_update_view": "Only the creator can update this view"}, "ewc-code": {"ewc_code_not_found": "This EWC code doesn't exists", "missing_ewc_levels": "Does not have the all the required EWC levels"}, "exceptions": {"internal_server_error": "The server was unable to complete your request. Please try again later.", "service_unavailable": "The server was unable to complete your request. Please try again later."}, "files": {"file_not_accessible": "This file is not accessible", "file_not_found": "File with uuid {fileUuid} not found"}, "generic": {"date_must_be_after": "The date must be after {date}", "field_must_be_null": "This field must be null", "missing_required_field": "This field is required", "not_found": "Not found", "unauthorized": "Unauthorized"}, "invoice": {"invalid_invoice_status_combination_default": "Invalid given combination of invoice statuses", "not_found": "Invoice not found", "non_approve_or_rejectable_draft_invoice": "Can only approve or reject draft invoices with status to_be_approved"}, "nats": {"nats_cache_unavailable": "The NATS cache is not configured", "nats_client_unavailable": "The NATS client is not configured"}, "news": {"news-item-translation-exists": "This translation already exists", "news-item-translation-expected": "At least 1 news item translation is expected", "no-start-date-or-end-date-expected": "No start date or end date expected"}, "packaging-request": {"packaging_request_already_submitted": "Packaging request is already submitted", "packaging_request_not_found": "Packaging request not found", "invalid-transport-mode": "Must be transport mode packaged with curtain sider", "submit_only_packaging_request": "Only packaging request can be submitted this way", "delete_only_packaging_request": "Only packaging request can be deleted this way", "update_only_packaging_request": "Only packaging request can be updated this way", "copy_non_submitted_packaging_request": "Copy only allowed on submitted packaging requests", "invalid-packaging-request-copy": "Requested copy is not a packaging request"}, "pick-up-address": {"not_found": "PickUpAddress with id {pickUpAddressId} not found", "pick_up_address_not_accessible": "This pick-up address is not accessible", "pick_up_address_not_provided": "This pick-up address is not provided"}, "pick-up-request": {"contract-line-id-mismatch": "The contract line id should be the same", "exactly_one_contract_line_expected": "Exactly one contract line is expected", "invalid_pick_up_request_status_combination": "Cannot combine pick-up inquiry status 'draft' with any other status", "invalid_pick_up_request_status_combination_default": "Invalid given combination of pick-up request statuses", "invalid_total_quantity_pallets": "Total quantity of pallets must be equal to or more than quantity in materials", "invalid_transport_mode": "Should not have {transportMode} in weekly planning request", "invalid_transport_mode_default": "Invalid transport mode", "pick_up_request_already_submitted": "The pick up request is already submitted", "pick_up_request_not_found": "Pick-up request not found", "too_many_contract_lines": "You have selected too many contract lines", "copy_non_submitted_pick_up_request": "Copy only allowed on submitted pick up requests", "invalid-indascan-submit-status": "Sap pick up requests can only be submitted when it has the status of Indascan", "contract-line-not-found": "Contract line at position {position} was not found for pick up request {number}", "invalid-sap-waste-materials-update": "Can not add or remove waste materials on a SAP pick up request", "invalid-pick-up-request-copy": "Requested copy is not a pick up request"}, "roles": {"role_name_already_in_use": "Role name {name} is already in use by another role", "role_not_editable": "This role is not editable", "role_not_found": "Role {roleUuid} not found"}, "template": {"not-submittable": "A template is not submittable"}, "user": {"user_not_found": "User {userUuid} not found"}, "waste-inquiry": {"invalid_stable_temperature": "The stable temperature provided is invalid", "invalid_waste_inquiry_status_combination": "Cannot combine waste inquiry status 'draft' with any other status", "invalid_waste_inquiry_status_combination_default": "Invalid given combination of waste inquiry statuses", "min_temperature_must_be_less_than_max": "Min temperature must be less than max temperature", "no_analysis_report_files_expected": "No analysis report files expected", "no_option_expected_when_none_selected": "No other option should be selected when the option 'None' is selected", "no_sds_files_expected": "No SDS files expected", "no_svhc_extra_expected": "No addition SVHC information expected", "temperature_does_not_to_be_provided": "Min, max or average temperature must not be provided when type is ambient", "temperature_must_be_provided": "Either min and max temperature or average temperature must be provided", "transport_options_invalid": "Transport options are invalid", "waste_inquiry_already_submitted": "The waste inquiry is already submitted", "not_found": "Waste inquiry not found"}, "waste-producer": {"waste_producer_not_accessible": "This waste producer is not accessible", "not_found": "Waste producer with id {wasteProducerId} not found"}, "weekly-planning-request": {"needs-exactly-one-material": "The pick up request needs exactly one material", "not_submitted_error": "The pick up request is not yet submitted", "pick-up-request-id-mismatch": "There is a mismatch with id in pick up request", "weekly-planning-contract-sap-not-found": "Weekly planning contract not found in SAP", "weekly-planning-id-mismatch": "There is a mismatch with id in weekly planning request", "weekly_planning_request_already_submitted_error": "The weekly planning request was already submitted", "weekly_planning_request_not_found": "Weekly planning request not found"}}