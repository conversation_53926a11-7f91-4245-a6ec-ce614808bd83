{"announcement": {"created": {"v1": "Announcement created"}, "deleted": {"v1": "Announcement deleted"}, "updated": {"v1": "Announcement updated"}}, "contact": {"created": {"v1": "Contact created"}, "deleted": {"v1": "Contact deleted"}, "updated": {"v1": "Contact updated"}}, "dynamic-table-view": {"created": {"v1": "Dynamic table view created"}, "deleted": {"v1": "Dynamic table view deleted"}, "updated": {"v1": "Dynamic table view updated"}}, "file": {"created": {"v1": "File created"}, "deleted": {"v1": "File deleted"}, "uploaded": {"v1": "File uploaded"}}, "news-item": {"created": {"v1": "News item created"}, "deleted": {"v1": "News item deleted"}, "updated": {"v1": "News item updated"}}, "notification": {"created": {"v1": "Notification created {type}"}, "preference": {"preset": {"updated": {"v1": "Notification preference preset updated"}}}, "read": {"all": {"v1": "All notifications read"}, "v1": "Notification read"}, "types": {"migrated": {"v1": "Notification types migrated"}}, "unread": {"v1": "Notification unread"}}, "packaging-request": {"copied": {"v1": "Packaging request copied"}, "created": {"v1": "Packaging request created"}, "submitted": {"v1": "Packaging request submitted"}, "deleted": {"v1": "Packaging request deleted"}, "updated": {"v1": "Packaging request updated"}}, "pick-up-request": {"copied": {"v1": "Pick-up request copied"}, "created": {"v1": "Pick-up request created"}, "deleted": {"v1": "Pick-up request deleted"}, "submitted": {"v1": "Pick-up request submitted"}, "updated": {"v1": "Pick-up request updated"}}, "pick-up-request-template": {"created": {"v1": "Pick-up request template created"}, "updated": {"v1": "Pick-up request template updated"}, "deleted": {"v1": "Pick-up request template deleted"}}, "role": {"created": {"v1": "Role created"}, "deleted": {"v1": "Role deleted"}, "permissions": {"cache": {"cleared": {"v1": "Permissions cache cleared"}}, "updated": {"v1": "Role permissions updated"}}, "renamed": {"v1": "Role renamed"}}, "test-notification": {"sent": {"v1": "Test notification sent"}}, "user": {"created": {"v1": "User created"}, "default-notification-preferences": {"assigned": {"v1": "Default notification preferences assigned to user"}}, "notification": {"created": {"v1": "User notification created"}}, "role-assigned": {"v1": "Role assigned to user"}}, "waste-inquiry": {"copied": {"v1": "Waste inquiry copied"}, "created": {"v1": "Waste inquiry created"}, "deleted": {"v1": "Waste inquiry deleted"}, "submitted": {"v1": "Waste inquiry submitted"}, "updated": {"v1": "Waste inquiry updated"}}, "weekly-planning-request": {"created": {"v1": "Weekly planning request created"}, "submitted": {"v1": "Weekly planning request submitted"}, "updated": {"v1": "Weekly planning request updated"}}}