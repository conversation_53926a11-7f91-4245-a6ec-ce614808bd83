import { Injectable } from '@nestjs/common'
import { PgBossScheduler } from '@wisemen/pgboss-nestjs-job'
import { PickUpRequestSubmittedEvent } from '../../../pick-up-request/use-cases/submit-pick-up-request/pick-up-request-submitted.event.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { Subscribe } from '../../../domain-events/subscribe.decorator.js'
import { WasteInquirySubmittedEvent } from '../../../waste-inquiry/use-cases/submit-waste-inquiry/waste-inquiry-submitted.event.js'
import { WeeklyPlanningRequestSubmittedEvent } from '../../../weekly-planning-request/use-cases/submit-weekly-planning-request/weekly-planning-request-submitted.event.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { CreateContactsFromFormSubmissionJob, FormType } from './create-contacts-from-form-submission.job.js'

@Injectable()
export class CreateContactsFromFormSubmissionSubscriber {
  constructor (
    private readonly authContext: AuthContext,
    private readonly jobScheduler: PgBossScheduler
  ) {}

  @Subscribe(WasteInquirySubmittedEvent)
  @Subscribe(PickUpRequestSubmittedEvent)
  @Subscribe(WeeklyPlanningRequestSubmittedEvent)
  async handle (
    events: WasteInquirySubmittedEvent[]
      | PickUpRequestSubmittedEvent[]
      | WeeklyPlanningRequestSubmittedEvent[]
  ): Promise<void> {
    const jobs: CreateContactsFromFormSubmissionJob[] = []
    const userUuid = this.authContext.getUserUuidOrFail()

    for (const event of events) {
      if (event instanceof WasteInquirySubmittedEvent) {
        jobs.push(new CreateContactsFromFormSubmissionJob(
          userUuid, event.content.wasteInquiryUuid, FormType.WASTE_INQUIRY
        ))
      } else if (event instanceof PickUpRequestSubmittedEvent) {
        jobs.push(new CreateContactsFromFormSubmissionJob(
          userUuid, event.content.pickUpRequestUuid, FormType.PICK_UP_REQUEST
        ))
      } else if (event instanceof WeeklyPlanningRequestSubmittedEvent) {
        jobs.push(new CreateContactsFromFormSubmissionJob(
          userUuid, event.content.weeklyPlanningRequestUuid, FormType.WEEKLY_PLANNING_REQUEST
        ))
      } else {
        exhaustiveCheck(event)
      }
    }

    await this.jobScheduler.scheduleJobs(jobs)
  }
}
