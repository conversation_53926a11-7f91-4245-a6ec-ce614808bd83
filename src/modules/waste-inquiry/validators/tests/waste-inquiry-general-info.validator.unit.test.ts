import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { CustomerPickUpAddressAuthService } from '../../../auth/services/customer-pick-up-address-auth.service.js'
import { CustomerWasteProducerAuthService } from '../../../auth/services/customer-waste-producer-auth.service.js'
import { UserCustomerAuthService } from '../../../auth/services/user-customer-auth.service.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { PickUpAddressNotAccessibleError } from '../../../pick-up-address/errors/pick-up-address-not-accessible.error.js'
import { WasteProducerNotAccessibleError } from '../../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { WasteInquiryGeneralInfoValidator } from '../waste-inquiry-general-info.validator.js'
import { WasteInquiryEntityBuilder } from '../../tests/waste-inquiry-entity.builder.js'
import { ValidWasteInquiryEntityBuilder } from '../../tests/valid-waste-inquiry-entity.builder.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { FieldMustBeNullError } from '../../../exceptions/generic/field-must-be-null.error.js'
import { MissingRequiredFieldError } from '../../../exceptions/generic/missing-required-field.error.js'

describe('Waste inquiry general info validator unit test', () => {
  let validator: WasteInquiryGeneralInfoValidator

  let authContext: SinonStubbedInstance<AuthContext>
  let userCustomerAuthService: SinonStubbedInstance<UserCustomerAuthService>
  let customerWasteProducerAuthService: SinonStubbedInstance<CustomerWasteProducerAuthService>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>
  let customerPickUpAddressAuthService: SinonStubbedInstance<CustomerPickUpAddressAuthService>

  before(() => {
    TestBench.setupUnitTest()

    authContext = createStubInstance(AuthContext)
    userCustomerAuthService = createStubInstance(UserCustomerAuthService)
    customerWasteProducerAuthService = createStubInstance(CustomerWasteProducerAuthService)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)
    customerPickUpAddressAuthService = createStubInstance(CustomerPickUpAddressAuthService)

    validator = new WasteInquiryGeneralInfoValidator(
      authContext,
      userCustomerAuthService,
      customerWasteProducerAuthService,
      userWasteProducerAuthService,
      customerPickUpAddressAuthService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getAzureEntraUpnOrFail.returns(randomUUID())
    userCustomerAuthService.canUserAccessCustomer.resolves(true)
    userWasteProducerAuthService.canUserAccessWasteProducer.resolves(true)
    customerWasteProducerAuthService.canCustomerAccessWasteProducer.resolves(true)
    customerPickUpAddressAuthService.canCustomerAccessPickUpAddresses.resolves(true)
  }

  describe('Update validation', () => {
    describe('Waste producer', () => {
      const wasteInquiry = new WasteInquiryEntityBuilder()
        .withCustomerId(randomUUID())
        .withWasteProducerId(randomUUID())
        .build()

      it('throws an error when waste producer is not accessible by customer', async () => {
        customerWasteProducerAuthService.canCustomerAccessWasteProducer.resolves(false)

        await expect(validator.validateUpdate(wasteInquiry))
          .rejects.toThrow(WasteProducerNotAccessibleError)
      })

      it('throws an error when wasteProducerId is defined and isUnknownWasteProducer is true', async () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withWasteProducerId(randomUUID())
          .withIsUnknownWasteProducer(true)
          .build()

        await expect(validator.validateUpdate(wasteInquiry))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.wasteProducerId' }
          }))
      })

      it('throws an error when waste producer is not accessible by auth user', async () => {
        userWasteProducerAuthService.canUserAccessWasteProducer.resolves(false)

        await expect(validator.validateUpdate(wasteInquiry))
          .rejects.toThrow(WasteProducerNotAccessibleError)
      })
    })

    describe('Pick up addresses', () => {
      const wasteInquiry = new WasteInquiryEntityBuilder()
        .withCustomerId(randomUUID())
        .withPickUpAddressId(randomUUID())
        .build()

      it('throws an error when pickUpAddresses not accessible by customer', async () => {
        customerPickUpAddressAuthService.canCustomerAccessPickUpAddresses.resolves(false)

        await expect(validator.validateUpdate(wasteInquiry))
          .rejects.toThrow(PickUpAddressNotAccessibleError)
      })

      it('throws an error when pickUpAddress is defined and isUnknownPickUpAddress is true', async () => {
        const wasteInquiry = new WasteInquiryEntityBuilder()
          .withPickUpAddressId(randomUUID())
          .withIsUnknownPickUpAddress(true)
          .build()

        await expect(validator.validateUpdate(wasteInquiry))
          .rejects.toThrow(expect.objectContaining({
            code: new FieldMustBeNullError().code,
            source: { pointer: '$.pickUpAddressId' }
          }))
      })
    })
  })

  describe('Submit validation', () => {
    const wasteInquiry = new ValidWasteInquiryEntityBuilder().build()

    describe('Waste producer', () => {
      it('throws an error when waste producer is not accessible by customer', async () => {
        customerWasteProducerAuthService.canCustomerAccessWasteProducer.resolves(false)

        await expect(validator.validateSubmit(wasteInquiry))
          .rejects.toThrow(WasteProducerNotAccessibleError)
      })

      it('throws an error when wasteProducerId is undefined and isUnknownWasteProducer is false', async () => {
        const invalidWasteInquiry = new WasteInquiryEntityBuilder()
          .withCustomerId(randomUUID())
          .withWasteProducerId(null)
          .withIsUnknownWasteProducer(false)
          .build()

        await expect(validator.validateSubmit(invalidWasteInquiry))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.wasteProducerId' }
          }))
      })

      it('throws an error when waste producer is not accessible by auth user', async () => {
        userWasteProducerAuthService.canUserAccessWasteProducer.resolves(false)

        await expect(validator.validateSubmit(wasteInquiry))
          .rejects.toThrow(WasteProducerNotAccessibleError)
      })
    })

    describe('Pick up addresses', () => {
      it('throws an error when pickUpAddressId is empty and isUnknownPickUpAddress is false', async () => {
        const invalidWasteInquiry = new WasteInquiryEntityBuilder()
          .withCustomerId(randomUUID())
          .withPickUpAddressId(null)
          .withIsUnknownPickUpAddress(false)
          .build()

        await expect(validator.validateSubmit(invalidWasteInquiry))
          .rejects.toThrow(expect.objectContaining({
            code: new MissingRequiredFieldError().code,
            source: { pointer: '$.wasteProducerId' }
          }))
      })

      it('throws an error when pickUpAddresses not accessible by customer', async () => {
        customerPickUpAddressAuthService.canCustomerAccessPickUpAddresses.resolves(false)

        await expect(validator.validateSubmit(wasteInquiry))
          .rejects.toThrow(PickUpAddressNotAccessibleError)
      })
    })
  })
})
