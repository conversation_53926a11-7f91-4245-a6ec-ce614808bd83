import { Injectable } from '@nestjs/common'
import { SapGetWasteInquiryIndexResponse } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { MapUniformWasteInquirySapService } from '../../services/map-uniform-waste-inquiry-sap.service.js'
import { MapUniformWasteInquiryDbService } from '../../services/map-uniform-waste-inquiry-db.service.js'
import { mapWasteInquiryStatusToSapValue, WasteInquiryStatus } from '../../enums/waste-inquiry-status.enum.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { ViewWasteInquiryIndexResponse, ViewWasteInquiryIndexResponseMeta } from './view-waste-inquiry-index.response.js'
import { ViewWasteInquiryIndexValidator } from './view-waste-inquiry-index.validator.js'
import { ViewWasteInquiryIndexQuery } from './query/view-waste-inquiry-index.query.js'
import { ViewWasteInquiryIndexDbQueryKey } from './query/view-waste-inquiry-index.db-query-key.js'
import { ViewWasteInquiryIndexSapQueryKey } from './query/view-waste-inquiry-index.sap-query-key.js'
import { ViewWasteInquiryIndexRepository } from './view-waste-inquiry-index.repository.js'

@Injectable()
export class ViewWasteInquiryIndexUseCase {
  constructor (
    private readonly validator: ViewWasteInquiryIndexValidator,
    private readonly authContext: AuthContext,
    private readonly repository: ViewWasteInquiryIndexRepository,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly sapGetWasteInquiryIndex: SapGetWasteInquiryIndexUseCase
  ) {}

  public async execute (
    query: ViewWasteInquiryIndexQuery
  ): Promise<ViewWasteInquiryIndexResponse> {
    this.validator.validate(query)

    const user = this.authContext.getAuthOrFail()

    if (query.filter.statuses.includes(WasteInquiryStatus.DRAFT)) {
      const dbWasteInquiries = await this.repository.getDraftWasteInquiriesByUserUuid(
        user.uuid,
        query.pagination
      )

      const uniformWasteInquiries = MapUniformWasteInquiryDbService
        .mapResultsToUniformWasteInquiries(
          dbWasteInquiries
        )

      return new ViewWasteInquiryIndexResponse(
        uniformWasteInquiries,
        new ViewWasteInquiryIndexResponseMeta(
          dbWasteInquiries.length > 0
            ? new ViewWasteInquiryIndexDbQueryKey(
              dbWasteInquiries.at(-1)!.firstCollectionDate === null ? 'null' : dbWasteInquiries.at(-1)!.firstCollectionDate,
              dbWasteInquiries.at(-1)!.uuid
            )
            : null
        )
      )
    } else {
      const sapQuery = await this.getSapQuery(query)
      const sapResponse = await this.sapGetWasteInquiryIndex.execute(sapQuery)

      const uniformWasteInquiries = MapUniformWasteInquirySapService
        .mapResultsToUniformWasteInquiries(
          sapResponse.items
        )

      return new ViewWasteInquiryIndexResponse(
        uniformWasteInquiries,
        new ViewWasteInquiryIndexResponseMeta(
          sapResponse.skipToken !== null
            ? new ViewWasteInquiryIndexSapQueryKey(
              sapResponse.skipToken
            )
            : null
        )
      )
    }
  }

  private async getSapQuery (
    query: ViewWasteInquiryIndexQuery
  ): Promise<SapQuery<SapGetWasteInquiryIndexResponse>> {
    const sapStatuses: string[] = []
    for (const status of query.filter.statuses) {
      sapStatuses.push(...mapWasteInquiryStatusToSapValue(status))
    }

    const sapQuery = new SapQuery<SapGetWasteInquiryIndexResponse>(query)
      .addSelect([
        'InquiryNumber',
        'WasteName',
        'RequestDate',
        'Contract',
        'ContractItem',
        'Customer',
        'CustomerName',
        'SalesOrganization',
        'SalesOrganizationName',
        'WasteProducer',
        'WasteProducerName',
        'PickUpAddress',
        'PickUpAddressName',
        'RequestorName',
        'NewRequestStatus',
        'EwcCustomerLevel1',
        'EwcCustomerLevel2',
        'EwcCustomerLevel3'
      ])
      .addOrderBy('CollectionDate', 'desc')
      .where((qb) => {
        qb.where('NewRequestStatus', sapStatuses[0])
        for (let i = 1; i < sapStatuses.length; i++) {
          qb.orWhere('NewRequestStatus', sapStatuses[i])
        }
        return qb
      })

    // Add filters based on selected customer and accessible waste producers
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null) {
      sapQuery.andWhere('Customer', selectedCustomerId)

      const restrictedWasteProducerIds = await this.userWasteProducerAuthService
        .getRestrictedWasteProducerIds(
          this.authContext.getAzureEntraUpnOrFail(),
          selectedCustomerId
        )
      if (restrictedWasteProducerIds !== undefined && restrictedWasteProducerIds.length > 0) {
        sapQuery.andWhere((qb) => {
          for (let i = 0; i < restrictedWasteProducerIds.length; i++) {
            if (i === 0) {
              qb.where('WasteProducer', restrictedWasteProducerIds[i])
            } else {
              qb.orWhere('WasteProducer', restrictedWasteProducerIds[i])
            }
          }
          return qb
        })
      }
    }

    return sapQuery
  }
}
