import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Brackets, Repository } from 'typeorm'
import { WasteInquiry } from '../../entities/waste-inquiry.entity.js'
import { DEFAULT_LIMIT } from '../../../typesense/param-builders/search-params.builder.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewWasteInquiryIndexPaginationQuery } from './query/view-waste-inquiry-index.pagination-query.js'
import { ViewWasteInquiryIndexDbQueryKey } from './query/view-waste-inquiry-index.db-query-key.js'

@Injectable()
export class ViewWasteInquiryIndexRepository {
  constructor (
    @InjectRepository(WasteInquiry)
    private readonly wasteInquiryRepository: Repository<WasteInquiry>,
    private readonly authContext: AuthContext
  ) {}

  async getDraftWasteInquiriesByUserUuid (
    userUuid: string,
    pagination?: ViewWasteInquiryIndexPaginationQuery
  ): Promise<WasteInquiry[]> {
    const query = this.wasteInquiryRepository.createQueryBuilder('wasteInquiry')
      .where('wasteInquiry.submittedOn IS NULL')
      .andWhere('wasteInquiry.createdByUserUuid = :userUuid', { userUuid })
      .leftJoinAndSelect('wasteInquiry.createdByUser', 'createdByUser')
      .orderBy('wasteInquiry.firstCollectionDate', 'DESC', 'NULLS LAST')
      .addOrderBy('wasteInquiry.uuid', 'DESC')
      .limit(pagination?.limit ?? DEFAULT_LIMIT)

    if (pagination?.key != null && pagination.key instanceof ViewWasteInquiryIndexDbQueryKey) {
      const { firstCollectionDate, uuid } = pagination.key

      if (firstCollectionDate === null) {
        query.andWhere('wasteInquiry.firstCollectionDate IS NULL AND wasteInquiry.uuid < :uuid', { uuid })
      } else {
        query.andWhere(
          new Brackets((qb) => {
            qb.where('(wasteInquiry.firstCollectionDate, wasteInquiry.uuid) < (:firstCollectionDate, :uuid)', { firstCollectionDate, uuid })
              .orWhere('wasteInquiry.firstCollectionDate IS NULL')
          })
        )
      }
    }

    if (this.authContext.getSelectedCustomerId() != null) {
      query.andWhere(
        new Brackets((qb) => {
          qb.where('wasteInquiry.customerId IS NULL')
            .orWhere('wasteInquiry.customerId = :selectedCustomerId', {
              selectedCustomerId: this.authContext.getSelectedCustomerId()
            })
        })
      )
    }

    const wasteInquiries = await query.getMany()

    return wasteInquiries
  }
}
