import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { SinonStubbedInstance, createStubInstance, assert } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewWasteInquiryIndexUseCase } from '../view-waste-inquiry-index.use-case.js'
import { ViewWasteInquiryIndexValidator } from '../view-waste-inquiry-index.validator.js'
import { WasteInquiryStatus } from '../../../enums/waste-inquiry-status.enum.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetWasteInquiryIndexResponse } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { ViewWasteInquiryIndexRepository } from '../view-waste-inquiry-index.repository.js'
import { UserWasteProducerAuthService } from '../../../../auth/services/user-waste-producer-auth.service.js'
import { ViewWasteInquiryIndexQueryBuilder } from './view-waste-inquiry-index-query.builder.js'
import { ViewWasteInquiryIndexFilterQueryBuilder } from './view-waste-inquiry-index.filter.builder.js'

describe('View waste inquiry index use-case unit test', () => {
  let useCase: ViewWasteInquiryIndexUseCase

  let validator: SinonStubbedInstance<ViewWasteInquiryIndexValidator>
  let authContext: SinonStubbedInstance<AuthContext>
  let repository: SinonStubbedInstance<ViewWasteInquiryIndexRepository>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>
  let sapGetWasteInquiryIndex: SinonStubbedInstance<SapGetWasteInquiryIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    validator = createStubInstance(ViewWasteInquiryIndexValidator)
    authContext = createStubInstance(AuthContext)
    repository = createStubInstance(ViewWasteInquiryIndexRepository)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)
    sapGetWasteInquiryIndex = createStubInstance(SapGetWasteInquiryIndexUseCase)

    useCase = new ViewWasteInquiryIndexUseCase(
      validator,
      authContext,
      repository,
      userWasteProducerAuthService,
      sapGetWasteInquiryIndex
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    validator.validate.resolves()
    authContext.getAuthOrFail.returns({
      zitadelSub: randomUUID(),
      uuid: randomUUID(),
      impersonateUserUuid: null,
      azureEntraId: randomUUID(),
      azureEntraUpn: randomUUID(),
      selectedCustomerId: null
    })
    repository.getDraftWasteInquiriesByUserUuid.resolves([])
    userWasteProducerAuthService.getRestrictedWasteProducerIds.resolves(undefined)
    sapGetWasteInquiryIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>().build()
    )
  }

  describe('Source database', () => {
    it('Retrieves result from database when database source given', async () => {
      const query = new ViewWasteInquiryIndexQueryBuilder()
        .withFilter(
          new ViewWasteInquiryIndexFilterQueryBuilder()
            .addStatus(WasteInquiryStatus.DRAFT)
            .build()
        )
        .build()

      await useCase.execute(query)

      assert.calledOnce(repository.getDraftWasteInquiriesByUserUuid)
    })
  })

  describe('Source SAP', () => {
    it('Retrieves result from SAP when SAP source given', async () => {
      const query = new ViewWasteInquiryIndexQueryBuilder()
        .withFilter(
          new ViewWasteInquiryIndexFilterQueryBuilder()
            .addStatus(WasteInquiryStatus.NEW)
            .build()
        )
        .build()

      await useCase.execute(query)

      assert.calledOnce(sapGetWasteInquiryIndex.execute)
    })
  })
})
