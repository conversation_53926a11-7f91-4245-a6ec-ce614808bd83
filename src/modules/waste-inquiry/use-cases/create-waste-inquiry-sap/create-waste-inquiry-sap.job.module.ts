import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { WasteInquiry } from '../../../waste-inquiry/entities/waste-inquiry.entity.js'
import { SapModule } from '../../../sap/sap.module.js'
import { CustomerModule } from '../../../customer/customer.module.js'
import { FileModule } from '../../../files/file.module.js'
import { CreateWasteInquirySapJobHandler } from './create-waste-inquiry-sap.job-handler.js'
import { CreateWasteInquirySapUseCase } from './create-waste-inquiry-sap.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      WasteInquiry
    ]),
    SapModule,
    CustomerModule,
    FileModule
  ],
  providers: [
    CreateWasteInquirySapJobHandler,
    CreateWasteInquirySapUseCase
  ]
})
export class CreateWasteInquirySapJobModule {}
