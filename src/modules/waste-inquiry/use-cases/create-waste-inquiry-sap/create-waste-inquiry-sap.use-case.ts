import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { WasteInquiry } from '../../../waste-inquiry/entities/waste-inquiry.entity.js'
import { SapCreateWasteInquiryUseCase } from '../../../sap/use-cases/create-waste-inquiry/create-waste-inquiry.use-case.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapUploadAndCreateDocumentUseCase } from '../../../sap/use-cases/upload-and-create-document/upload-and-create-document.use-case.js'
import { FileLinkService } from '../../../files/services/file-link.service.js'
import { EntityPart } from '../../../files/enums/entity-part.enum.js'
import { FileLink } from '../../../files/entities/file-link.entity.js'
import { CreateWasteInquirySapMapper } from './create-waste-inquiry-sap.mapper.js'

@Injectable()
export class CreateWasteInquirySapUseCase {
  constructor (
    @InjectRepository(WasteInquiry)
    private readonly wasteInquiryRepository: Repository<WasteInquiry>,
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly fileLinkService: FileLinkService,
    private readonly sapCreatePickUpRequest: SapCreateWasteInquiryUseCase,
    private readonly sapCreateAndUploadDocument: SapUploadAndCreateDocumentUseCase
  ) {}

  async execute (wasteInquiryUuid: string): Promise<string> {
    const wasteInquiry = await this.wasteInquiryRepository.findOneOrFail({
      where: {
        uuid: wasteInquiryUuid
      },
      relations: {
        createdByUser: true
      }
    })

    await this.loadFiles(wasteInquiry)

    assert(wasteInquiry.customerId !== null)

    const customerDefaultSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        wasteInquiry.customerId
      )

    const wasteInquirySapCommand = CreateWasteInquirySapMapper.mapSubmittedWasteInquiryToSapCommand(
      wasteInquiry,
      customerDefaultSalesOrganisationId
    )

    const sapResponse = await this.sapCreatePickUpRequest.execute(
      wasteInquirySapCommand
    )

    assert(wasteInquiry.additionalFiles !== undefined)
    assert(wasteInquiry.sdsFiles !== undefined)
    assert(wasteInquiry.analysisReportFiles !== undefined)

    const allFiles: FileLink[] = [
      ...wasteInquiry.additionalFiles,
      ...wasteInquiry.sdsFiles,
      ...wasteInquiry.analysisReportFiles
    ]

    await this.createAndUploadFiles(
      sapResponse.InquiryNumber,
      allFiles
    )

    return sapResponse.InquiryNumber
  }

  async createAndUploadFiles (
    inquiryNumber: string,
    fileLinks: FileLink[]
  ): Promise<void> {
    await Promise.all(
      fileLinks.map(async (fileLink) => {
        assert(fileLink.file !== undefined)
        await this.sapCreateAndUploadDocument.execute(
          inquiryNumber,
          fileLink.file,
          fileLink.entityPart as EntityPart
        )
      })
    )
  }

  async loadFiles (wasteInquiry: WasteInquiry): Promise<void> {
    const entityParts = [EntityPart.SDS, EntityPart.ANALYSIS_REPORT, EntityPart.ADDITIONAL]

    const fileLinks = await this.fileLinkService.loadFileLinksGroupedByEntityParts(
      wasteInquiry.uuid,
      WasteInquiry.name,
      entityParts
    )

    wasteInquiry.sdsFiles = fileLinks.filter(
      fileLink => fileLink.entityPart === EntityPart.SDS
    )
    wasteInquiry.analysisReportFiles = fileLinks.filter(
      fileLink => fileLink.entityPart === EntityPart.ANALYSIS_REPORT
    )
    wasteInquiry.additionalFiles = fileLinks.filter(
      fileLink => fileLink.entityPart === EntityPart.ADDITIONAL
    )
  }
}
