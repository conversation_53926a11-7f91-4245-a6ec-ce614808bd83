import { randomUUID } from 'crypto'
import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'
import { SapGetWasteInquiryIndexUseCase } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { WasteInquiry } from '../../entities/waste-inquiry.entity.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetWasteInquiryIndexResponse } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { WasteInquiryNotFoundError } from '../../errors/waste-inquiry-not-found-error.js'
import { WasteInquiryEntityBuilder } from '../../tests/waste-inquiry-entity.builder.js'
import { MapUniformWasteInquiryDetailSapService } from '../../services/map-uniform-waste-inquiry-detail-sap.service.js'
import { UniformFullWasteInquiry } from '../../types/uniform-waste-inquiry-detail.type.js'
import { CopyWasteInquirySapResponse } from './copy-waste-inquiry-sap.response.js'
import { WasteInquirySapCopiedEvent } from './waste-inquiry-sap-copied.event.js'
import { CopyWasteInquirySapValidator } from './copy-waste-inquiry-sap.validator.js'

@Injectable()
export class CopyWasteInquirySapUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    private readonly getWasteInquirySapUseCase: SapGetWasteInquiryIndexUseCase,
    @InjectRepository(WasteInquiry)
    private readonly repository: Repository<WasteInquiry>,
    private readonly validator: CopyWasteInquirySapValidator
  ) {}

  async execute (
    inquiryNumber: string
  ): Promise<CopyWasteInquirySapResponse> {
    const wasteInquiries = await this.getSapWasteInquiries(inquiryNumber)

    if (wasteInquiries.length === 0) {
      throw new WasteInquiryNotFoundError({ inquiryNumber })
    }

    await this.validator.validate(wasteInquiries[0])

    const uniformWasteInquiry = MapUniformWasteInquiryDetailSapService
      .mapResultToUniformWasteInquiry(wasteInquiries[0])

    const entity = this.mapUniformWasteInquiryToEntity(uniformWasteInquiry)

    await transaction(this.dataSource, async () => {
      await this.repository.insert(entity)
      await this.eventEmitter.emitOne(new WasteInquirySapCopiedEvent(inquiryNumber, entity.uuid))
    })

    return new CopyWasteInquirySapResponse(entity)
  }

  private async getSapWasteInquiries (
    inquiryNumber: string
  ): Promise<SapGetWasteInquiryIndexResponse[]> {
    const sapQuery = new SapQuery<SapGetWasteInquiryIndexResponse>()
      .where('InquiryNumber', inquiryNumber)
      .setTop(1)

    const responses = await this.getWasteInquirySapUseCase
      .execute(sapQuery)

    return responses.items
  }

  private mapUniformWasteInquiryToEntity (
    uniformWasteInquiry: UniformFullWasteInquiry
  ): WasteInquiry {
    return new WasteInquiryEntityBuilder()
      .withUuid(randomUUID())
      .createdByUserUuid(this.authContext.getUserUuidOrFail())
      .withCustomerId(uniformWasteInquiry.customer?.id ?? null)
      .withWasteProducerId(uniformWasteInquiry.wasteProducer?.id ?? null)
      .withPickUpAddressId(uniformWasteInquiry.pickUpAddress?.id ?? null)
      .withWasteStreamName(uniformWasteInquiry.wasteStreamName)
      .withWasteStreamDescription(uniformWasteInquiry.wasteStreamDescription)
      .withEwcLevel1(uniformWasteInquiry.ewcLevel1)
      .withEwcLevel2(uniformWasteInquiry.ewcLevel2)
      .withEwcLevel3(uniformWasteInquiry.ewcLevel3)
      .withStateOfMatter(uniformWasteInquiry.stateOfMatter)
      .withPackagingType(uniformWasteInquiry.packagingType)
      .withFlashpoint(uniformWasteInquiry.flashpoint)
      .withPh(uniformWasteInquiry.ph)
      .withSpecificGravity(uniformWasteInquiry.specificGravity)
      .withStableTemperatureType(uniformWasteInquiry.stableTemperatureType)
      .withMinStableTemperature(uniformWasteInquiry.minStableTemperature)
      .withMaxStableTemperature(uniformWasteInquiry.maxStableTemperature)
      .withAverageStableTemperature(uniformWasteInquiry.averageStableTemperature)
      .withSdsFiles(uniformWasteInquiry.sdsFiles)
      .withNoSds(uniformWasteInquiry.noSds)
      .withAnalysisReportFiles(uniformWasteInquiry.analysisReportFiles)
      .withNoAnalysisReport(uniformWasteInquiry.noAnalysisReport)
      .withComposition(uniformWasteInquiry.composition)
      .withIsSampleAvailable(uniformWasteInquiry.isSampleAvailable)
      .withSelectedLegislationOptions(uniformWasteInquiry.selectedLegislationOptions)
      .withSvhcExtra(uniformWasteInquiry.svhcExtra)
      .withLegislationRemarks(uniformWasteInquiry.legislationRemarks)
      .withSelectedPropertyOptions(uniformWasteInquiry.selectedPropertyOptions)
      .withPropertyRemarks(uniformWasteInquiry.propertyRemarks)
      .withExpectedYearlyVolumeAmount(uniformWasteInquiry.expectedYearlyVolumeAmount)
      .withExpectedYearlyVolumeUnit(uniformWasteInquiry.expectedYearlyVolumeUnit)
      .withExpectedPerCollectionQuantity(uniformWasteInquiry.expectedPerCollectionQuantity)
      .withExpectedPerCollectionUnit(uniformWasteInquiry.expectedPerCollectionUnit)
      .withDischargeFrequency(uniformWasteInquiry.dischargeFrequency)
      .withFirstCollectionDate(uniformWasteInquiry.firstCollectionDate)
      .withExpectedEndDate(uniformWasteInquiry.expectedEndDate)
      .withCollectionRemarks(uniformWasteInquiry.collectionRemarks)
      .withIsTransportByIndaver(uniformWasteInquiry.isTransportByIndaver)
      .withIsLoadingByIndaver(uniformWasteInquiry.isLoadingByIndaver)
      .withIsRegulatedTransport(uniformWasteInquiry.isRegulatedTransport)
      .withUnNumbers(uniformWasteInquiry.unNumbers)
      .withPackaging(uniformWasteInquiry.packaging)
      .withTransportType(uniformWasteInquiry.transportType)
      .withTransportIn(uniformWasteInquiry.transportIn)
      .withContainerLoadingType(uniformWasteInquiry.containerLoadingType)
      .withLoadingType(uniformWasteInquiry.loadingType)
      .withLoadingMethod(uniformWasteInquiry.loadingMethod)
      .withStoredIn(uniformWasteInquiry.storedIn)
      .withTransportVolumeAmount(uniformWasteInquiry.transportVolumeAmount)
      .withTransportVolumeUnit(uniformWasteInquiry.transportVolumeUnit)
      .withIsTankOwnedByCustomer(uniformWasteInquiry.isTankOwnedByCustomer)
      .withCollectionRequirements(uniformWasteInquiry.collectionRequirements)
      .withRemarks(uniformWasteInquiry.remarks)
      .withSendCopyToContacts(uniformWasteInquiry.sendCopyToContacts)
      .withSubmittedOn(uniformWasteInquiry.submittedOn)
      .withAdditionalFiles(uniformWasteInquiry.additionalFiles)
      .withInquiryNumber(uniformWasteInquiry.inquiryNumber)
      .build()
  }
}
