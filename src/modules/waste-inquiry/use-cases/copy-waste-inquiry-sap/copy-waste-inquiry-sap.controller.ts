import { <PERSON>, <PERSON><PERSON>, <PERSON> } from '@nestjs/common'
import { ApiCreatedResponse, ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { CopyWasteInquirySapUseCase } from './copy-waste-inquiry-sap.use-case.js'
import { CopyWasteInquirySapResponse } from './copy-waste-inquiry-sap.response.js'

@ApiTags('Waste inquiry')
@Controller('waste-inquiries/sap/:inquiryNumber/copy')
@ApiOAuth2([])
export class CopyWasteInquirySapController {
  constructor (
    private readonly useCase: CopyWasteInquirySapUseCase
  ) {}

  @Post()
  @GlobalCustomerRequired()
  @Permissions(Permission.WASTE_INQUIRY_MANAGE)
  @ApiCreatedResponse({ type: CopyWasteInquirySapResponse })
  async copyWasteInquiry (
    @Param('inquiryNumber') inquiryNumber: string
  ): Promise<CopyWasteInquirySapResponse> {
    return await this.useCase.execute(inquiryNumber)
  }
}
