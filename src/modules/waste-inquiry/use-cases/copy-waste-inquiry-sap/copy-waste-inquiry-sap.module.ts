import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { SapModule } from '../../../sap/sap.module.js'
import { WasteInquiry } from '../../entities/waste-inquiry.entity.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { CopyWasteInquirySapController } from './copy-waste-inquiry-sap.controller.js'
import { CopyWasteInquirySapUseCase } from './copy-waste-inquiry-sap.use-case.js'
import { CopyWasteInquirySapValidator } from './copy-waste-inquiry-sap.validator.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([WasteInquiry]),
    SapModule,
    DomainEventEmitterModule
  ],
  controllers: [
    CopyWasteInquirySapController
  ],
  providers: [
    CopyWasteInquirySapUseCase,
    CopyWasteInquirySapValidator
  ]
})
export class CopyWasteInquirySapModule {}
