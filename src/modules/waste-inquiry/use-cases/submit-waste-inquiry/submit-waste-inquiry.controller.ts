import { Controller, HttpCode, Post } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { ApiConflictErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { WasteInquiryAlreadySubmitted } from '../../errors/waste-inquiry-already-submitted.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { SubmitWasteInquiryUseCase } from './submit-waste-inquiry.use-case.js'
import { SubmitWasteInquiryResponse } from './submit-waste-inquiry.response.js'

@ApiTags('Waste inquiry')
@Controller('waste-inquiries/:uuid/submit')
@ApiOAuth2([])
export class SubmitWasteInquiryController {
  constructor (
    private readonly useCase: SubmitWasteInquiryUseCase
  ) {}

  @Post()
  @GlobalCustomerRequired()
  @HttpCode(200)
  @Permissions(Permission.WASTE_INQUIRY_MANAGE)
  @ApiOkResponse({
    description: 'Waste inquiry submitted',
    type: SubmitWasteInquiryResponse
  })
  @ApiNotFoundErrorResponse(NotFoundError)
  @ApiConflictErrorResponse(WasteInquiryAlreadySubmitted)
  async submitWasteInquiry (
    @UuidParam('uuid') wasteInquiryUuid: string
  ): Promise<SubmitWasteInquiryResponse> {
    return await this.useCase.execute(wasteInquiryUuid)
  }
}
