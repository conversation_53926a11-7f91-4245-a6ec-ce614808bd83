import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'
import { WasteInquiry } from '../../entities/waste-inquiry.entity.js'
import { CreateWasteInquirySapUseCase } from '../create-waste-inquiry-sap/create-waste-inquiry-sap.use-case.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { SubmitWasteInquiryValidator } from './submit-waste-inquiry.validator.js'
import { SubmitWasteInquiryResponse } from './submit-waste-inquiry.response.js'
import { WasteInquirySubmittedEvent } from './waste-inquiry-submitted.event.js'

@Injectable()
export class SubmitWasteInquiryUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly validator: SubmitWasteInquiryValidator,
    private readonly createWasteInquirySapUseCase: CreateWasteInquirySapUseCase,
    @InjectRepository(WasteInquiry)
    private readonly wasteInquiryRepository: Repository<WasteInquiry>,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (wasteInquiryUuid: string): Promise<SubmitWasteInquiryResponse> {
    const wasteInquiry = await this.validator.validate(wasteInquiryUuid)

    const inquiryNumber = await this.createWasteInquirySapUseCase.execute(wasteInquiryUuid)

    wasteInquiry.submittedOn = new Date()
    wasteInquiry.inquiryNumber = inquiryNumber

    await transaction(this.dataSource, async () => {
      await this.wasteInquiryRepository.save(wasteInquiry)

      await this.eventEmitter.emitOne(new WasteInquirySubmittedEvent(wasteInquiryUuid))
    })

    return new SubmitWasteInquiryResponse(wasteInquiry)
  }
}
