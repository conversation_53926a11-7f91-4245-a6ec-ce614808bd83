import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { WasteInquiry } from '../../../entities/waste-inquiry.entity.js'
import { WasteInquiryEntityBuilder } from '../../../tests/waste-inquiry-entity.builder.js'
import { ViewWasteInquiryUseCase } from '../view-waste-inquiry.use-case.js'
import { FileLinkService } from '../../../../files/services/file-link.service.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { SapGetCustomerIndexUseCase } from '../../../../sap/use-cases/get-customer-index/get-customer-index.use-case.js'
import { GetCustomerIndexResponseBuilder } from '../../../../sap/use-cases/get-customer-index/tests/get-customer-index.response.builder.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetCustomerIndexResponse } from '../../../../sap/use-cases/get-customer-index/get-customer-index.response.js'
import { SapGetWasteProducerIndexResponse } from '../../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.response.js'
import { SapGetPickUpAddressIndexResponse } from '../../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.response.js'
import { GetWasteProducerIndexResponseBuilder } from '../../../../sap/use-cases/get-waste-producer-index/tests/get-waste-producer-index.response.builder.js'
import { GetPickUpAddressIndexResponseBuilder } from '../../../../sap/use-cases/get-pick-up-address-index/tests/get-pick-up-address-index.response.builder.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapGetWasteProducerIndexUseCase } from '../../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.use-case.js'
import { SapGetPickUpAddressIndexUseCase } from '../../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.use-case.js'

describe('View waste inquiry use-case unit test', () => {
  let useCase: ViewWasteInquiryUseCase

  let userUuid: string
  let wasteInquiry: WasteInquiry

  let authContext: SinonStubbedInstance<AuthContext>
  let wasteInquiryRepository: SinonStubbedInstance<Repository<WasteInquiry>>
  let fileLinkService: SinonStubbedInstance<FileLinkService>
  let sapGetCustomerIndex: SinonStubbedInstance<SapGetCustomerIndexUseCase>
  let salesOrganisationUseCase: SinonStubbedInstance<GetCustomerDefaultSalesOrganisationIdUseCase>
  let sapGetWasteProducerIndex: SinonStubbedInstance<SapGetWasteProducerIndexUseCase>
  let sapGetPickUpAddressIndex: SinonStubbedInstance<SapGetPickUpAddressIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()
    wasteInquiry = new WasteInquiryEntityBuilder().build()

    authContext = createStubInstance(AuthContext)
    wasteInquiryRepository = createStubInstance<Repository<WasteInquiry>>(Repository<WasteInquiry>)
    fileLinkService = createStubInstance(FileLinkService)
    sapGetCustomerIndex = createStubInstance(SapGetCustomerIndexUseCase)
    salesOrganisationUseCase = createStubInstance(GetCustomerDefaultSalesOrganisationIdUseCase)
    sapGetWasteProducerIndex = createStubInstance(SapGetWasteProducerIndexUseCase)
    sapGetPickUpAddressIndex = createStubInstance(SapGetPickUpAddressIndexUseCase)

    useCase = new ViewWasteInquiryUseCase(
      authContext,
      wasteInquiryRepository,
      fileLinkService,
      sapGetCustomerIndex,
      salesOrganisationUseCase,
      sapGetWasteProducerIndex,
      sapGetPickUpAddressIndex
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getUserUuidOrFail.resolves(userUuid)
    authContext.getSelectedCustomerId.resolves(undefined)
    wasteInquiryRepository.findOneOrFail.resolves(wasteInquiry)
    fileLinkService.loadFileLinks.resolves([])
    sapGetCustomerIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetCustomerIndexResponse>()
        .addItem(
          new GetCustomerIndexResponseBuilder().build()
        )
        .build()
    )
    salesOrganisationUseCase.getOrganisationIdOrFail.resolves(randomUUID())
    sapGetWasteProducerIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteProducerIndexResponse>()
        .addItem(
          new GetWasteProducerIndexResponseBuilder().build()
        )
        .build()
    )
    sapGetPickUpAddressIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetPickUpAddressIndexResponse>()
        .addItem(
          new GetPickUpAddressIndexResponseBuilder().build()
        )
        .build()
    )
  }

  it('Calls all methods once', async () => {
    await useCase.execute(wasteInquiry.uuid)

    assert.calledOnce(wasteInquiryRepository.findOneOrFail)
    assert.calledThrice(fileLinkService.loadFileLinks)
  })

  it('Calls sapGetCustomerIndex.execute when customerId not null', async () => {
    wasteInquiry.customerId = randomUUID()
    wasteInquiryRepository.findOneOrFail.resolves(wasteInquiry)

    await useCase.execute(wasteInquiry.uuid)

    assert.calledOnce(sapGetCustomerIndex.execute)
  })

  it('Calls sapGetWasteProducerIndex.execute when wasteProducerId not null', async () => {
    wasteInquiry.wasteProducerId = randomUUID()
    wasteInquiryRepository.findOneOrFail.resolves(wasteInquiry)

    await useCase.execute(wasteInquiry.uuid)

    assert.calledOnce(sapGetWasteProducerIndex.execute)
  })

  it('Calls sapGetPickUpAddressIndex.execute when pickUpAddressId not null', async () => {
    wasteInquiry.pickUpAddressId = randomUUID()
    wasteInquiryRepository.findOneOrFail.resolves(wasteInquiry)

    await useCase.execute(wasteInquiry.uuid)

    assert.calledOnce(sapGetPickUpAddressIndex.execute)
  })
})
