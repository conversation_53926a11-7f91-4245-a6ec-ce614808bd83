import { Injectable } from '@nestjs/common'
import { IsNull, Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { WasteInquiry } from '../../entities/waste-inquiry.entity.js'
import { FileLinkService } from '../../../files/services/file-link.service.js'
import { EntityPart } from '../../../files/enums/entity-part.enum.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetCustomerIndexResponse } from '../../../sap/use-cases/get-customer-index/get-customer-index.response.js'
import { CustomerNotFoundError } from '../../../customer/errors/customer-not-found.error.js'
import { MapCustomerSapService } from '../../../customer/services/map-customer-sap.service.js'
import { SapGetCustomerIndexUseCase } from '../../../sap/use-cases/get-customer-index/get-customer-index.use-case.js'
import { WasteProducerNotFoundError } from '../../../waste-producer/errors/waste-producer-not-found.error.js'
import { MapWasteProducerSapService } from '../../../waste-producer/services/map-waste-producer.service.js'
import { SapGetWasteProducerIndexResponse } from '../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.response.js'
import { SapGetWasteProducerIndexUseCase } from '../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.use-case.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapGetPickUpAddressIndexResponse } from '../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.response.js'
import { SapGetPickUpAddressIndexUseCase } from '../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.use-case.js'
import { MapPickUpAddressSapService } from '../../../pick-up-address/services/map-pick-up-address.service.js'
import { PickUpAddressNotFoundError } from '../../../pick-up-address/errors/pick-up-address-not-found.error.js'
import { ViewWasteInquiryResponse } from './view-waste-inquiry.response.js'

@Injectable()
export class ViewWasteInquiryUseCase {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(WasteInquiry)
    private readonly wasteInquiryRepository: Repository<WasteInquiry>,
    private readonly fileLinkService: FileLinkService,
    private readonly sapGetCustomerIndex: SapGetCustomerIndexUseCase,
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly sapGetWasteProducerIndex: SapGetWasteProducerIndexUseCase,
    private readonly sapGetPickUpAddressIndex: SapGetPickUpAddressIndexUseCase
  ) {}

  async execute (wasteInquiryUuid: string): Promise<ViewWasteInquiryResponse> {
    const authUserUuid = this.authContext.getUserUuidOrFail()

    const wasteInquiry = await this.wasteInquiryRepository.findOneOrFail({
      where: [
        {
          uuid: wasteInquiryUuid,
          createdByUserUuid: authUserUuid,
          customerId: this.authContext.getSelectedCustomerId() ?? undefined
        },
        {
          uuid: wasteInquiryUuid,
          createdByUserUuid: authUserUuid,
          customerId: IsNull()
        }
      ]
    })

    await Promise.all([
      this.loadFiles(wasteInquiry),
      this.loadCustomer(wasteInquiry),
      this.loadWasteProducer(wasteInquiry),
      this.loadPickUpAddress(wasteInquiry)
    ])

    return new ViewWasteInquiryResponse(wasteInquiry)
  }

  async loadFiles (wasteInquiry: WasteInquiry): Promise<void> {
    const [sdsFiles, analysisReportFiles, additionalFiles] = await Promise.all([
      this.fileLinkService.loadFileLinks(
        wasteInquiry.uuid,
        WasteInquiry.name,
        EntityPart.SDS
      ),
      this.fileLinkService.loadFileLinks(
        wasteInquiry.uuid,
        WasteInquiry.name,
        EntityPart.ANALYSIS_REPORT
      ),
      this.fileLinkService.loadFileLinks(
        wasteInquiry.uuid,
        WasteInquiry.name,
        EntityPart.ADDITIONAL
      )
    ])

    wasteInquiry.sdsFiles = sdsFiles
    wasteInquiry.analysisReportFiles = analysisReportFiles
    wasteInquiry.additionalFiles = additionalFiles

    this.fileLinkService.loadTempUrlOnFileLinks(wasteInquiry.sdsFiles)
    this.fileLinkService.loadTempUrlOnFileLinks(wasteInquiry.analysisReportFiles)
    this.fileLinkService.loadTempUrlOnFileLinks(wasteInquiry.additionalFiles)
  }

  async loadCustomer (wasteInquiry: WasteInquiry): Promise<void> {
    wasteInquiry.customer = null

    if (wasteInquiry.customerId === null) return

    const sapQuery = new SapQuery<SapGetCustomerIndexResponse>()
      .where('Customer', wasteInquiry.customerId)
    const sapResult = await this.sapGetCustomerIndex.execute(sapQuery)

    if (sapResult.items.length === 0) throw new CustomerNotFoundError(wasteInquiry.customerId)

    wasteInquiry.customer = MapCustomerSapService.mapResultToCustomer(sapResult.items[0])
  }

  async loadWasteProducer (wasteInquiry: WasteInquiry): Promise<void> {
    wasteInquiry.wasteProducer = null

    if (wasteInquiry.customerId === null || wasteInquiry.wasteProducerId === null) return

    const customerSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        wasteInquiry.customerId
      )

    const sapQuery = new SapQuery<SapGetWasteProducerIndexResponse>()
      .where('Customer', wasteInquiry.customerId)
      .andWhere('SalesOrganization', customerSalesOrganisationId)
      .andWhere('WasteProducer', wasteInquiry.wasteProducerId)
    const sapResult = await this.sapGetWasteProducerIndex.execute(sapQuery)

    if (sapResult.items.length === 0) {
      throw new WasteProducerNotFoundError(wasteInquiry.wasteProducerId)
    }

    wasteInquiry.wasteProducer = MapWasteProducerSapService.mapResultToWasteProducer(
      sapResult.items[0]
    )
  }

  async loadPickUpAddress (wasteInquiry: WasteInquiry): Promise<void> {
    wasteInquiry.pickUpAddress = null

    if (wasteInquiry.customerId === null || wasteInquiry.pickUpAddressId === null) return

    const customerSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        wasteInquiry.customerId
      )

    const sapQuery = new SapQuery<SapGetPickUpAddressIndexResponse>()
      .where('Customer', wasteInquiry.customerId)
      .andWhere('SalesOrganization', customerSalesOrganisationId)
      .andWhere('PickUpAddress', wasteInquiry.pickUpAddressId)

    const sapResult = await this.sapGetPickUpAddressIndex.execute(sapQuery)

    if (sapResult.items.length === 0) {
      throw new PickUpAddressNotFoundError(wasteInquiry.pickUpAddressId)
    }

    wasteInquiry.pickUpAddress = MapPickUpAddressSapService.mapResultPickUpAddress(
      sapResult.items[0]
    )
  }
}
