import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { WasteInquiry } from '../../entities/waste-inquiry.entity.js'
import { CustomerModule } from '../../../customer/customer.module.js'
import { WasteProducerModule } from '../../../waste-producer/waste-producer.module.js'
import { PickUpAddressModule } from '../../../pick-up-address/pick-up-address.module.js'
import { FileModule } from '../../../files/file.module.js'
import { SapModule } from '../../../sap/sap.module.js'
import { ViewWasteInquiryController } from './view-waste-inquiry.controller.js'
import { ViewWasteInquiryUseCase } from './view-waste-inquiry.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([WasteInquiry]),
    CustomerModule,
    WasteProducerModule,
    PickUpAddressModule,
    FileModule,
    SapModule
  ],
  controllers: [
    ViewWasteInquiryController
  ],
  providers: [
    ViewWasteInquiryUseCase
  ]
})
export class ViewWasteInquiryModule {}
