import { <PERSON>, Controller, Patch } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiBadRequestErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { CustomerNotAccessibleError } from '../../../customer/errors/customer-not-accessible.error.js'
import { MissingEwcLevelsError } from '../../../ewc-code/errors/missing-ewc-levels.error.js'
import { EwcCodeNotFound } from '../../../ewc-code/errors/ewc-code-not-found.error.js'
import { InvalidStableTemperatureError } from '../../errors/invalid-stable-temperature.error.js'
import { FileNotAccessibleError } from '../../../files/errors/file-not-accessible.error.js'
import { NoSdsFilesExpected } from '../../errors/no-sds-files-expected.error.js'
import { NoAnalysisReportFilesExpected } from '../../errors/no-analysis-report-files-expected.error.js'
import { NoOptionExpectedWhenNoneSelected } from '../../errors/no-option-expected-when-none-selected.error.js'
import { NoSvhcExtraExpected } from '../../errors/no-svhc-extra-expected.error.js'
import { PickUpRequestNotFoundError } from '../../../pick-up-request/errors/pick-up-request-sap-not-found.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { UpdateWasteInquiryUseCase } from './update-waste-inquiry.use-case.js'
import { UpdateWasteInquiryCommand } from './update-waste-inquiry.command.js'
import { UpdateWasteInquiryResponse } from './update-waste-inquiry.response.js'

@ApiTags('Waste inquiry')
@Controller('waste-inquiries/:uuid')
@ApiOAuth2([])
export class UpdateWasteInquiryController {
  constructor (
    private readonly useCase: UpdateWasteInquiryUseCase
  ) {}

  @Patch()
  @GlobalCustomerRequired()
  @Permissions(Permission.WASTE_INQUIRY_MANAGE)
  @ApiOkResponse({ type: UpdateWasteInquiryResponse })
  @ApiNotFoundErrorResponse(PickUpRequestNotFoundError)
  @ApiBadRequestErrorResponse(
    CustomerNotAccessibleError,
    MissingEwcLevelsError,
    EwcCodeNotFound,
    InvalidStableTemperatureError,
    FileNotAccessibleError,
    NoSdsFilesExpected,
    NoAnalysisReportFilesExpected,
    NoOptionExpectedWhenNoneSelected,
    NoSvhcExtraExpected
  )
  async createWasteInquiry (
    @UuidParam('uuid') wasteInquiryUuid: string,
    @Body() command: UpdateWasteInquiryCommand
  ): Promise<UpdateWasteInquiryResponse> {
    return await this.useCase.execute(wasteInquiryUuid, command)
  }
}
