import { Injectable } from '@nestjs/common'
import { SapGetWasteInquiryIndexResponse } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { AddDocumentToWasteInquirySapCommand } from './add-document-to-waste-inquiry-sap.command.js'
import { AddDocumentToWasteInquirySapValidator } from './add-document-to-waste-inquiry-sap.validator.js'

@Injectable()
export class AddDocumentToWasteInquirySapUseCase {
  constructor (
    private readonly sapGetWasteInquiryIndex: SapGetWasteInquiryIndexUseCase,
    private readonly validator: AddDocumentToWasteInquirySapValidator
  ) {}

  async execute (
    inquiryNumber: string,
    command: AddDocumentToWasteInquirySapCommand
  ): Promise<void> {
    await this.validator.validateCommand(command)

    const sapQuery = this.getSapQuery(inquiryNumber)
    const sapResponse = await this.sapGetWasteInquiryIndex.execute(sapQuery)

    if (sapResponse.items.length === 0) {
      throw new NotFoundError()
    }

    await this.validator.validateAccessibility(sapResponse.items[0])

    // TODO: Implement the logic to add a document to the waste inquiry in SAP
  }

  private getSapQuery (
    inquiryNumber: string
  ): SapQuery<SapGetWasteInquiryIndexResponse> {
    const sapQuery = new SapQuery<SapGetWasteInquiryIndexResponse>({})
      .addSelect(
        'InquiryNumber'
      )
      .where('InquiryNumber', inquiryNumber)
      .setTop(1)

    return sapQuery
  }
}
