import { Body, Controller, Param, Post } from '@nestjs/common'
import { ApiNoContentResponse, ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { AddDocumentToWasteInquirySapCommand } from './add-document-to-waste-inquiry-sap.command.js'
import { AddDocumentToWasteInquirySapUseCase } from './add-document-to-waste-inquiry-sap.use-case.js'

@ApiTags('Waste inquiry')
@ApiOAuth2([])
@Controller('waste-inquiries/sap/:inquiryNumber/add-documents')
export class AddDocumentToWasteInquirySapController {
  constructor (
    private readonly useCase: AddDocumentToWasteInquirySapUseCase
  ) { }

  @Post()
  @GlobalCustomerRequired()
  @Permissions(Permission.WASTE_INQUIRY_MANAGE)
  @ApiNoContentResponse()
  @ApiNotFoundErrorResponse()
  public async viewWasteInquirySap (
    @Param('inquiryNumber') inquiryNumber: string,
    @Body() command: AddDocumentToWasteInquirySapCommand
  ): Promise<void> {
    return await this.useCase.execute(inquiryNumber, command)
  }
}
