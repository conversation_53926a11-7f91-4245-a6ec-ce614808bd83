import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { In, Repository } from 'typeorm'
import { AuthContext } from '../../../auth/auth.context.js'
import { File } from '../../../files/entities/file.entity.js'
import { FileNotAccessibleError } from '../../../files/errors/file-not-accessible.error.js'
import { SapGetWasteInquiryIndexResponse } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { WasteInquiryNotFoundError } from '../../errors/waste-inquiry-not-found-error.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { AddDocumentToWasteInquirySapCommand } from './add-document-to-waste-inquiry-sap.command.js'

@Injectable()
export class AddDocumentToWasteInquirySapValidator {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(File)
    private readonly fileRepository: Repository<File>,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  async validateCommand (
    command: AddDocumentToWasteInquirySapCommand
  ): Promise<void> {
    const authUserUuid = this.authContext.getUserUuidOrFail()
    const fileUuids: string[] = []

    if (command.sdsFiles !== undefined) {
      fileUuids.push(...command.sdsFiles.map(file => file.fileUuid))
    }
    if (command.analysisReportFiles !== undefined) {
      fileUuids.push(...command.analysisReportFiles.map(file => file.fileUuid))
    }
    if (command.additionalFiles !== undefined) {
      fileUuids.push(...command.additionalFiles.map(file => file.fileUuid))
    }

    const files = await this.fileRepository.findBy({
      uuid: In(fileUuids),
      userUuid: authUserUuid
    })
    const foundFileUuids = files.map(file => file.uuid)

    command.sdsFiles?.forEach((sdsFile, index) => {
      if (!foundFileUuids.includes(sdsFile.fileUuid)) {
        throw new FileNotAccessibleError({ pointer: `$.sdsFiles[${index}]` })
      }
    })
    command.analysisReportFiles?.forEach((analysisFile, index) => {
      if (!foundFileUuids.includes(analysisFile.fileUuid)) {
        throw new FileNotAccessibleError({ pointer: `$.analysisReportFiles[${index}]` })
      }
    })
    command.additionalFiles?.forEach((additionalFile, index) => {
      if (!foundFileUuids.includes(additionalFile.fileUuid)) {
        throw new FileNotAccessibleError({ pointer: `$.additionalFiles[${index}]` })
      }
    })
  }

  async validateAccessibility (
    wasteInquiry: SapGetWasteInquiryIndexResponse
  ): Promise<void> {
    if (wasteInquiry.Customer === undefined || wasteInquiry.Customer === '') {
      return
    }

    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== wasteInquiry.Customer) {
      throw new WasteInquiryNotFoundError({ inquiryNumber: wasteInquiry.InquiryNumber ?? 'InquiryNumber not given' })
    }
    if (wasteInquiry.WasteProducer === undefined || wasteInquiry.WasteProducer === '') {
      return
    }

    const userId = this.authContext.getAzureEntraUpnOrFail()
    const canUserAccessWasteProducer = await this.userWasteProducerAuthService
      .canUserAccessWasteProducer(
        userId,
        wasteInquiry.Customer,
        wasteInquiry.WasteProducer
      )
    if (!canUserAccessWasteProducer) {
      throw new WasteInquiryNotFoundError({ inquiryNumber: wasteInquiry.InquiryNumber ?? 'InquiryNumber not given' })
    }
  }
}
