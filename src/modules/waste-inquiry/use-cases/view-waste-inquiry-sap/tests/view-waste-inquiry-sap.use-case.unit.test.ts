import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { SinonStubbedInstance, createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { GetWasteInquiryIndexResponseBuilder } from '../../../../sap/use-cases/get-waste-inquiry-index/tests/get-waste-inquiry-index.response.builder.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { ViewWasteInquirySapUseCase } from '../view-waste-inquiry-sap.use-case.js'
import { NotFoundError } from '../../../../exceptions/generic/not-found.error.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetWasteInquiryIndexResponse } from '../../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { ViewWasteInquirySapValidator } from '../view-waste-inquiry-sap.validator.js'

describe('View waste inquiry SAP use-case unit test', () => {
  let useCase: ViewWasteInquirySapUseCase

  let sapGetWasteInquiryIndex: SinonStubbedInstance<SapGetWasteInquiryIndexUseCase>
  let validator: SinonStubbedInstance<ViewWasteInquirySapValidator>

  before(() => {
    TestBench.setupUnitTest()

    sapGetWasteInquiryIndex = createStubInstance(SapGetWasteInquiryIndexUseCase)
    validator = createStubInstance(ViewWasteInquirySapValidator)

    useCase = new ViewWasteInquirySapUseCase(
      sapGetWasteInquiryIndex,
      validator
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()

    mockMethods()
  })

  function mockMethods () {
    sapGetWasteInquiryIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>().build()
    )
    validator.validate.resolves()
  }

  it('Throws an error when waste inquiry not found', async () => {
    await expect(useCase.execute(randomUUID())).rejects.toThrow(NotFoundError)
  })

  it('Returns waste inquiry when found', async () => {
    const inquiryNumber = randomUUID()
    sapGetWasteInquiryIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(
          new GetWasteInquiryIndexResponseBuilder().withInquiryNumber(inquiryNumber).build()
        )
        .build()
    )

    const response = await useCase.execute(inquiryNumber)

    expect(response).toBeDefined()
    expect(response.inquiryNumber).toBe(inquiryNumber)
  })

  it('Calls all methods once', async () => {
    const inquiryNumber = randomUUID()
    sapGetWasteInquiryIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(
          new GetWasteInquiryIndexResponseBuilder().withInquiryNumber(inquiryNumber).build()
        )
        .build()
    )

    await useCase.execute(inquiryNumber)

    expect(sapGetWasteInquiryIndex.execute.calledOnce).toBeTruthy()
    expect(validator.validate.calledOnce).toBeTruthy()
  })
})
