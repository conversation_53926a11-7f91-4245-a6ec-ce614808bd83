import { <PERSON>, Get, Param } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewWasteInquirySapUseCase } from './view-waste-inquiry-sap.use-case.js'
import { ViewWasteInquirySapResponse } from './view-waste-inquiry-sap.response.js'

@ApiTags('Waste inquiry')
@ApiOAuth2([])
@Controller('waste-inquiries/sap/:inquiryNumber')
export class ViewWasteInquirySapController {
  constructor (
    private readonly useCase: ViewWasteInquirySapUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.WASTE_INQUIRY_READ, Permission.WASTE_INQUIRY_MANAGE)
  @ApiOkResponse({ type: ViewWasteInquirySapResponse })
  @ApiNotFoundErrorResponse()
  public async viewWasteInquirySap (
    @Param('inquiryNumber') inquiryNumber: string
  ): Promise<ViewWasteInquirySapResponse> {
    return await this.useCase.execute(inquiryNumber)
  }
}
