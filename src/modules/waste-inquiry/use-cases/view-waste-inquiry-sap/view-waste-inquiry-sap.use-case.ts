import { Injectable } from '@nestjs/common'
import { SapGetWasteInquiryIndexResponse } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { SapGetWasteInquiryIndexUseCase } from '../../../sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { MapUniformWasteInquiryDetailSapService } from '../../services/map-uniform-waste-inquiry-detail-sap.service.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { ViewWasteInquirySapResponse } from './view-waste-inquiry-sap.response.js'
import { ViewWasteInquirySapValidator } from './view-waste-inquiry-sap.validator.js'

@Injectable()
export class ViewWasteInquirySapUseCase {
  constructor (
    private readonly sapGetWasteInquiryIndex: SapGetWasteInquiryIndexUseCase,
    private readonly validator: ViewWasteInquirySapValidator
  ) {}

  async execute (inquiryNumber: string): Promise<ViewWasteInquirySapResponse> {
    const sapQuery = this.getSapQuery(inquiryNumber)
    const sapResponse = await this.sapGetWasteInquiryIndex.execute(sapQuery)

    if (sapResponse.items.length === 0) {
      throw new NotFoundError()
    }

    await this.validator.validate(sapResponse.items[0])

    const uniformWasteInquiry = MapUniformWasteInquiryDetailSapService
      .mapResultToUniformWasteInquiry(
        sapResponse.items[0]
      )

    return new ViewWasteInquirySapResponse(uniformWasteInquiry)
  }

  private getSapQuery (
    inquiryNumber: string
  ): SapQuery<SapGetWasteInquiryIndexResponse> {
    const sapQuery = new SapQuery<SapGetWasteInquiryIndexResponse>({})
      .addSelect([
        'NewRequestStatus',
        'InquiryNumber',
        'Contract',
        'ContractItem',
        'WasteName',
        'WasteMaterialDescription',
        'EwcCustomerLevel1',
        'EwcCustomerLevel2',
        'EwcCustomerLevel3',
        'StateOfMatter',
        'WasteType',
        'Flashpoint',
        'Ph',
        'Gravity',
        'Sds',
        'AnalysisReport',
        'Sample',
        'TypeOfSvhc',
        'PersistentSubstance',
        'CommentSubjectLegislation',
        'CommentSubjectHazard',
        'QuantityYear',
        'QuantityYearUom',
        'CollectionQuantity',
        'CollectionUom',
        'FrequencyDischarge',
        'CollectionDate',
        'ExpectedEnd',
        'InformationDelivery',
        'TransportArranged',
        'LoadingByIndaver',
        'TransportRegulated',
        'TransportType',
        'LoadingType',
        'LoadingMethod',
        'StoredIn',
        'Volume',
        'VolumeUom',
        'TankOwnedCustomer',
        'RequiredForCollection',
        'RequestDescription',
        'CreatedAt',
        'CreatedBy',
        'Customer',
        'CustomerName',
        'CustomerAddress',
        'WasteProducer',
        'WasteProducerName',
        'WasteProducerAddress',
        'PickUpAddress',
        'PickUpAddressName',
        'PickUpAddressAddress',
        'Temperature',
        'MinTemperature',
        'MaxTemperature'
      ])
      .where('InquiryNumber', inquiryNumber)
      .setTop(1)

    return sapQuery
  }
}
