import { Module } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { ViewWasteInquirySapController } from './view-waste-inquiry-sap.controller.js'
import { ViewWasteInquirySapUseCase } from './view-waste-inquiry-sap.use-case.js'
import { ViewWasteInquirySapValidator } from './view-waste-inquiry-sap.validator.js'

@Module({
  imports: [
    SapModule
  ],
  controllers: [
    ViewWasteInquirySapController
  ],
  providers: [
    ViewWasteInquirySapUseCase,
    ViewWasteInquirySapValidator
  ]
})
export class ViewWasteInquirySapModule {}
