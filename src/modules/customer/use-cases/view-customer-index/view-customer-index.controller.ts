import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { ViewCustomerIndexQuery } from './query/view-customer-index.query.js'
import { ViewCustomerIndexUseCase } from './view-customer-index.use-case.js'
import { ViewCustomerIndexResponse } from './view-customer-index.response.js'

@ApiTags('Customer')
@ApiOAuth2([])
@Controller('customers')
export class ViewCustomerIndexController {
  constructor (
    private readonly viewCustomerIndexUseCase: ViewCustomerIndexUseCase
  ) { }

  @Get()
  @ApiOkResponse({ type: ViewCustomerIndexResponse })
  public async viewCustomerIndex (
    @Query() query: ViewCustomerIndexQuery
  ): Promise<ViewCustomerIndexResponse> {
    return await this.viewCustomerIndexUseCase.execute(query)
  }
}
