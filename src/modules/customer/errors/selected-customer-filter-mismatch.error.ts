import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { ErrorSource } from '../../exceptions/types/json-api-error.type.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class SelectedCustomerFilterMismatchError extends BadRequestApiError {
  @ApiErrorCode('selected_customer_filter_mismatch')
  readonly code = 'selected_customer_filter_mismatch'

  readonly meta: never

  constructor (source?: ErrorSource) {
    const detail = translateCurrent('error.customer.selected_customer_filter_mismatch')
    super(detail, source)
  }
}
