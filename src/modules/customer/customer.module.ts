import { Module } from '@nestjs/common'
import { ViewCustomerIndexModule } from './use-cases/view-customer-index/view-customer-index.module.js'
import { ViewSuggestedCustomersModule } from './use-cases/view-suggested-customers/view-suggested-customers.module.js'
import { GetCustomerDefaultSalesOrganisationIdModule } from './use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.module.js'
import { ViewCustomerCountryModule } from './use-cases/view-customer-country/view-customer-country.module.js'

@Module({
  imports: [
    ViewCustomerIndexModule,
    ViewSuggestedCustomersModule,
    GetCustomerDefaultSalesOrganisationIdModule,
    ViewCustomerCountryModule
  ],
  exports: [
    GetCustomerDefaultSalesOrganisationIdModule,
    ViewCustomerCountryModule
  ]
})
export class CustomerModule { }
