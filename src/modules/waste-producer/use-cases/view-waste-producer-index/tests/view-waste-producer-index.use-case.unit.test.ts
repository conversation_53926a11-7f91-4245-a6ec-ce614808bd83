import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { createStubInstance, SinonStubbedInstance, assert } from 'sinon'
import { randCity, randFullName, randStreetAddress, randZipCode } from '@ngneat/falso'
import { expect } from 'expect'
import { ViewWasteProducerIndexUseCase } from '../view-waste-producer-index.use-case.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { WasteProducer } from '../../../types/waste-producer.type.js'
import { MapWasteProducerSapService } from '../../../services/map-waste-producer.service.js'
import { SapGetWasteProducerIndexUseCase } from '../../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.use-case.js'
import { ViewWasteProducerIndexValidator } from '../view-waste-producer-index.validator.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetWasteProducerIndexResponse } from '../../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.response.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../../auth/services/user-waste-producer-auth.service.js'
import { ViewWasteProducerIndexQueryBuilder } from './view-waste-producer-index.query.builder.js'

describe('View waste producer index use-case unit test', () => {
  let useCase: ViewWasteProducerIndexUseCase

  let validator: SinonStubbedInstance<ViewWasteProducerIndexValidator>
  let authContext: SinonStubbedInstance<AuthContext>
  let salesOrganisationUseCase: SinonStubbedInstance<GetCustomerDefaultSalesOrganisationIdUseCase>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>
  let sapGetWasteProducerIndex: SinonStubbedInstance<SapGetWasteProducerIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    validator = createStubInstance(ViewWasteProducerIndexValidator)
    authContext = createStubInstance(AuthContext)
    salesOrganisationUseCase = createStubInstance(GetCustomerDefaultSalesOrganisationIdUseCase)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)
    sapGetWasteProducerIndex = createStubInstance(SapGetWasteProducerIndexUseCase)

    useCase = new ViewWasteProducerIndexUseCase(
      validator,
      authContext,
      salesOrganisationUseCase,
      userWasteProducerAuthService,
      sapGetWasteProducerIndex
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    authContext.getAzureEntraUpnOrFail.returns(randomUUID())
    validator.execute.returns()
    salesOrganisationUseCase.getOrganisationIdOrFail.resolves(randomUUID())
    userWasteProducerAuthService.getRestrictedWasteProducerIds.resolves(undefined)
    sapGetWasteProducerIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteProducerIndexResponse>().build()
    )
  }

  it ('Call all methods once', async () => {
    const command = new ViewWasteProducerIndexQueryBuilder().build()

    await useCase.execute(command)

    assert.calledOnce(validator.execute)
    assert.calledOnce(salesOrganisationUseCase.getOrganisationIdOrFail)
    assert.calledOnce(sapGetWasteProducerIndex.execute)
  })

  it('Returns parsed result from SAP', async () => {
    const wasteProducers: WasteProducer[] = [{
      id: randomUUID(),
      name: randFullName(),
      address: {
        countryCode: null,
        postalCode: randZipCode(),
        locality: randCity(),
        addressLine1: randStreetAddress(),
        addressLine2: null,
        coordinates: null
      }
    }]

    const sapResults = MapWasteProducerSapService.mapWasteProducersToResults(wasteProducers)

    sapGetWasteProducerIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteProducerIndexResponse>()
        .addItem(sapResults)
        .build()
    )

    const result = await useCase.execute({
      filter: {
        customerId: randomUUID()
      }
    })

    expect(result.items).toStrictEqual(expect.arrayContaining(wasteProducers))
  })
})
