import { Controller, Get, Query } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewWasteProducerIndexQuery } from './query/view-waste-producer-index.query.js'
import { ViewWasteProducerIndexResponse } from './view-waste-producer-index.response.js'
import { ViewWasteProducerIndexUseCase } from './view-waste-producer-index.use-case.js'

@ApiTags('Waste producer')
@ApiOAuth2([])
@Controller('waste-producers')
export class ViewWasteProducerIndexController {
  constructor (
    private readonly viewWasteProducerIndexUseCase: ViewWasteProducerIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(
    Permission.WASTE_INQUIRY_MANAGE,
    Permission.WASTE_INQUIRY_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ
  )
  @ApiOkResponse({ type: ViewWasteProducerIndexResponse })
  public async viewWasteProducerIndex (
    @Query() query: ViewWasteProducerIndexQuery
  ): Promise<ViewWasteProducerIndexResponse> {
    return this.viewWasteProducerIndexUseCase.execute(query)
  }
}
