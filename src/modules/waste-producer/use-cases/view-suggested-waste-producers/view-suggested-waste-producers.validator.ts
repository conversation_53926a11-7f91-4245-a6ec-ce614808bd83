import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { SelectedCustomerFilterMismatchError } from '../../../customer/errors/selected-customer-filter-mismatch.error.js'
import { ViewSuggestedWasteProducersQuery } from './view-suggested-waste-producers.query.js'

@Injectable()
export class ViewSuggestedWasteProducersValidator {
  constructor (
    private readonly authContext: AuthContext
  ) {}

  validate (
    query: ViewSuggestedWasteProducersQuery
  ): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== query.filter.customerId) {
      throw new SelectedCustomerFilterMismatchError({ pointer: '$.customerId' })
    }
  }
}
