import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewSuggestedWasteProducersUseCase } from './view-suggested-waste-producers.use-case.js'
import { ViewSuggestedWasteProducersResponse } from './view-suggested-waste-producers.response.js'
import { ViewSuggestedWasteProducersQuery } from './view-suggested-waste-producers.query.js'

@ApiTags('Waste producer')
@ApiOAuth2([])
@Controller('suggested-waste-producers')
export class ViewSuggestedWasteProducersController {
  constructor (
    private readonly useCase: ViewSuggestedWasteProducersUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(
    Permission.WASTE_INQUIRY_MANAGE,
    Permission.WASTE_INQUIRY_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ
  )
  @ApiOkResponse({ type: ViewSuggestedWasteProducersResponse })
  public async viewWasteProducersIndex (
    @Query() query: ViewSuggestedWasteProducersQuery
  ): Promise<ViewSuggestedWasteProducersResponse> {
    return await this.useCase.execute(query)
  }
}
