import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { SinonStubbedInstance, createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewSuggestedWasteProducersValidator } from '../view-suggested-waste-producers.validator.js'
import { ViewSuggestedWasteProducersQuery } from '../view-suggested-waste-producers.query.js'
import { RequestType } from '../../../../../utils/enums/request-type.enum.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { SelectedCustomerFilterMismatchError } from '../../../../customer/errors/selected-customer-filter-mismatch.error.js'
import { ViewSuggestedWasteProducersQueryBuilder } from './view-suggested-waste-producers.query.builder.js'
import { ViewSuggestedWasteProducersFilterQueryBuilder } from './view-suggested-waste-producers.filter.builder.js'

describe('View suggested waste producers validator unit test', () => {
  let validator: ViewSuggestedWasteProducersValidator

  let query: ViewSuggestedWasteProducersQuery

  let authContext: SinonStubbedInstance<AuthContext>

  before(() => {
    TestBench.setupUnitTest()

    query = new ViewSuggestedWasteProducersQueryBuilder()
      .withFilter(
        new ViewSuggestedWasteProducersFilterQueryBuilder()
          .withCustomerId(randomUUID())
          .withRequestType(RequestType.WASTE)
          .build()
      )
      .build()

    authContext = createStubInstance(AuthContext)

    validator = new ViewSuggestedWasteProducersValidator(
      authContext
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getSelectedCustomerId.returns(query.filter.customerId)
  }

  it('doesn\'t throw an error when validation passes', () => {
    expect(() => validator.validate(query)).not.toThrow()
  })

  it('throws an error when customer is different from selected customer', () => {
    authContext.getSelectedCustomerId.returns(randomUUID())

    expect(() => validator.validate(query)).toThrow(SelectedCustomerFilterMismatchError)
  })
})
