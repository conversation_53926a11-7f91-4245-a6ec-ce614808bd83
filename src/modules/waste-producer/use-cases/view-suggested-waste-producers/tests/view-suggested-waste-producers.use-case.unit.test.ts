import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { createStubInstance, SinonStubbedInstance, assert } from 'sinon'
import { randFullName } from '@ngneat/falso'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { RequestType } from '../../../../../utils/enums/request-type.enum.js'
import { SUGGESTED_WASTE_PRODUCERS_AMOUNT } from '../../../../../utils/constants/suggested-entities-amount.constant.js'
import { ViewSuggestedWasteProducersUseCase } from '../view-suggested-waste-producers.use-case.js'
import { ViewSuggestedWasteProducersRepository } from '../view-suggested-waste-producers.repository.js'
import { SapGetWasteProducerIndexResponse } from '../../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.response.js'
import { ViewSuggestedWasteProducersValidator } from '../view-suggested-waste-producers.validator.js'
import { WasteProducer } from '../../../types/waste-producer.type.js'
import { MapWasteProducerSapService } from '../../../services/map-waste-producer.service.js'
import { SapGetWasteProducerIndexUseCase } from '../../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.use-case.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { UserWasteProducerAuthService } from '../../../../auth/services/user-waste-producer-auth.service.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapQuery } from '../../../../sap/query/sap-query.js'
import { FilterOperator } from '../../../../sap/enums/odata-filter-operator.enum.js'
import { ViewSuggestedWasteProducersFilterQueryBuilder } from './view-suggested-waste-producers.filter.builder.js'
import { ViewSuggestedWasteProducersQueryBuilder } from './view-suggested-waste-producers.query.builder.js'

describe('View suggested waste producers use-case unit test', () => {
  let useCase: ViewSuggestedWasteProducersUseCase

  let customerDefaultSalesOrganisationId: string

  let validator: SinonStubbedInstance<ViewSuggestedWasteProducersValidator>
  let authContext: SinonStubbedInstance<AuthContext>
  let salesOrganisationUseCase: SinonStubbedInstance<GetCustomerDefaultSalesOrganisationIdUseCase>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>
  let repository: SinonStubbedInstance<ViewSuggestedWasteProducersRepository>
  let sapGetWasteProducerIndex: SinonStubbedInstance<SapGetWasteProducerIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    customerDefaultSalesOrganisationId = randomUUID()

    validator = createStubInstance(ViewSuggestedWasteProducersValidator)
    authContext = createStubInstance(AuthContext)
    salesOrganisationUseCase = createStubInstance(GetCustomerDefaultSalesOrganisationIdUseCase)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)
    repository = createStubInstance(ViewSuggestedWasteProducersRepository)
    sapGetWasteProducerIndex = createStubInstance(SapGetWasteProducerIndexUseCase)

    useCase = new ViewSuggestedWasteProducersUseCase(
      validator,
      authContext,
      salesOrganisationUseCase,
      userWasteProducerAuthService,
      repository,
      sapGetWasteProducerIndex
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    validator.validate.returns()
    authContext.getUserUuidOrFail.resolves(randomUUID())
    salesOrganisationUseCase.getOrganisationIdOrFail.resolves(customerDefaultSalesOrganisationId)
    userWasteProducerAuthService.getRestrictedWasteProducerIds.resolves(undefined)
    repository.findRecentWasteInquiryWasteProducerIds.resolves([])
    repository.findRecentPickUpRequestWasteProducerIds.resolves([])
    sapGetWasteProducerIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteProducerIndexResponse>().build()
    )
  }

  describe ('Request type "waste"', () => {
    const query = new ViewSuggestedWasteProducersQueryBuilder()
      .withFilter(
        new ViewSuggestedWasteProducersFilterQueryBuilder()
          .withCustomerId(randomUUID())
          .withRequestType(RequestType.WASTE)
          .build()
      )
      .build()

    it('Retrieves first X-amount of waste producers when no past waste inquiry', async () => {
      await useCase.execute(query)

      const sapQuery = new SapQuery<SapGetWasteProducerIndexResponse>()
        .where('Customer', query.filter.customerId)
        .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)
        .addOrderBy('WasteProducerName', 'asc')
        .setTop(SUGGESTED_WASTE_PRODUCERS_AMOUNT)

      assert.calledOnceWithExactly(sapGetWasteProducerIndex.execute, sapQuery)
    })

    it('Retrieves waste producers of past waste inquiries completed with waste producers alphabetically until X-amount', async () => {
      const wasteProducers: WasteProducer[] = [{
        id: randomUUID(),
        name: randFullName(),
        address: null
      }, {
        id: randomUUID(),
        name: randFullName(),
        address: null
      }]
      const wasteProducerIds = wasteProducers.map(wasteProducer => wasteProducer.id)

      const sapResults = MapWasteProducerSapService.mapWasteProducersToResults(wasteProducers)

      repository.findRecentWasteInquiryWasteProducerIds.resolves(wasteProducerIds)
      sapGetWasteProducerIndex.execute.resolves(
        new SapPaginatedResponseBuilder<SapGetWasteProducerIndexResponse>()
          .addItem(sapResults)
          .build()
      )

      await useCase.execute(query)

      const sapQuerySpecific = new SapQuery<SapGetWasteProducerIndexResponse>()
        .where('Customer', query.filter.customerId)
        .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)
        .andWhere((qb) => {
          qb.where('WasteProducer', wasteProducerIds[0])
          for (let i = 1; i < wasteProducerIds.length; i++) {
            qb.orWhere('WasteProducer', wasteProducerIds[i])
          }
          return qb
        })

      const sapQueryAll = new SapQuery<SapGetWasteProducerIndexResponse>()
        .where('Customer', query.filter.customerId)
        .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)
        .andWhere((qb) => {
          qb.where('WasteProducer', wasteProducerIds[0], FilterOperator.NOT_EQUAL)
          for (let i = 1; i < wasteProducerIds.length; i++) {
            qb.andWhere('WasteProducer', wasteProducerIds[i], FilterOperator.NOT_EQUAL)
          }
          return qb
        })
        .addOrderBy('WasteProducerName', 'asc')
        .setTop(SUGGESTED_WASTE_PRODUCERS_AMOUNT - wasteProducerIds.length)

      assert.calledTwice(sapGetWasteProducerIndex.execute)
      assert.calledWithExactly(
        sapGetWasteProducerIndex.execute.getCall(0),
        sapQuerySpecific
      )
      assert.calledWithExactly(
        sapGetWasteProducerIndex.execute.getCall(1),
        sapQueryAll
      )
    })
  })

  describe ('Request type "pick-up"', () => {
    const query = new ViewSuggestedWasteProducersQueryBuilder()
      .withFilter(
        new ViewSuggestedWasteProducersFilterQueryBuilder()
          .withCustomerId(randomUUID())
          .withRequestType(RequestType.PICK_UP)
          .build()
      )
      .build()

    it('Retrieves first X-amount of waste producers when no past pick-up requests', async () => {
      await useCase.execute(query)

      const sapQuery = new SapQuery<SapGetWasteProducerIndexResponse>()
        .where('Customer', query.filter.customerId)
        .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)
        .addOrderBy('WasteProducerName', 'asc')
        .setTop(SUGGESTED_WASTE_PRODUCERS_AMOUNT)

      assert.calledOnceWithExactly(sapGetWasteProducerIndex.execute, sapQuery)
    })

    it('Retrieves waste producers of past pick-up requests completed with waste producers alphabetically until X-amount', async () => {
      const wasteProducers: WasteProducer[] = [{
        id: randomUUID(),
        name: randFullName(),
        address: null
      }, {
        id: randomUUID(),
        name: randFullName(),
        address: null
      }]
      const wasteProducerIds = wasteProducers.map(wasteProducer => wasteProducer.id)

      const sapResults = MapWasteProducerSapService.mapWasteProducersToResults(wasteProducers)

      repository.findRecentPickUpRequestWasteProducerIds.resolves(wasteProducerIds)
      sapGetWasteProducerIndex.execute.resolves(
        new SapPaginatedResponseBuilder<SapGetWasteProducerIndexResponse>()
          .addItem(sapResults)
          .build()
      )

      await useCase.execute(query)

      const sapQuerySpecific = new SapQuery<SapGetWasteProducerIndexResponse>()
        .where('Customer', query.filter.customerId)
        .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)
        .andWhere((qb) => {
          qb.where('WasteProducer', wasteProducerIds[0])
          for (let i = 1; i < wasteProducerIds.length; i++) {
            qb.orWhere('WasteProducer', wasteProducerIds[i])
          }
          return qb
        })

      const sapQueryAll = new SapQuery<SapGetWasteProducerIndexResponse>()
        .where('Customer', query.filter.customerId)
        .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)
        .andWhere((qb) => {
          qb.where('WasteProducer', wasteProducerIds[0], FilterOperator.NOT_EQUAL)
          for (let i = 1; i < wasteProducerIds.length; i++) {
            qb.andWhere('WasteProducer', wasteProducerIds[i], FilterOperator.NOT_EQUAL)
          }
          return qb
        })
        .addOrderBy('WasteProducerName', 'asc')
        .setTop(SUGGESTED_WASTE_PRODUCERS_AMOUNT - wasteProducerIds.length)

      assert.calledTwice(sapGetWasteProducerIndex.execute)
      assert.calledWithExactly(
        sapGetWasteProducerIndex.execute.getCall(0),
        sapQuerySpecific
      )
      assert.calledWithExactly(
        sapGetWasteProducerIndex.execute.getCall(1),
        sapQueryAll
      )
    })
  })
})
