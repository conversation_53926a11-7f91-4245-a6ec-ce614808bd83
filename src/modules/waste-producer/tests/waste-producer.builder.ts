import { Address } from '../../../utils/address/types/address.type.js'
import { WasteProducer } from '../types/waste-producer.type.js'

export class WasteProducerBuilder {
  private readonly wasteProducer: WasteProducer

  constructor () {
    this.wasteProducer = {
      id: 'waste-producer-id',
      name: 'Waste Producer name',
      address: null
    }
  }

  withId (id: string): this {
    this.wasteProducer.id = id
    return this
  }

  withName (name: string): this {
    this.wasteProducer.name = name
    return this
  }

  withAddress (address: Address): this {
    this.wasteProducer.address = address ?? null
    return this
  }

  build (): WasteProducer {
    return this.wasteProducer
  }
}
