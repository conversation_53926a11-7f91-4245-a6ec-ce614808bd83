import { Injectable } from '@nestjs/common'
import { ContractLineValidatorService } from '../../validators/contract-line-validator.service.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { GenerateContractLinesPdfCommand } from './generate-contract-lines-pdf.command.js'

@Injectable()
export class GenerateContractLinesPdfValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly contractLineValidatorService: ContractLineValidatorService,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  async validate (
    command: GenerateContractLinesPdfCommand
  ): Promise<void> {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    let restrictedWasteProducerIds: string[] | undefined

    if (selectedCustomerId != null) {
      restrictedWasteProducerIds = await this.userWasteProducerAuthService
        .getRestrictedWasteProducerIds(
          this.authContext.getAzureEntraUpnOrFail(),
          selectedCustomerId
        )
    }

    if (command.selection !== undefined) {
      await this.contractLineValidatorService.checkAccessContractLines(
        'pick-up',
        command.selection.map(contractLine => ({
          contractNumber: contractLine.contractNumber,
          contractItem: contractLine.contractItem
        })),
        {
          customerId: selectedCustomerId ?? undefined,
          wasteProducerIds: restrictedWasteProducerIds
        }
      )
    }
  }
}
