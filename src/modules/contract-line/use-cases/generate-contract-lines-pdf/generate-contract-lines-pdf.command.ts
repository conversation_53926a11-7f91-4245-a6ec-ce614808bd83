import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { ArrayMinSize, ArrayUnique, IsArray, IsNotEmpty, IsObject, IsString, ValidateNested } from 'class-validator'
import { IsUndefinable } from '@wisemen/validators'
import { ViewContractLineIndexFilterQuery } from '../view-contract-line-index/query/view-contract-line-index.filter-query.js'

export class ContractLinesSelection {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  contractNumber: string

  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  contractItem: string
}

export class ContractLineQuerySelection {
  @ApiProperty({ type: ViewContractLineIndexFilterQuery })
  @Type(() => ViewContractLineIndexFilterQuery)
  @IsObject()
  @ValidateNested()
  @IsNotEmpty()
  filter: ViewContractLineIndexFilterQuery

  @ApiProperty({ type: ContractLinesSelection, isArray: true, minItems: 1, required: false })
  @IsUndefinable()
  @Type(() => ContractLinesSelection)
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique({ each: true })
  @ValidateNested({ each: true })
  excludeSelection?: ContractLinesSelection[]
}

export class GenerateContractLinesPdfCommand {
  @ApiProperty({ type: ContractLinesSelection, isArray: true, minItems: 1, required: false })
  @IsUndefinable()
  @Type(() => ContractLinesSelection)
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  selection?: ContractLinesSelection[]

  @ApiProperty({ type: ContractLineQuerySelection, required: false })
  @Type(() => ContractLineQuerySelection)
  @IsUndefinable()
  @IsObject()
  @ValidateNested()
  @IsNotEmpty()
  querySelection?: ContractLineQuerySelection
}
