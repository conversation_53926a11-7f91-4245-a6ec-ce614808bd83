import { Injectable } from '@nestjs/common'
import { SapGenerateContractLinesPdfUseCase } from '../../../sap/use-cases/generate-contract-lines-pdf/generate-contract-lines-pdf.use-case.js'
import { EmptyContractLinesSelectionError } from '../../errors/empty-contract-lines-selection.error.js'
import { ViewContractLineIndexUseCase } from '../view-contract-line-index/view-contract-line-index.use-case.js'
import { ViewContractLineIndexQueryKey } from '../view-contract-line-index/query/view-contract-line-index.query-key.js'
import { SapGenerateContractLinesPdfSelectionCommand } from '../../../sap/use-cases/generate-contract-lines-pdf/generate-contract-lines-pdf.command.js'
import { ContractLineQuerySelection, ContractLinesSelection, GenerateContractLinesPdfCommand } from './generate-contract-lines-pdf.command.js'
import { GenerateContractLinesPdfValidator } from './generate-contract-lines-pdf.validator.js'
import { GenerateContractLinesPdfResponse } from './generate-contract-lines-pdf.response.js'

@Injectable()
export class GenerateContractLinesPdfUseCase {
  constructor (
    private readonly validator: GenerateContractLinesPdfValidator,
    private readonly viewContractLineIndexUseCase: ViewContractLineIndexUseCase,
    private readonly generateContractLinesPdf: SapGenerateContractLinesPdfUseCase
  ) {}

  async execute (
    command: GenerateContractLinesPdfCommand
  ): Promise<GenerateContractLinesPdfResponse> {
    await this.validator.validate(command)

    if (command.selection !== undefined) {
      return this.executeWithSelection(command.selection)
    }
    if (command.querySelection !== undefined) {
      return this.executeWithQuerySelection(command.querySelection)
    }

    throw new EmptyContractLinesSelectionError()
  }

  async executeWithSelection (
    selection: ContractLinesSelection[]
  ): Promise<GenerateContractLinesPdfResponse> {
    const sapResponse = await this.generateContractLinesPdf.execute({
      selection: selection.map(line => ({
        VBELN: line.contractNumber,
        POSNR: line.contractItem
      }))
    })

    return new GenerateContractLinesPdfResponse(sapResponse)
  }

  async executeWithQuerySelection (
    querySelection: ContractLineQuerySelection
  ): Promise<GenerateContractLinesPdfResponse> {
    const excludeSet = new Set<string>()
    if (querySelection.excludeSelection !== undefined) {
      for (const selection of querySelection.excludeSelection) {
        excludeSet.add(
          this.createContractLineKey(selection.contractNumber, selection.contractItem)
        )
      }
    }

    const selection: SapGenerateContractLinesPdfSelectionCommand[] = []

    let nextKey: undefined | ViewContractLineIndexQueryKey = undefined
    do {
      const response = await this.viewContractLineIndexUseCase.execute({
        filter: querySelection.filter,
        pagination: {
          key: nextKey
        }
      })

      for (const line of response.items) {
        const key = this.createContractLineKey(line.contractNumber, line.contractItem)
        if (!excludeSet.has(key)) {
          selection.push({
            VBELN: line.contractNumber,
            POSNR: line.contractItem
          })
        }
      }

      nextKey = response.meta.next ?? undefined
    } while (nextKey !== undefined)

    const sapResponse = await this.generateContractLinesPdf.execute({ selection })

    return new GenerateContractLinesPdfResponse(sapResponse)
  }

  private createContractLineKey (
    contractNumber: string,
    contractItem: string
  ): string {
    return `${contractNumber}-${contractItem}`
  }
}
