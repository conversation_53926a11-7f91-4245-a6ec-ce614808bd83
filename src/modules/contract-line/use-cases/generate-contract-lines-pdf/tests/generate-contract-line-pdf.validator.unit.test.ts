import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { GenerateContractLinesPdfValidator } from '../generate-contract-lines-pdf.validator.js'
import { ContractLineValidatorService } from '../../../validators/contract-line-validator.service.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../../auth/services/user-waste-producer-auth.service.js'
import { GenerateContractLinesPdfSelectionCommandBuilder } from './generate-contract-lines-pdf-selection.command.builder.js'

describe('GenerateContractLinesPdfValidator - unit test', () => {
  let validator: GenerateContractLinesPdfValidator

  let authContext: SinonStubbedInstance<AuthContext>
  let contractLineValidatorService: SinonStubbedInstance<ContractLineValidatorService>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>

  before(() => {
    TestBench.setupUnitTest()

    authContext = createStubInstance(AuthContext)
    contractLineValidatorService = createStubInstance(ContractLineValidatorService)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)

    validator = new GenerateContractLinesPdfValidator(
      authContext,
      contractLineValidatorService,
      userWasteProducerAuthService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getSelectedCustomerId.returns(null)
    authContext.getAzureEntraUpnOrFail.returns(randomUUID())
    contractLineValidatorService.checkAccessContractLines.resolves()
    userWasteProducerAuthService.getRestrictedWasteProducerIds.resolves(undefined)
  }

  it('Calls all methods once', async () => {
    const command = new GenerateContractLinesPdfSelectionCommandBuilder().build()

    await validator.validate(command)

    assert.calledOnce(authContext.getSelectedCustomerId)
    assert.calledOnce(contractLineValidatorService.checkAccessContractLines)
  })
})
