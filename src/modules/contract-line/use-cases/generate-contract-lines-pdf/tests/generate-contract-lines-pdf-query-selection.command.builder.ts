import { ViewContractLineIndexFilterQuery } from '../../view-contract-line-index/query/view-contract-line-index.query.js'
import { GenerateContractLinesPdfCommand } from '../generate-contract-lines-pdf.command.js'

export class GenerateContractLinesPdfQuerySelectionCommandBuilder {
  private command: GenerateContractLinesPdfCommand

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.command = new GenerateContractLinesPdfCommand()

    this.command.querySelection = {
      filter: {},
      excludeSelection: undefined
    }

    return this
  }

  withFilter (filter: ViewContractLineIndexFilterQuery): this {
    this.command.querySelection!.filter = filter
    return this
  }

  withExcludeSelection (selection: { contractNumber: string, contractItem: string }[]): this {
    this.command.querySelection!.excludeSelection = selection

    return this
  }

  public build (): GenerateContractLinesPdfCommand {
    const result = this.command

    this.reset()

    return result
  }
}
