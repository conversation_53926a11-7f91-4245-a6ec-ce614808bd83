import { GenerateContractLinesPdfCommand } from '../generate-contract-lines-pdf.command.js'

export class GenerateContractLinesPdfSelectionCommandBuilder {
  private command: GenerateContractLinesPdfCommand

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.command = new GenerateContractLinesPdfCommand()

    this.command.selection = [{
      contractNumber: '0000000001',
      contractItem: '000010'
    }]

    return this
  }

  withSelection (selection: { contractNumber: string, contractItem: string }[]): this {
    this.command.selection = selection
    return this
  }

  public build (): GenerateContractLinesPdfCommand {
    const result = this.command

    this.reset()

    return result
  }
}
