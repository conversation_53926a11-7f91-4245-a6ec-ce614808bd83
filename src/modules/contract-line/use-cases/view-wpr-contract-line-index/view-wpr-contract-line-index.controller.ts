import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewWprContractLineIndexQuery } from './view-wpr-contract-line-index.query.js'
import { ViewWprContractLineIndexResponse } from './view-wpr-contract-line-index.response.js'
import { ViewWprContractLineIndexUseCase } from './view-wpr-contract-line-index.use-case.js'

@ApiTags('Contract line')
@ApiOAuth2([])
@Controller('contract-lines/weekly-planning-requests')
export class ViewWprContractLineIndexController {
  constructor (
    private readonly useCase: ViewWprContractLineIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(
    Permission.WEEKLY_PLANNING_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE
  )
  @ApiOkResponse({ type: ViewWprContractLineIndexResponse })
  public async viewWprContractLineIndex (
    @Query() query: ViewWprContractLineIndexQuery
  ): Promise<ViewWprContractLineIndexResponse> {
    return await this.useCase.execute(query)
  }
}
