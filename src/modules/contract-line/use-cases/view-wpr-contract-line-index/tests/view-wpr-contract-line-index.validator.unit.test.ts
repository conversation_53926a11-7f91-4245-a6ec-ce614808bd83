import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { SinonStubbedInstance, createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { CustomerWasteProducerAuthService } from '../../../../auth/services/customer-waste-producer-auth.service.js'
import { UserWasteProducerAuthService } from '../../../../auth/services/user-waste-producer-auth.service.js'
import { CustomerPickUpAddressAuthService } from '../../../../auth/services/customer-pick-up-address-auth.service.js'
import { WasteProducerNotAccessibleError } from '../../../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { PickUpAddressNotAccessibleError } from '../../../../pick-up-address/errors/pick-up-address-not-accessible.error.js'
import { ViewWprContractLineIndexValidator } from '../view-wpr-contract-line-index.validator.js'
import { ViewWprContractLineIndexQuery } from '../view-wpr-contract-line-index.query.js'
import { ViewWprContractLineIndexQueryBuilder } from './view-wpr-contract-line-index.query.builder.js'

describe('View WPR contract line index validator service unit test', () => {
  let validator: ViewWprContractLineIndexValidator

  let query: ViewWprContractLineIndexQuery

  let authContext: SinonStubbedInstance<AuthContext>
  let customerWasteProducerAuthService: SinonStubbedInstance<CustomerWasteProducerAuthService>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>
  let customerPickUpAddressAuthService: SinonStubbedInstance<CustomerPickUpAddressAuthService>

  before(() => {
    TestBench.setupUnitTest()

    query = new ViewWprContractLineIndexQueryBuilder()
      .withFilter({
        customerId: randomUUID(),
        wasteProducerId: randomUUID(),
        pickUpAddressIds: [randomUUID()]
      })
      .build()

    authContext = createStubInstance(AuthContext)
    customerWasteProducerAuthService = createStubInstance(CustomerWasteProducerAuthService)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)
    customerPickUpAddressAuthService = createStubInstance(CustomerPickUpAddressAuthService)

    validator = new ViewWprContractLineIndexValidator(
      authContext,
      customerWasteProducerAuthService,
      userWasteProducerAuthService,
      customerPickUpAddressAuthService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getAzureEntraUpnOrFail.returns(randomUUID())
    customerWasteProducerAuthService.canCustomerAccessWasteProducer.resolves(true)
    userWasteProducerAuthService.canUserAccessWasteProducer.resolves(true)
    customerPickUpAddressAuthService.canCustomerAccessPickUpAddresses.resolves(true)
  }

  it('doesn\'t throw an error when validation passes', async () => {
    await expect(validator.validate(query))
      .resolves.not.toThrow()
  })

  it('throws an error when waste producer is not accessible by customer', async () => {
    customerWasteProducerAuthService.canCustomerAccessWasteProducer.resolves(false)

    await expect(validator.validate(query))
      .rejects.toThrow(WasteProducerNotAccessibleError)
  })

  it('throws an error when waste producer is not accessible by auth user', async () => {
    userWasteProducerAuthService.canUserAccessWasteProducer.resolves(false)

    await expect(validator.validate(query))
      .rejects.toThrow(WasteProducerNotAccessibleError)
  })

  it('throws an error when pick-up addresses is not accessible by customer', async () => {
    customerPickUpAddressAuthService.canCustomerAccessPickUpAddresses.resolves(false)

    await expect(validator.validate(query))
      .rejects.toThrow(PickUpAddressNotAccessibleError)
  })
})
