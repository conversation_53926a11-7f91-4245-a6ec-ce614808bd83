import { ViewContractLineIndexFilterQuery } from '../query/view-contract-line-index.filter-query.js'
import { ViewContractLineIndexQuery } from '../query/view-contract-line-index.query.js'

export class ViewContractLineIndexQueryBuilder {
  private query: ViewContractLineIndexQuery

  constructor () {
    this.reset()
  }

  reset (): this {
    this.query = new ViewContractLineIndexQuery()

    return this
  }

  withFilter (filter: ViewContractLineIndexFilterQuery): this {
    this.query.filter = filter

    return this
  }

  build (): ViewContractLineIndexQuery {
    const result = this.query

    this.reset()

    return result
  }
}
