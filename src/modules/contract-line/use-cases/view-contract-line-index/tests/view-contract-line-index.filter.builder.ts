import { ViewContractLineIndexFilterQuery } from '../query/view-contract-line-index.filter-query.js'

export class ViewContractLineIndexFilterQueryBuilder {
  private filter: ViewContractLineIndexFilterQuery

  constructor () {
    this.reset()
  }

  reset (): this {
    this.filter = new ViewContractLineIndexFilterQuery()

    return this
  }

  withCustomerId (customerId: string): this {
    this.filter.customerId = customerId

    return this
  }

  withWasteProducerId (wasteProducerId: string): this {
    this.filter.wasteProducerId = wasteProducerId

    return this
  }

  withPickUpAddressIds (pickUpAddressIds: string[]): this {
    this.filter.pickUpAddressIds = pickUpAddressIds

    return this
  }

  build (): ViewContractLineIndexFilterQuery {
    const result = this.filter

    this.reset()

    return result
  }
}
