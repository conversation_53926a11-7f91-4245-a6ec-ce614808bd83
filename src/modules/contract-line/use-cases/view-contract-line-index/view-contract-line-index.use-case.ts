import { Injectable } from '@nestjs/common'
import { SapGetContractLineIndexResponse } from '../../../sap/use-cases/get-contract-line-index/get-contract-line-index.response.js'
import { MapContractLineSapService } from '../../services/map-contract-line.service.js'
import { SapGetContractLineIndexUseCase } from '../../../sap/use-cases/get-contract-line-index/get-contract-line-index.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { FilterOperator } from '../../../sap/enums/odata-filter-operator.enum.js'
import { toBoolean } from '../../../../utils/transformers/to-boolean.js'
import { ViewContractLineIndexResponse } from './view-contract-line-index.response.js'
import { ViewContractLineIndexQuery } from './query/view-contract-line-index.query.js'
import { ViewContractLineIndexValidator } from './view-contract-line-index.validator.js'
import { ViewContractLineIndexSortQueryKey } from './query/view-contract-line-index.sort-query.js'

@Injectable()
export class ViewContractLineIndexUseCase {
  constructor (
    private readonly validator: ViewContractLineIndexValidator,
    private readonly authContext: AuthContext,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly sapGetContractLineIndex: SapGetContractLineIndexUseCase
  ) {}

  public async execute (
    query: ViewContractLineIndexQuery
  ): Promise<ViewContractLineIndexResponse> {
    await this.validator.validate(query)

    const sapQuery = await this.getSapQuery(query)
    const sapResult = await this.sapGetContractLineIndex.execute(sapQuery)

    const contractLines = MapContractLineSapService.mapResultsToContractLines(sapResult)

    return new ViewContractLineIndexResponse(contractLines, null)
  }

  private async getSapQuery (
    query: ViewContractLineIndexQuery
  ): Promise<SapQuery<SapGetContractLineIndexResponse>> {
    const sapQuery = new SapQuery<SapGetContractLineIndexResponse>(query, {
      defaultOrderBy: {
        column: 'Vbeln',
        direction: 'asc'
      },
      keyMapper: (key: string) => this.mapSortKeyToSapKey(key as ViewContractLineIndexSortQueryKey)
    })
      .addSelect([
        'Vbeln',
        'Posnr',
        'Yyklantmat',
        'MaterialDescription',
        'Matnr',
        'LifnrY0Name',
        'LifnrY3Name',
        'Kunnr',
        'KunnrDisplayName',
        'KunnrY2',
        'KunnrY2DisplayName',
        'KunnrWe',
        'KunnrWeDisplayName',
        'Yyasn',
        'Yvtfsflag',
        'Dangerous',
        'Ybpflow',
        'ActionNr',
        'Y004',
        'Eural',
        'YeLifnr',
        'YeDisplayName',
        'Remarks',
        'Installation',
        'DeliveryInfo',
        'MaterialType',
        'PackagingIndicator',
        'EsnNumber'
      ])
      .where('MaterialType', 'YWAS')

    // Add filters based on selected customer and accessible waste producers
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null) {
      const customerSalesOrganisationId = await this.getCustomerSalesOrganisationId
        .getOrganisationIdOrFail(selectedCustomerId)

      sapQuery.andWhere('Kunnr', selectedCustomerId)
        .andWhere('Vkorg', customerSalesOrganisationId)

      const restrictedWasteProducerIds = await this.userWasteProducerAuthService
        .getRestrictedWasteProducerIds(
          this.authContext.getAzureEntraUpnOrFail(),
          selectedCustomerId
        )
      if (restrictedWasteProducerIds !== undefined && restrictedWasteProducerIds.length > 0) {
        sapQuery.andWhere((qb) => {
          for (let i = 0; i < restrictedWasteProducerIds.length; i++) {
            if (i === 0) {
              qb.where('KunnrY2', restrictedWasteProducerIds[i])
            } else {
              qb.orWhere('KunnrY2', restrictedWasteProducerIds[i])
            }
          }
          return qb
        })
      }
    }

    // Add filters based on query parameters
    if (selectedCustomerId === null && query.filter?.customerId !== undefined) {
      const customerId = query.filter.customerId
      const customerSalesOrganisationId = await this.getCustomerSalesOrganisationId
        .getOrganisationIdOrFail(
          customerId
        )

      sapQuery.andWhere((qb) => {
        return qb.where('Kunnr', customerId)
          .andWhere('Vkorg', customerSalesOrganisationId)
      })
    }

    if (query.filter?.wasteProducerId !== undefined) {
      sapQuery.andWhere('KunnrY2', query.filter.wasteProducerId)
    }

    if (query.filter?.pickUpAddressIds !== undefined && query.filter.pickUpAddressIds.length > 0) {
      const pickUpAddressIds = query.filter.pickUpAddressIds
      sapQuery.andWhere((qb) => {
        for (let i = 0; i < pickUpAddressIds.length; i++) {
          if (i === 0) {
            qb.where('KunnrWe', pickUpAddressIds[i])
          } else {
            qb.orWhere('KunnrWe', pickUpAddressIds[i])
          }
        }
        return qb
      })
    }
    if (query.filter?.contractNumber !== undefined) {
      sapQuery.andWhere('Vbeln', query.filter.contractNumber)
    }
    if (query.filter?.contractItem !== undefined) {
      sapQuery.andWhere('Posnr', query.filter.contractItem)
    }
    if (query.filter?.customerReference !== undefined) {
      sapQuery.andWhere('Yyklantmat', query.filter.customerReference, FilterOperator.SUBSTRING_OF)
    }
    if (query.filter?.wasteMaterial !== undefined) {
      sapQuery.andWhere('MaterialDescription', query.filter.wasteMaterial, FilterOperator.SUBSTRING_OF)
    }
    if (query.filter?.materialNumber !== undefined) {
      sapQuery.andWhere('Matnr', query.filter.materialNumber)
    }
    if (query.filter?.treatmentCenterName !== undefined) {
      sapQuery.andWhere('LifnrY0Name', query.filter.treatmentCenterName, FilterOperator.SUBSTRING_OF)
    }
    if (query.filter?.installationName !== undefined) {
      sapQuery.andWhere('LifnrY3Name', query.filter.installationName, FilterOperator.SUBSTRING_OF)
    }
    if (query.filter?.asn !== undefined) {
      sapQuery.andWhere('Yyasn', query.filter.asn, FilterOperator.SUBSTRING_OF)
    }
    if (query.filter?.tfs !== undefined) {
      sapQuery.andWhere('Yvtfsflag', toBoolean(query.filter.tfs) ? 'X' : '')
    }
    if (query.filter?.isHazardous !== undefined) {
      sapQuery.andWhere('Dangerous', toBoolean(query.filter.isHazardous) ? 'X' : '')
    }
    if (query.filter?.tcNumber !== undefined) {
      sapQuery.andWhere('ActionNr', query.filter.tcNumber)
    }
    if (query.filter?.materialAnalysis !== undefined) {
      sapQuery.andWhere('Y004', query.filter.materialAnalysis, FilterOperator.SUBSTRING_OF)
    }
    if (query.filter?.ewcCode !== undefined) {
      sapQuery.andWhere('Eural', query.filter.ewcCode)
    }
    if (query.filter?.endTreatmentCenterId !== undefined) {
      sapQuery.andWhere('YeLifnr', query.filter.endTreatmentCenterId, FilterOperator.SUBSTRING_OF)
    }
    if (query.filter?.endTreatmentCenterName !== undefined) {
      sapQuery.andWhere('YeDisplayName', query.filter.endTreatmentCenterName, FilterOperator.SUBSTRING_OF)
    }
    if (query.filter?.processCode !== undefined) {
      sapQuery.andWhere('Installation', query.filter.processCode)
    }
    if (query.filter?.deliveryInfo !== undefined) {
      sapQuery.andWhere('DeliveryInfo', query.filter.deliveryInfo, FilterOperator.SUBSTRING_OF)
    }
    if (query.filter?.esnNumber !== undefined) {
      sapQuery.andWhere('EsnNumber', query.filter.esnNumber, FilterOperator.SUBSTRING_OF)
    }
    if (query.filter?.packagingIndicator !== undefined) {
      sapQuery.andWhere('PackagingIndicator', query.filter.packagingIndicator)
    }

    return sapQuery
  }

  private mapSortKeyToSapKey (
    key: ViewContractLineIndexSortQueryKey
  ): keyof SapGetContractLineIndexResponse {
    const mapping: Record<
      ViewContractLineIndexSortQueryKey,
      keyof SapGetContractLineIndexResponse
    > = {
      [ViewContractLineIndexSortQueryKey.CONTRACT_NUMBER]: 'Vbeln',
      [ViewContractLineIndexSortQueryKey.CONTRACT_ITEM]: 'Posnr',
      [ViewContractLineIndexSortQueryKey.WASTE_MATERIAL]: 'MaterialDescription',
      [ViewContractLineIndexSortQueryKey.TREATMENT_CENTER_NAME]: 'LifnrY0Name',
      [ViewContractLineIndexSortQueryKey.INSTALLATION_NAME]: 'LifnrY3Name',
      [ViewContractLineIndexSortQueryKey.WASTE_PRODUCER_ID]: 'KunnrY2',
      [ViewContractLineIndexSortQueryKey.WASTE_PRODUCER_NAME]: 'KunnrY2DisplayName',
      [ViewContractLineIndexSortQueryKey.PICK_UP_ADDRESS_ID]: 'KunnrWe',
      [ViewContractLineIndexSortQueryKey.PICK_UP_ADDRESS_NAME]: 'KunnrWeDisplayName',
      [ViewContractLineIndexSortQueryKey.ASN]: 'Yyasn',
      [ViewContractLineIndexSortQueryKey.EWC_CODE]: 'Eural',
      [ViewContractLineIndexSortQueryKey.TC_NUMBER]: 'ActionNr',
      [ViewContractLineIndexSortQueryKey.END_TREATMENT_CENTER_ID]: 'YeLifnr',
      [ViewContractLineIndexSortQueryKey.END_TREATMENT_CENTER_NAME]: 'YeDisplayName',
      [ViewContractLineIndexSortQueryKey.PROCESS_CODE]: 'Installation',
      [ViewContractLineIndexSortQueryKey.ESN_NUMBER]: 'EsnNumber'
    }

    return mapping[key]
  }
}
