import { ArrayUnique, IsArray, IsNotEmpty, IsObject, IsOptional, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { PaginatedKeysetSearchQuery } from '@wisemen/pagination'
import { IsUndefinable } from '@wisemen/validators'
import { ViewContractLineIndexPaginationQuery } from './view-contract-line-index.pagination-query.js'
import { ViewContractLineIndexSortQuery } from './view-contract-line-index.sort-query.js'
import { ViewContractLineIndexFilterQuery } from './view-contract-line-index.filter-query.js'

export class ViewContractLineIndexQuery extends PaginatedKeysetSearchQuery {
  @ApiProperty({ type: ViewContractLineIndexSortQuery, required: false, isArray: true })
  @Type(() => ViewContractLineIndexSortQuery)
  @ValidateNested({ each: true })
  @IsUndefinable()
  @IsArray()
  @ArrayUnique()
  sort?: ViewContractLineIndexSortQuery[]

  @ApiProperty({ type: ViewContractLineIndexFilterQuery })
  @Type(() => ViewContractLineIndexFilterQuery)
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @IsNotEmpty()
  filter?: ViewContractLineIndexFilterQuery

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  search?: string

  @ApiProperty({ type: ViewContractLineIndexPaginationQuery, required: false })
  @IsUndefinable()
  @Type(() => ViewContractLineIndexPaginationQuery)
  @ValidateNested()
  @IsObject()
  pagination?: ViewContractLineIndexPaginationQuery
}
