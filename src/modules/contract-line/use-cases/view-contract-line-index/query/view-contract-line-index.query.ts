import { <PERSON>rrayUnique, IsArray, IsNotEmpty, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { FilterQuery, PaginatedKeysetSearchQuery } from '@wisemen/pagination'
import { IsUndefinable } from '@wisemen/validators'
import { ViewContractLineIndexPaginationQuery } from './view-contract-line-index.pagination-query.js'
import { ViewContractLineIndexSortQuery } from './view-contract-line-index.sort-query.js'

export class ViewContractLineIndexFilterQuery extends FilterQuery {
  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  customerId?: string

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  wasteProducerId?: string

  @ApiProperty({ type: String, isArray: true, required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  pickUpAddressIds?: string[]
}

export class ViewContractLineIndexQuery extends PaginatedKeysetSearchQuery {
  @ApiProperty({ type: ViewContractLineIndexSortQuery, required: false, isArray: true })
  @Type(() => ViewContractLineIndexSortQuery)
  @ValidateNested({ each: true })
  @IsUndefinable()
  @IsArray()
  @ArrayUnique()
  sort?: ViewContractLineIndexSortQuery[]

  @ApiProperty({ type: ViewContractLineIndexFilterQuery })
  @Type(() => ViewContractLineIndexFilterQuery)
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @IsNotEmpty()
  filter?: ViewContractLineIndexFilterQuery

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  search?: string

  @ApiProperty({ type: ViewContractLineIndexPaginationQuery, required: false })
  @IsUndefinable()
  @Type(() => ViewContractLineIndexPaginationQuery)
  @ValidateNested()
  @IsObject()
  pagination?: ViewContractLineIndexPaginationQuery
}
