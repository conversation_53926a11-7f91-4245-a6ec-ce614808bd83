import { ApiProperty } from '@nestjs/swagger'
import { FilterQuery } from '@wisemen/pagination'
import { IsUndefinable } from '@wisemen/validators'
import { IsArray, IsBooleanString, IsEnum, IsNotEmpty, IsString } from 'class-validator'

enum PackagingIndicatorFilter {
  RENTAL = 'R',
  SALE = 'S'
}

export class ViewContractLineIndexFilterQuery extends FilterQuery {
  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsUndefinable()
  @IsNotEmpty()
  customerId?: string

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsUndefinable()
  @IsNotEmpty()
  wasteProducerId?: string

  @ApiProperty({ type: String, isArray: true, required: false })
  @IsUndefinable()
  @IsArray()
  @IsString({ each: true })
  pickUpAddressIds?: string[]

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  contractNumber?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  contractItem?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  customerReference?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  wasteMaterial?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  materialNumber?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  treatmentCenterName?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  installationName?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  asn?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsBooleanString()
  @IsNotEmpty()
  tfs?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsBooleanString()
  @IsNotEmpty()
  isHazardous?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  tcNumber?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  materialAnalysis?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  ewcCode?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  endTreatmentCenterId?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  endTreatmentCenterName?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  processCode?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  deliveryInfo?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  esnNumber?: string

  @ApiProperty({ type: String, required: false, enum: PackagingIndicatorFilter, enumName: 'PackagingIndicatorFilter' })
  @IsUndefinable()
  @IsEnum(PackagingIndicatorFilter)
  @IsString()
  @IsNotEmpty()
  packagingIndicator?: string
}
