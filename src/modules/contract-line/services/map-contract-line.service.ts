import assert from 'node:assert'
import { SapGetContractLineIndexResponse } from '../../sap/use-cases/get-contract-line-index/get-contract-line-index.response.js'
import { ContractLine } from '../types/contract-line.type.js'
import { toBoolean } from '../../../utils/transformers/to-boolean.js'
import { mapSapContractLineId } from '../helpers/map-contract-line-id.helper.js'
import { SapGetPickUpRequestDetailResponse } from '../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.response.js'

export class MapContractLineSapService {
  static mapPickUpRequestResultToContractLine (
    response: SapGetPickUpRequestDetailResponse
  ): ContractLine {
    assert(response.Vbeln !== undefined)
    assert(response.Posnr !== undefined)

    return {
      contractLineId: mapSapContractLineId(response.Vbeln, response.Posnr, response.ActionNr),

      contractNumber: response.Vbeln,
      contractItem: response.Posnr,
      customerReference: response.Yyklantmat !== '' && response.Yyklantmat !== undefined
        ? response.Yyklantmat
        : null,
      wasteMaterial: response.Arktx !== '' && response.Arktx !== undefined
        ? response.Arktx
        : null,
      materialNumber: response.Matnr !== '' && response.Matnr !== undefined
        ? response.Matnr
        : null,
      treatmentCenterName: null,
      installationName: null,
      customerId: response.Kunnr !== '' && response.Kunnr !== undefined
        ? response.Kunnr
        : null,
      customerName: null,
      wasteProducerId: response.KunnrY2 !== '' && response.KunnrY2 !== undefined
        ? response.KunnrY2
        : null,
      wasteProducerName: null,
      pickUpAddressId: response.KunnrWe !== '' && response.KunnrWe !== undefined
        ? response.KunnrWe
        : null,
      pickUpAddressName: null,
      asn: response.Yyasn !== '' && response.Yyasn !== undefined
        ? response.Yyasn
        : null,
      tfs: null,
      isHazardous: response.Dangerous !== '' && response.Dangerous !== undefined
        ? toBoolean(response.Dangerous)
        : null,
      packaged: null,
      tcNumber: response.ActionNr !== '' && response.ActionNr !== undefined
        ? response.ActionNr
        : null,
      materialAnalysis: null,
      ewcCode: response.Eural !== '' && response.Eural !== undefined
        ? response.Eural
        : null,
      endTreatmentCenterId: response.YeLifnr !== '' && response.YeLifnr !== undefined
        ? response.YeLifnr
        : null,
      endTreatmentCenterName: null,
      remarks: null,
      processCode: null,
      esnNumber: null,
      deliveryInfo: null,
      materialType: null,
      packagingIndicator: null,
      isSales: null
    }
  }

  static mapResultToContractLine (
    response: SapGetContractLineIndexResponse,
    imageBaseUrl?: string
  ): ContractLine {
    assert(response.Vbeln !== undefined)
    assert(response.Posnr !== undefined)

    return {
      contractLineId: mapSapContractLineId(response.Vbeln, response.Posnr, response.ActionNr),

      contractNumber: response.Vbeln,
      contractItem: response.Posnr,
      customerReference: response.Yyklantmat !== '' && response.Yyklantmat !== undefined
        ? response.Yyklantmat
        : null,
      wasteMaterial: response.MaterialDescription !== '' && response.MaterialDescription !== undefined
        ? response.MaterialDescription
        : null,
      materialNumber: response.Matnr !== '' && response.Matnr !== undefined
        ? response.Matnr
        : null,
      treatmentCenterName: response.LifnrY0Name !== '' && response.LifnrY0Name !== undefined
        ? response.LifnrY0Name
        : null,
      installationName: response.LifnrY3Name !== '' && response.LifnrY3Name !== undefined
        ? response.LifnrY3Name
        : null,
      customerId: response.Kunnr !== '' && response.Kunnr !== undefined
        ? response.Kunnr
        : null,
      customerName: response.KunnrDisplayName !== '' && response.KunnrDisplayName !== undefined
        ? response.KunnrDisplayName
        : null,
      wasteProducerId: response.KunnrY2 !== '' && response.KunnrY2 !== undefined
        ? response.KunnrY2
        : null,
      wasteProducerName: response.KunnrY2DisplayName !== '' && response.KunnrY2DisplayName !== undefined
        ? response.KunnrY2DisplayName
        : null,
      pickUpAddressId: response.KunnrWe !== '' && response.KunnrWe !== undefined
        ? response.KunnrWe
        : null,
      pickUpAddressName: response.KunnrWeDisplayName !== '' && response.KunnrWeDisplayName !== undefined
        ? response.KunnrWeDisplayName
        : null,
      asn: response.Yyasn !== '' && response.Yyasn !== undefined
        ? response.Yyasn
        : null,
      tfs: response.Yvtfsflag !== undefined
        ? toBoolean(response.Yvtfsflag)
        : null,
      isHazardous: response.Dangerous !== undefined
        ? toBoolean(response.Dangerous)
        : null,
      packaged: response.Ybpflow !== '' && response.Ybpflow !== undefined
        ? response.Ybpflow
        : null,
      tcNumber: response.ActionNr !== '' && response.ActionNr !== undefined
        ? response.ActionNr
        : null,
      materialAnalysis: response.Y004 !== '' && response.Y004 !== undefined
        ? response.Y004
        : null,
      ewcCode: response.Eural !== '' && response.Eural !== undefined
        ? response.Eural
        : null,
      endTreatmentCenterId: response.YeLifnr !== '' && response.YeLifnr !== undefined
        ? response.YeLifnr
        : null,
      endTreatmentCenterName: response.YeDisplayName !== '' && response.YeDisplayName !== undefined
        ? response.YeDisplayName
        : null,
      remarks: response.Remarks !== '' && response.Remarks !== undefined
        ? response.Remarks
        : null,
      processCode: response.Installation !== '' && response.Installation !== undefined
        ? response.Installation
        : null,
      esnNumber: response.EsnNumber !== '' && response.EsnNumber !== undefined
        ? response.EsnNumber
        : null,
      deliveryInfo: response.DeliveryInfo !== '' && response.DeliveryInfo !== undefined
        ? response.DeliveryInfo
        : null,
      materialType: response.MaterialType !== '' && response.MaterialType !== undefined
        ? response.MaterialType
        : null,
      packagingIndicator: response.PackagingIndicator !== '' && response.PackagingIndicator !== undefined
        ? response.PackagingIndicator
        : null,
      isSales: response.PackagingIndicator !== '' && response.PackagingIndicator !== undefined
        ? response.PackagingIndicator === 'S' ? true : false
        : null,
      imageUrl: imageBaseUrl !== undefined && response.Matnr !== undefined
        ? `${imageBaseUrl}${response.Matnr}.jpg`
        : undefined
    }
  }

  static mapResultsToContractLines (
    responses: SapGetContractLineIndexResponse[],
    imageBaseUrl?: string
  ): ContractLine[] {
    return responses.map(response => this.mapResultToContractLine(response, imageBaseUrl))
  }
}
