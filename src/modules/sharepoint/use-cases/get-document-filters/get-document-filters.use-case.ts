import { Injectable } from '@nestjs/common'
import { SharepointClient } from '../../clients/sharepoint.client.js'
import { SHAREPOINT_ENDPOINTS } from '../../constants/endpoints.constant.js'
import { SharepointGetDocumentFiltersResponse } from './get-document-filters.response.js'
import { GetDocumentFiltersQuery as GetDocumentFiltersCommand } from './get-document-filters.command.js'

@Injectable()
export class SharepointGetDocumentFiltersUseCase {
  constructor (
    private readonly sharepointClient: SharepointClient
  ) {}

  async execute (
    command: GetDocumentFiltersCommand
  ): Promise<SharepointGetDocumentFiltersResponse> {
    const response = await this.sharepointClient.client
      .post<SharepointGetDocumentFiltersResponse>(
        SHAREPOINT_ENDPOINTS.AVAILABLE_FILTERS.INDEX, command
      )

    return response.data
  }
}
