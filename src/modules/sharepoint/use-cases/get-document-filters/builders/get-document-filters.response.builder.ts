import { SharepointGetDocumentFiltersResponse } from '../get-document-filters.response.js'

export class GetDocumentFiltersResponseBuilder {
  private response: SharepointGetDocumentFiltersResponse

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.response = {
      lookupValuesTFS: {
        1: '1. TFS Application',
        2: '2. Consentment authorities',
        3: '3. Notification form',
        4: '4. Shipment form',
        5: '5. Itinerary',
        6: '6. Info transporters'
      },
      dropdownValuesTransportType: [
        {
          key: '-1',
          value: '(empty)'
        },
        {
          key: '1',
          value: 'Begeleidingsformulier NL'
        },
        {
          key: '2',
          value: 'CMR'
        },
        {
          key: '3',
          value: 'Identification form'
        }
      ],
      distinctLookupValues: {
        extRefLookupValues: {
          'YC-0276': 'YC-0276',
          'YC-0275': 'YC-0275',
          'YC-0274': 'YC-0274',
          'YC-0266': 'YC-0266',
          'YC-0176': 'YC-0176',
          'YC-0118': 'YC-0118',
          'YC-0117': 'YC-0117',
          'YC-0116': 'YC-0116',
          'NM-9546': 'NM-9546',
          'NM-9080': 'NM-9080'
        },
        yearLookupValues: {
          2009: '2009',
          2010: '2010',
          2011: '2011',
          2012: '2012',
          2013: '2013',
          2014: '2014',
          2015: '2015',
          2016: '2016',
          2017: '2017',
          2018: '2018',
          2019: '2019',
          2020: '2020',
          2021: '2021',
          2022: '2022',
          2023: '2023',
          2024: '2024',
          2025: '2025'
        },
        transportDocTypeIds: [
          '-1',
          '10'
        ]
      }
    }

    return this
  }

  public build (): SharepointGetDocumentFiltersResponse {
    const result = this.response

    this.reset()

    return result
  }
}
