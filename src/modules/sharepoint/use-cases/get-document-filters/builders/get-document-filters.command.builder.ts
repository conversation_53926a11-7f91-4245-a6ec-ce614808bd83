import { GetDocumentFiltersQuery } from '../get-document-filters.command.js'

export class GetDocumentFiltersCommandBuilder {
  private command: GetDocumentFiltersQuery

  constructor () {
    this.reset()

    this.command.viewName = ''
    this.command.siteId = ''
    this.command.wasteProducerIds = []
    this.command.webUrl = ''
    this.command.publishedDocumentsLibraryId = ''
    this.command.typeTFSListId = ''
    this.command.typeTransportDocListId = ''
  }

  private reset (): this {
    this.command = new GetDocumentFiltersQuery()
    return this
  }

  withViewName (viewName: string): this {
    this.command.viewName = viewName
    return this
  }

  withSiteId (siteId: string): this {
    this.command.siteId = siteId
    return this
  }

  withWasteProducerIds (wasteProducerIds: string[]): this {
    this.command.wasteProducerIds = wasteProducerIds
    return this
  }

  withWebUrl (webUrl: string): this {
    this.command.webUrl = webUrl
    return this
  }

  withPublishedDocumentsLibraryId (publishedDocumentsLibraryId: string): this {
    this.command.publishedDocumentsLibraryId = publishedDocumentsLibraryId
    return this
  }

  withTypeTFSListId (typeTFSListId: string): this {
    this.command.typeTFSListId = typeTFSListId
    return this
  }

  withTypeTransportDocListId (typeTransportDocListId: string): this {
    this.command.typeTransportDocListId = typeTransportDocListId
    return this
  }

  public build (): GetDocumentFiltersQuery {
    const result = this.command
    this.reset()
    return result
  }
}
