import { Controller, Get } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ViewSalesOrganisationIndexUseCase } from './view-sales-organisation-index.use-case.js'
import { ViewSalesOrganisationIndexResponse } from './view-sales-organisation-index.response.js'

@ApiTags('Sales Organisation')
@ApiOAuth2([])
@Controller('sales-organisations')
export class ViewSalesOrganisationIndexController {
  constructor (
    private readonly useCase: ViewSalesOrganisationIndexUseCase
  ) {}

  @Get()
  @Permissions(
    Permission.WASTE_INQUIRY_MANAGE,
    Permission.WASTE_INQUIRY_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ
  )
  @ApiOkResponse({ type: ViewSalesOrganisationIndexResponse, isArray: true })
  async viewSalesOrganisationIndex (): Promise<ViewSalesOrganisationIndexResponse[]> {
    return await this.useCase.execute()
  }
}
