import { Controller, Get, Query } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ViewSalesOrganisationIndexUseCase } from './view-sales-organisation-index.use-case.js'
import { SapGetSalesOrganisationIndexResponse } from '../../../sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.response.js'
import { ViewSalesOrganisationIndexQuery } from './view-sales-organisation-index.query.js'

@ApiTags('Sales Organisation')
@ApiOAuth2([])
@Controller('sales-organisations')
export class ViewSalesOrganisationIndexController {
  constructor (
    private readonly useCase: ViewSalesOrganisationIndexUseCase
  ) {}

  @Get()
  @Permissions(
    Permission.NEWS_ITEM_MANAGE
  )
  @ApiOkResponse({ 
    description: 'Sales organisations retrieved successfully' 
  })
  async viewSalesOrganisationIndex (
    @Query() query: ViewSalesOrganisationIndexQuery
  ): Promise<SapGetSalesOrganisationIndexResponse[]> {
    return await this.useCase.execute(query)
  }
}
