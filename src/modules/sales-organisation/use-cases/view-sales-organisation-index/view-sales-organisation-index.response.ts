import { ApiProperty } from '@nestjs/swagger'
import { SapGetSalesOrganisationIndexResponse } from '../../../sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.response.js'

export class ViewSalesOrganisationIndexResponse {
  @ApiProperty({ type: String, description: 'Sales organization code' })
  salesOrganization: string

  @ApiProperty({ type: String, description: 'Sales organization name' })
  salesOrganizationName: string

  @ApiProperty({ type: String, description: 'Country code' })
  country: string

  @ApiProperty({ type: String, description: 'Country name' })
  countryName: string

  constructor (sapResponse: SapGetSalesOrganisationIndexResponse) {
    this.salesOrganization = sapResponse.SalesOrganization
    this.salesOrganizationName = sapResponse.SalesOrganizationName
    this.country = sapResponse.Country
    this.countryName = sapResponse.CountryName
  }
}
