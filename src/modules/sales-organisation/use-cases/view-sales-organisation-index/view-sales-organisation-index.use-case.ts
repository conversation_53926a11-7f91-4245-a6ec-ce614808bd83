import { Injectable } from '@nestjs/common'
import { SapGetSalesOrganisationIndexUseCase } from '../../../sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetSalesOrganisationIndexResponse } from '../../../sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.response.js'
import { ViewSalesOrganisationIndexResponse } from './view-sales-organisation-index.response.js'

@Injectable()
export class ViewSalesOrganisationIndexUseCase {
  constructor (
    private readonly sapGetSalesOrganisationIndexUseCase: SapGetSalesOrganisationIndexUseCase
  ) {}

  async execute (): Promise<ViewSalesOrganisationIndexResponse[]> {
    const query = new SapQuery<SapGetSalesOrganisationIndexResponse>()
    const sapResponse = await this.sapGetSalesOrganisationIndexUseCase.execute(query)
    
    return sapResponse.map(item => new ViewSalesOrganisationIndexResponse(item))
  }
}
