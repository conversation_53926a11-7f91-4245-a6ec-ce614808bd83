import { Injectable } from '@nestjs/common'
import { SapGetSalesOrganisationIndexUseCase } from '../../../sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetSalesOrganisationIndexResponse } from '../../../sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.response.js'

@Injectable()
export class ViewSalesOrganisationIndexUseCase {
  constructor (
    private readonly sapGetSalesOrganisationIndexUseCase: SapGetSalesOrganisationIndexUseCase
  ) {}

  async execute (query?: { search?: string }): Promise<SapGetSalesOrganisationIndexResponse[]> {
    const sapQuery = new SapQuery<SapGetSalesOrganisationIndexResponse>(query)
    return await this.sapGetSalesOrganisationIndexUseCase.execute(sapQuery)
  }
}
