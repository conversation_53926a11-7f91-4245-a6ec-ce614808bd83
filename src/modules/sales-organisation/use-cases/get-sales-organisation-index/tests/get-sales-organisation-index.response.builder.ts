import { randCompanyName, randCountry, randCountryCode, randNumber } from '@ngneat/falso'
import { SapGetSalesOrganisationIndexResponse } from '../get-sales-organisation-index.response.js'

export class GetSalesOrganisationIndexResponseBuilder {
  private response: SapGetSalesOrganisationIndexResponse

  constructor () {
    this.reset()
  }

  reset (): this {
    this.response = {
      SalesOrganization: randNumber({ min: 1000, max: 9999 }).toString(),
      SalesOrganizationName: randCompanyName(),
      Country: randCountryCode(),
      CountryName: randCountry()
    }

    return this
  }

  build (): SapGetSalesOrganisationIndexResponse {
    const result = this.response

    this.reset()

    return result
  }
}
