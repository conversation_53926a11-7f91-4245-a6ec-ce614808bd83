import { Inject, Injectable } from '@nestjs/common'
import { AxiosInstance } from 'axios'
import { SAP_ENDPOINTS } from '../../../sap/constants/endpoint.constants.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SAP_CLIENT } from '../../../sap/sap-client.provider.js'
import { SapValueResponse } from '../../../sap/shared/sap-value.response.js'
import { SapCall } from '../../../sap/enums/sap-call.enum.js'
import { SapCacheService } from '../../../sap/services/sap-cache.service.js'
import { SapGetSalesOrganisationIndexResponse } from './get-sales-organisation-index.response.js'

@Injectable()
export class SapGetSalesOrganisationIndexUseCase {
  constructor(
    @Inject(SAP_CLIENT) private client: AxiosInstance,
    private readonly sapCacheService: SapCacheService
  ) { }

  async execute(
    query: SapQuery<SapGetSalesOrganisationIndexResponse>
  ): Promise<SapGetSalesOrganisationIndexResponse[]> {
    const cachedResponse = await this.sapCacheService.getResponse<
      SapGetSalesOrganisationIndexResponse[]
    >(
      SapCall.VIEW_SALES_ORGANISATION_INDEX,
      query.getUniqueKey()
    )

    if (cachedResponse !== null) return cachedResponse

    const url = query.buildUrl(SAP_ENDPOINTS.SALES_ORGANISATION.INDEX)

    const response = await this.client.get<
      SapValueResponse<SapGetSalesOrganisationIndexResponse[]>
    >(
      url
    )

    await this.sapCacheService.putResponse(
      response.data.value,
      SapCall.VIEW_SALES_ORGANISATION_INDEX,
      query.getUniqueKey()
    )

    return response.data.value
  }
}
