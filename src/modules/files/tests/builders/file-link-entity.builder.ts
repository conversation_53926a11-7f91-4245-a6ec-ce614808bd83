import { randomUUID } from 'crypto'
import { randNumber } from '@ngneat/falso'
import { getRandomEnumValue } from '../../../../../test/utils/get-random-enum-value.js'
import { FileLink } from '../../entities/file-link.entity.js'
import { EntityPart } from '../../enums/entity-part.enum.js'
import { User } from '../../../../app/users/entities/user.entity.js'
import { File } from '../../entities/file.entity.js'

export class FileLinkEntityBuilder {
  private fileLink: FileLink

  constructor () {
    this.reset()
  }

  reset (): this {
    this.fileLink = new FileLink()

    this.fileLink.fileUuid = randomUUID()
    this.fileLink.entityUuid = randomUUID()
    this.fileLink.entityType = User.name
    this.fileLink.entityPart = getRandomEnumValue(EntityPart)
    this.fileLink.order = randNumber({ min: 1, max: 1000 })

    return this
  }

  withEntityPart (entityPart: string): this {
    this.fileLink.entityPart = entityPart

    return this
  }

  withFile (file: File): this {
    this.fileLink.file = file

    return this
  }

  build (): FileLink {
    const result = this.fileLink

    this.reset()

    return result
  }
}
