import { SapDocumentType } from '../../sap/use-cases/upload-document/sap-document-type.type.js'
import { EntityPart } from '../enums/entity-part.enum.js'

export class SapEntityPartMapper {
  public static toSap (entityPart: EntityPart): SapDocumentType {
    switch (entityPart) {
      case EntityPart.SDS:
        return 'ZCI_SD_ALL'
      case EntityPart.ANALYSIS_REPORT:
        return 'ZCI_AR_ALL'
      case EntityPart.ADDITIONAL:
        return 'ZCI_OT_ALL'
      default:
        throw new Error(`No SAP mapping defined for entity part: ${entityPart}`)
    }
  }

  public static mapResultToEntityPart (sapCode: string): EntityPart {
    switch (sapCode) {
      case 'ZCI_SD_ALL':
        return EntityPart.SDS
      case 'ZCI_AR_ALL':
        return EntityPart.ANALYSIS_REPORT
      case 'ZCI_OT_ALL':
        return EntityPart.ADDITIONAL
      default:
        throw new Error(`Unknown SAP document type code: ${sapCode}`)
    }
  }
}
