import { randomUUID } from 'crypto'
import { randStreetName } from '@ngneat/falso'
import { Address } from '../../../utils/address/types/address.type.js'
import { AddressTypeBuilder } from '../../../utils/address/types/address-type.builder.js'
import { PickUpAddress } from '../types/pick-up-address.type.js'

export class PickUpAddressBuilder {
  private readonly pickUpAddress: PickUpAddress

  constructor () {
    const addressBuilder = new AddressTypeBuilder()
    const address = addressBuilder.build()

    this.pickUpAddress = {
      id: randomUUID(),
      name: randStreetName(),
      address
    }
  }

  withId (id: string): this {
    this.pickUpAddress.id = id
    return this
  }

  withName (name: string): this {
    this.pickUpAddress.name = name
    return this
  }

  withAddress (address?: Address | null): this {
    this.pickUpAddress.address = address ?? null
    return this
  }

  build (): PickUpAddress {
    return this.pickUpAddress
  }
}
