import { Controller, Get, Query } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewPickUpAddressIndexUseCase } from './view-pick-up-address-index.use-case.js'
import { ViewPickUpAddressIndexQuery } from './query/view-pick-up-address-index.query.js'
import { ViewPickUpAddressIndexResponse } from './view-pick-up-address-index.response.js'

@ApiTags('Pick-up address')
@ApiOAuth2([])
@Controller('pick-up-addresses')
export class ViewPickUpAddressIndexController {
  constructor (
    private readonly useCase: ViewPickUpAddressIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(
    Permission.WASTE_INQUIRY_MANAGE,
    Permission.WASTE_INQUIRY_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ
  )
  @ApiOkResponse({ type: ViewPickUpAddressIndexResponse })
  public async viewPickUpAddressIndex (
    @Query() query: ViewPickUpAddressIndexQuery
  ): Promise<ViewPickUpAddressIndexResponse> {
    return await this.useCase.execute(query)
  }
}
