import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { createStubInstance, SinonStubbedInstance, assert } from 'sinon'
import { randCity, randFullName, randStreetAddress, randZipCode } from '@ngneat/falso'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewPickUpAddressIndexUseCase } from '../view-pick-up-address-index.use-case.js'
import { PickUpAddress } from '../../../types/pick-up-address.type.js'
import { MapPickUpAddressSapService } from '../../../services/map-pick-up-address.service.js'
import { SapGetPickUpAddressIndexUseCase } from '../../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.use-case.js'
import { ViewPickUpAddressIndexValidator } from '../view-pick-up-address-index.validator.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetPickUpAddressIndexResponse } from '../../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.response.js'
import { ViewPickUpAddressIndexQueryBuilder } from './view-pick-up-address-index.query.builder.js'

describe('View pick-up addresses index use-case unit test', () => {
  let useCase: ViewPickUpAddressIndexUseCase

  let validator: SinonStubbedInstance<ViewPickUpAddressIndexValidator>
  let salesOrganisationUseCase: SinonStubbedInstance<GetCustomerDefaultSalesOrganisationIdUseCase>
  let sapGetPickUpAddressIndex: SinonStubbedInstance<SapGetPickUpAddressIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    validator = createStubInstance(ViewPickUpAddressIndexValidator)
    salesOrganisationUseCase = createStubInstance(GetCustomerDefaultSalesOrganisationIdUseCase)
    sapGetPickUpAddressIndex = createStubInstance(SapGetPickUpAddressIndexUseCase)

    useCase = new ViewPickUpAddressIndexUseCase(
      validator,
      salesOrganisationUseCase,
      sapGetPickUpAddressIndex
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    validator.execute.returns()
    salesOrganisationUseCase.getOrganisationIdOrFail.resolves(randomUUID())
    sapGetPickUpAddressIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetPickUpAddressIndexResponse>().build()
    )
  }

  it ('Call all methods once', async () => {
    const query = new ViewPickUpAddressIndexQueryBuilder().build()

    await useCase.execute(query)

    assert.calledOnce(validator.execute)
    assert.calledOnce(salesOrganisationUseCase.getOrganisationIdOrFail)
    assert.calledOnce(sapGetPickUpAddressIndex.execute)
  })

  it('Returns parsed result from SAP', async () => {
    const pickUpAddress: PickUpAddress[] = [{
      id: randomUUID(),
      name: randFullName(),
      address: {
        countryCode: null,
        postalCode: randZipCode(),
        locality: randCity(),
        addressLine1: randStreetAddress(),
        addressLine2: null,
        coordinates: null
      }
    }]

    const sapResults = MapPickUpAddressSapService.mapPickUpAddressesToResults(pickUpAddress)

    sapGetPickUpAddressIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetPickUpAddressIndexResponse>()
        .addItem(sapResults)
        .build()
    )

    const result = await useCase.execute({
      filter: {
        customerId: randomUUID()
      }
    })

    expect(result.items).toStrictEqual(expect.arrayContaining(pickUpAddress))
  })
})
