import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { SinonStubbedInstance, createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { ViewPickUpAddressIndexValidator } from '../view-pick-up-address-index.validator.js'
import { ViewPickUpAddressIndexQuery } from '../query/view-pick-up-address-index.query.js'
import { SelectedCustomerFilterMismatchError } from '../../../../customer/errors/selected-customer-filter-mismatch.error.js'
import { ViewPickUpAddressIndexQueryBuilder } from './view-pick-up-address-index.query.builder.js'

describe('View pick-up address index validator unit test', () => {
  let validator: ViewPickUpAddressIndexValidator

  let query: ViewPickUpAddressIndexQuery

  let authContext: SinonStubbedInstance<AuthContext>

  before(() => {
    TestBench.setupUnitTest()

    query = new ViewPickUpAddressIndexQueryBuilder().build()

    authContext = createStubInstance(AuthContext)

    validator = new ViewPickUpAddressIndexValidator(
      authContext
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getAzureEntraUpnOrFail.resolves(randomUUID())
    authContext.getSelectedCustomerId.returns(query.filter.customerId)
  }

  it('doesn\'t throw an error when validation passes', () => {
    expect(() => validator.execute(query)).not.toThrow()
  })

  it('throws an error when customer is different from selected customer', () => {
    authContext.getSelectedCustomerId.returns(randomUUID())

    expect(() => validator.execute(query)).toThrow(SelectedCustomerFilterMismatchError)
  })
})
