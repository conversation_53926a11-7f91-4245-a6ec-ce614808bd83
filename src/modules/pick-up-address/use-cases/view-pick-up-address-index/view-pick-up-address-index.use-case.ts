import { Injectable } from '@nestjs/common'
import { SapGetPickUpAddressIndexResponse } from '../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.response.js'
import { SapGetPickUpAddressIndexUseCase } from '../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.use-case.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { MapPickUpAddressSapService } from '../../services/map-pick-up-address.service.js'
import { ViewPickUpAddressIndexQuery } from './query/view-pick-up-address-index.query.js'
import { ViewPickUpAddressIndexResponse } from './view-pick-up-address-index.response.js'
import { ViewPickUpAddressIndexValidator } from './view-pick-up-address-index.validator.js'

@Injectable()
export class ViewPickUpAddressIndexUseCase {
  constructor (
    private readonly validator: ViewPickUpAddressIndexValidator,
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly sapGetPickUpAddressIndex: SapGetPickUpAddressIndexUseCase
  ) {}

  public async execute (
    query: ViewPickUpAddressIndexQuery
  ): Promise<ViewPickUpAddressIndexResponse> {
    this.validator.execute(query)

    const customerDefaultSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        query.filter.customerId
      )

    const sapQuery = this.getSapQuery(
      query,
      customerDefaultSalesOrganisationId
    )
    const sapResult = await this.sapGetPickUpAddressIndex.execute(sapQuery)

    const pickUpAddresses = MapPickUpAddressSapService.mapResultsToPickUpAddresses(sapResult.items)

    return new ViewPickUpAddressIndexResponse(
      pickUpAddresses,
      sapResult.skipToken
    )
  }

  private getSapQuery (
    query: ViewPickUpAddressIndexQuery,
    customerDefaultSalesOrganisationId: string
  ): SapQuery<SapGetPickUpAddressIndexResponse> {
    const sapQuery = new SapQuery<SapGetPickUpAddressIndexResponse>(query)
      .where('Customer', query.filter.customerId)
      .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)

    return sapQuery
  }
}
