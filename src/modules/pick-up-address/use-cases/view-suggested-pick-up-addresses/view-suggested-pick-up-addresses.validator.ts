import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { SelectedCustomerFilterMismatchError } from '../../../customer/errors/selected-customer-filter-mismatch.error.js'
import { ViewSuggestedPickUpAddressesQuery } from './view-suggested-pick-up-addresses.query.js'

@Injectable()
export class ViewSuggestedPickUpAddressesValidator {
  constructor (
    private readonly authContext: AuthContext
  ) {}

  validate (
    query: ViewSuggestedPickUpAddressesQuery
  ): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== query.filter.customerId) {
      throw new SelectedCustomerFilterMismatchError({ pointer: '$.customerId' })
    }
  }
}
