import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewSuggestedPickUpAddressesUseCase } from './view-suggested-pick-up-addresses.use-case.js'
import { ViewSuggestedPickUpAddressesResponse } from './view-suggested-pick-up-addresses.response.js'
import { ViewSuggestedPickUpAddressesQuery as ViewSuggestedPickUpAddressesQuery } from './view-suggested-pick-up-addresses.query.js'

@ApiTags('Pick-up address')
@ApiOAuth2([])
@Controller('suggested-pick-up-addresses')
export class ViewSuggestedPickUpAddressesController {
  constructor (
    private readonly useCase: ViewSuggestedPickUpAddressesUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(
    Permission.WASTE_INQUIRY_MANAGE,
    Permission.WASTE_INQUIRY_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ
  )
  @ApiOkResponse({ type: ViewSuggestedPickUpAddressesResponse })
  public async viewSuggestedPickUpAddressesIndex (
    @Query() query: ViewSuggestedPickUpAddressesQuery
  ): Promise<ViewSuggestedPickUpAddressesResponse> {
    return await this.useCase.execute(query)
  }
}
