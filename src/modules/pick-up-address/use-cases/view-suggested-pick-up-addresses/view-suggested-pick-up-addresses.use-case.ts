import { Injectable } from '@nestjs/common'
import { RequestType } from '../../../../utils/enums/request-type.enum.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { SapGetPickUpAddressIndexResponse } from '../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.response.js'
import { SUGGESTED_PICK_UP_ADDRESSES_AMOUNT } from '../../../../utils/constants/suggested-entities-amount.constant.js'
import { SapGetPickUpAddressIndexUseCase } from '../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.use-case.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { MapPickUpAddressSapService } from '../../services/map-pick-up-address.service.js'
import { FilterOperator } from '../../../sap/enums/odata-filter-operator.enum.js'
import { ViewSuggestedPickUpAddressesResponse } from './view-suggested-pick-up-addresses.response.js'
import { ViewSuggestedPickUpAddressesQuery as ViewSuggestedPickUpAddressesQuery } from './view-suggested-pick-up-addresses.query.js'
import { ViewSuggestedPickUpAddressesRepository } from './view-suggested-pick-up-addresses.repository.js'
import { ViewSuggestedPickUpAddressesValidator } from './view-suggested-pick-up-addresses.validator.js'

@Injectable()
export class ViewSuggestedPickUpAddressesUseCase {
  constructor (
    private readonly validator: ViewSuggestedPickUpAddressesValidator,
    private readonly authContext: AuthContext,
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly repository: ViewSuggestedPickUpAddressesRepository,
    private readonly sapGetPickUpAddressIndex: SapGetPickUpAddressIndexUseCase
  ) {}

  public async execute (
    query: ViewSuggestedPickUpAddressesQuery
  ): Promise<ViewSuggestedPickUpAddressesResponse> {
    this.validator.validate(query)

    const userUuid = this.authContext.getUserUuidOrFail()
    const recentPickUpAddressIds = await this.getRecentPickUpAddressIds(
      userUuid,
      query.filter.requestType,
      query.filter.customerId
    )
    const customerDefaultSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        query.filter.customerId
      )

    let sapPickUpAddresses: SapGetPickUpAddressIndexResponse[] = []

    if (recentPickUpAddressIds.length > 0) {
      sapPickUpAddresses = await this.getSapPickUpAddressesByIds(
        query.filter.customerId,
        customerDefaultSalesOrganisationId,
        recentPickUpAddressIds
      )

      if (sapPickUpAddresses.length < SUGGESTED_PICK_UP_ADDRESSES_AMOUNT) {
        const sapExtraPickUpAddresses = await this.getSapPickUpAddresses(
          query.filter.customerId,
          customerDefaultSalesOrganisationId,
          SUGGESTED_PICK_UP_ADDRESSES_AMOUNT - sapPickUpAddresses.length,
          recentPickUpAddressIds
        )

        sapPickUpAddresses.push(...sapExtraPickUpAddresses)
      }
    } else {
      sapPickUpAddresses = await this.getSapPickUpAddresses(
        query.filter.customerId,
        customerDefaultSalesOrganisationId,
        SUGGESTED_PICK_UP_ADDRESSES_AMOUNT
      )
    }

    const pickUpAddresses = MapPickUpAddressSapService.mapResultsToPickUpAddresses(
      sapPickUpAddresses
    )

    return new ViewSuggestedPickUpAddressesResponse(
      pickUpAddresses
    )
  }

  private async getRecentPickUpAddressIds (
    userUuid: string,
    requestType: RequestType,
    customerId: string
  ): Promise<string[]> {
    switch (requestType) {
      case RequestType.WASTE:
        return this.repository.findRecentWasteInquiryPickUpAddressIds(userUuid, customerId)
      case RequestType.PICK_UP:
        return this.repository.findRecentPickUpRequestPickUpAddressIds(userUuid, customerId)
      default:
        exhaustiveCheck(requestType)
    }
  }

  private async getSapPickUpAddressesByIds (
    customerId: string,
    customerDefaultSalesOrganisationId: string,
    pickUpAddressIds: string[]
  ): Promise<SapGetPickUpAddressIndexResponse[]> {
    const sapQuery = new SapQuery<SapGetPickUpAddressIndexResponse>()
      .where('Customer', customerId)
      .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)
      .andWhere((qb) => {
        qb.where('PickUpAddress', pickUpAddressIds[0])
        for (let i = 1; i < pickUpAddressIds.length; i++) {
          qb.orWhere('PickUpAddress', pickUpAddressIds[i])
        }
        return qb
      })

    const sapResponse = await this.sapGetPickUpAddressIndex.execute(sapQuery)

    return sapResponse.items
  }

  private async getSapPickUpAddresses (
    customerId: string,
    customerDefaultSalesOrganisationId: string,
    amount: number,
    excludePickUpAddressIds?: string[]
  ): Promise<SapGetPickUpAddressIndexResponse[]> {
    const sapQuery = new SapQuery<SapGetPickUpAddressIndexResponse>()
      .where('Customer', customerId)
      .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)

    if (excludePickUpAddressIds !== undefined && excludePickUpAddressIds.length > 0) {
      sapQuery.andWhere((qb) => {
        qb.where('PickUpAddress', excludePickUpAddressIds[0], FilterOperator.NOT_EQUAL)
        for (let i = 1; i < excludePickUpAddressIds.length; i++) {
          qb.andWhere('PickUpAddress', excludePickUpAddressIds[i], FilterOperator.NOT_EQUAL)
        }
        return qb
      })
    }

    sapQuery.addOrderBy('PickUpAddressAddress', 'asc')
    sapQuery.setTop(amount)

    const sapResponse = await this.sapGetPickUpAddressIndex.execute(sapQuery)

    return sapResponse.items
  }
}
