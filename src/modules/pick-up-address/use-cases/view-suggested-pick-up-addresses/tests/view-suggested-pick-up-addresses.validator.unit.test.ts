import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { SinonStubbedInstance, createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { RequestType } from '../../../../../utils/enums/request-type.enum.js'
import { ViewSuggestedPickUpAddressesValidator } from '../view-suggested-pick-up-addresses.validator.js'
import { ViewSuggestedPickUpAddressesQuery } from '../view-suggested-pick-up-addresses.query.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { SelectedCustomerFilterMismatchError } from '../../../../customer/errors/selected-customer-filter-mismatch.error.js'
import { ViewSuggestedPickUpAddressesQueryBuilder } from './view-suggested-pick-up-addresses.query.builder.js'
import { ViewSuggestedPickUpAddressesFilterQueryBuilder } from './view-suggested-pick-up-addresses.filter.builder.js'

describe('View suggested pick-up addresses validator unit test', () => {
  let validator: ViewSuggestedPickUpAddressesValidator

  let query: ViewSuggestedPickUpAddressesQuery

  let authContext: SinonStubbedInstance<AuthContext>

  before(() => {
    TestBench.setupUnitTest()

    query = new ViewSuggestedPickUpAddressesQueryBuilder()
      .withFilter(
        new ViewSuggestedPickUpAddressesFilterQueryBuilder()
          .withCustomerId(randomUUID())
          .withRequestType(RequestType.WASTE)
          .build()
      )
      .build()

    authContext = createStubInstance(AuthContext)

    validator = new ViewSuggestedPickUpAddressesValidator(
      authContext
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getSelectedCustomerId.returns(query.filter.customerId)
  }

  it('doesn\'t throw an error when validation passes', () => {
    expect(() => validator.validate(query)).not.toThrow()
  })

  it('throws an error when customer is not accessible by auth user', () => {
    authContext.getSelectedCustomerId.returns(randomUUID())

    expect(() => validator.validate(query)).toThrow(SelectedCustomerFilterMismatchError)
  })
})
