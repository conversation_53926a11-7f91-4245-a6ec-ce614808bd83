import { Inject, Injectable } from '@nestjs/common'
import { Client } from '@microsoft/microsoft-graph-client'
import { AppRoleAssignment } from 'microsoft-graph'

@Injectable()
export class AppRoleAssignmentsService {
  constructor (
        @Inject('MICROSOFT_GRAPH_CLIENT') private readonly client: Client
  ) { }

  async fetchAll (servicePrincipalId: string): Promise<AppRoleAssignment[]> {
    const assignments: AppRoleAssignment[] = []
    let nextLink: string | undefined = `/servicePrincipals/${servicePrincipalId}/appRoleAssignedTo`

    while (nextLink !== undefined) {
      const response = await this.client.api(nextLink).get() as {
        'value': AppRoleAssignment[]
        '@odata.nextLink'?: string
      }
      assignments.push(...(response.value ?? []))
      nextLink = response['@odata.nextLink']
    }

    return assignments
  }
}
