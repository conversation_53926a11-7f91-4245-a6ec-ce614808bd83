import { Modu<PERSON> } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { createMicrosoftGraphClient } from '../../microsoft-graph.client.js'
import { GetEntraUsersUseCase } from './fetch-entra-users.use-case.js'
import { AppRoleAssignmentsService } from './app-role-assignments.service.js'
import { GroupMembersService } from './group-members.service.js'
import { UsersService } from './users.service.js'

@Module({
  providers: [
    {
      provide: 'MICROSOFT_GRAPH_CLIENT',
      useFactory: (configService: ConfigService) => createMicrosoftGraphClient(configService),
      inject: [ConfigService]
    },
    AppRoleAssignmentsService,
    GroupMembersService,
    UsersService,
    GetEntraUsersUseCase
  ],
  exports: [GetEntraUsersUseCase]
})
export class GetEntraUsersModule { }
