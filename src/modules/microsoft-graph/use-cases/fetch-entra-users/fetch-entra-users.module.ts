import { Module } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { createMicrosoftGraphClient } from '../../microsoft-graph.client.js'
import { FetchEntraUsersUseCase } from './fetch-entra-users.use-case.js'

@Module({
  providers: [
    {
      provide: 'MICROSOFT_GRAPH_CLIENT',
      useFactory: (configService: ConfigService) => createMicrosoftGraphClient(configService),
      inject: [ConfigService]
    },
    FetchEntraUsersUseCase
  ],
  exports: [FetchEntraUsersUseCase]
})
export class FetchEntraUsersModule { }
