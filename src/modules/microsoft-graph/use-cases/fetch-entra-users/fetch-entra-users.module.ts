import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { Client } from '@microsoft/microsoft-graph-client'
import { ClientSecretCredential } from '@azure/identity'
import { TokenCredentialAuthenticationProvider } from '@microsoft/microsoft-graph-client/authProviders/azureTokenCredentials'
import { FetchEntraUsersUseCase } from './fetch-entra-users.use-case.js'

@Module({
    imports: [ConfigModule],
    providers: [
        FetchEntraUsersUseCase,
        {
            provide: 'MICROSOFT_GRAPH_CLIENT',
            useFactory: (configService: ConfigService) => {
                const tenantId = configService.getOrThrow<string>('MICROSOFT_GRAPH_TENANT_ID')
                const clientId = configService.getOrThrow<string>('MICROSOFT_GRAPH_CLIENT_ID')
                const clientSecret = configService.getOrThrow<string>('MICROSOFT_GRAPH_CLIENT_SECRET')

                const credential = new ClientSecretCredential(tenantId, clientId, clientSecret)
                const authProvider = new TokenCredentialAuthenticationProvider(credential, {
                    scopes: ['https://graph.microsoft.com/.default']
                })

                return Client.initWithMiddleware({ authProvider })
            },
            inject: [ConfigService]
        }
    ],
    exports: [FetchEntraUsersUseCase]
})
export class FetchEntraUsersModule { }
