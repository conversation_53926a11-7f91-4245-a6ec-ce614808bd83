import { ApiProperty } from '@nestjs/swagger'

export class EntraUserResponse {
  @ApiProperty()
  upn: string

  @ApiProperty()
  firstName: string

  @ApiProperty()
  lastName: string

  @ApiProperty()
  email: string

  @ApiProperty({ required: false, nullable: true })
  zitadelSub?: string | null

  constructor (
    data: {
      upn: string
      firstName: string
      lastName: string
      email: string
      zitadelSub?: string | null
    }
  ) {
    this.upn = data.upn
    this.firstName = data.firstName
    this.lastName = data.lastName
    this.email = data.email
    this.zitadelSub = data.zitadelSub ?? null
  }
}
