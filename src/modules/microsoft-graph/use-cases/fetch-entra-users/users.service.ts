import { Inject, Injectable } from '@nestjs/common'
import { Client } from '@microsoft/microsoft-graph-client'

interface GraphUser {
  id: string
  userPrincipalName: string
  mail: string
  givenName: string
  surname: string
}

@Injectable()
export class UsersService {
  constructor (
        @Inject('MICROSOFT_GRAPH_CLIENT') private readonly client: Client
  ) { }

  async fetchByIds (userIds: string[]): Promise<GraphUser[]> {
    if (userIds.length === 0) return []

    const allGraphUsers: GraphUser[] = []
    const batchSize = 999

    for (let i = 0; i < userIds.length; i += batchSize) {
      const batchIds = userIds.slice(i, i + batchSize)
      const filterString = batchIds.map(id => `id eq '${id}'`).join(' or ')
      let nextLink: string | undefined = `/users?$filter=${filterString}&$select=id,userPrincipalName,mail,givenName,surname`

      while (nextLink !== undefined) {
        const response = await this.client.api(nextLink).get() as { 'value': GraphUser[], '@odata.nextLink'?: string }
        allGraphUsers.push(...(response.value ?? []))
        nextLink = response['@odata.nextLink']
      }
    }

    return allGraphUsers
  }
}
