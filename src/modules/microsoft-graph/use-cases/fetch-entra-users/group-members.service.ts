import { Inject, Injectable } from '@nestjs/common'
import { Client } from '@microsoft/microsoft-graph-client'

interface GraphUser {
  id: string
}

@Injectable()
export class GroupMembersService {
  constructor (
        @Inject('MICROSOFT_GRAPH_CLIENT') private readonly client: Client
  ) { }

  async fetchAllUserIdsFromGroups (groupIds: string[]): Promise<string[]> {
    if (groupIds.length === 0) return []

    const groupMemberPromises = groupIds.map(groupId => this.fetchMemberIds(groupId))
    const results = await Promise.all(groupMemberPromises)
    return results.flat()
  }

  private async fetchMemberIds (groupId: string): Promise<string[]> {
    const memberIds: string[] = []
    let nextLink: string | undefined = `/groups/${groupId}/members`

    while (nextLink !== undefined) {
      const response = await this.client.api(nextLink).get() as {
        'value': GraphUser[]
        '@odata.nextLink'?: string
      }
      memberIds.push(...response.value.map(member => member.id))
      nextLink = response['@odata.nextLink']
    }

    return memberIds
  }
}
