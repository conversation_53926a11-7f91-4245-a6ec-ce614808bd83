import { Client } from '@microsoft/microsoft-graph-client'
import { Inject, Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { AppRoleAssignment } from 'microsoft-graph'
import { EntraUserResponse } from './entra-user.response.js'

interface GraphUser {
  id: string
  userPrincipalName: string
  mail: string
  givenName: string
  surname: string
}

@Injectable()
export class FetchEntraUsersUseCase {
  constructor (
    @Inject('MICROSOFT_GRAPH_CLIENT') private client: Client,
    private readonly configService: ConfigService
  ) {}

  async execute (search?: string): Promise<EntraUserResponse[]> {
    const servicePrincipalId = this.configService.getOrThrow<string>('MICROSOFT_ENTRA_SERVICE_PRINCIPAL_ID')
    const data = await this.client.api(`/servicePrincipals/${servicePrincipalId}/appRoleAssignedTo`).get() as { value: AppRoleAssignment[] }

    const userAssignments = (data.value ?? []).filter(
      (a: AppRoleAssignment) => a.principalType === 'User' && typeof a.principalId === 'string'
    )

    const users: GraphUser[] = await Promise.all(userAssignments.map(async (assignment) => {
      const user = await this.client.api(`/users/${assignment.principalId}`)
        .select('id,userPrincipalName,mail,givenName,surname')
        .get() as GraphUser
      return user
    }))

    const externalUsers = users.filter(
      u => typeof u.userPrincipalName === 'string' && !u.userPrincipalName.toLowerCase().endsWith('@indaver.com')
    )

    let results = externalUsers.map(u => new EntraUserResponse({
      upn: u.userPrincipalName,
      firstName: u.givenName,
      lastName: u.surname,
      email: u.mail,
      zitadelSub: u.id
    }))

    if (search) {
      const q = search.toLowerCase();
      results = results.filter(u =>
        (typeof u.upn === 'string' && u.upn.toLowerCase().includes(q))
        || (typeof u.firstName === 'string' && u.firstName.toLowerCase().includes(q))
        || (typeof u.lastName === 'string' && u.lastName.toLowerCase().includes(q))
        || (typeof u.email === 'string' && u.email.toLowerCase().includes(q))
      )
    }

    results.sort((a, b) => (a.lastName || '').localeCompare(b.lastName || ''))

    return results
  }
}
