import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { CronjobUseCase } from '../../../cronjobs/cronjob.use-case.js'
import { FetchEntraUsersUseCase } from '../../use-cases/fetch-entra-users/fetch-entra-users.use-case.js'
import { User } from '../../../../app/users/entities/user.entity.js'
import { UserEntityBuilder } from '../../../../app/users/tests/user-entity.builder.js'

@Injectable()
export class SyncEntraUsersCronjobUseCase implements CronjobUseCase {
  constructor (
    private readonly fetchEntraUsersUseCase: FetchEntraUsersUseCase,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {}

  async execute (): Promise<void> {
    const entraUsers = await this.fetchEntraUsersUseCase.execute()

    for (const entraUser of entraUsers) {
      const existingUser = await this.userRepository.findOne({
        where: {
          email: entraUser.email
        }
      })

      if (!existingUser) {
        const newUser = new UserEntityBuilder()
          .withEmail(entraUser.email)
          .withFirstName(entraUser.firstName)
          .withLastName(entraUser.lastName)
          .withZitadelSub(null)
          .withAzureEntraId(entraUser.zitadelSub ?? null)
          .withAzureEntraUpn(entraUser.upn)
          .build()

        await this.userRepository.insert(newUser)
      }
    }
  }
}
