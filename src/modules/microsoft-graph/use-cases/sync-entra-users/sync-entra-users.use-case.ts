import { Injectable } from '@nestjs/common'
import { CronjobUseCase } from '../../../cronjobs/cronjob.use-case.js'
import { FetchEntraUsersUseCase } from '../../use-cases/fetch-entra-users/fetch-entra-users.use-case.js'

@Injectable()
export class SyncEntraUsersCronjobUseCase implements CronjobUseCase {
  constructor (private readonly fetchEntraUsersUseCase: FetchEntraUsersUseCase) {}

  async execute (): Promise<void> {
    const users = await this.fetchEntraUsersUseCase.execute()
    console.log(`Fetched ${users.length} external Entra users.`)
    // Optionally: cache, notify, or process users here
  }
}
