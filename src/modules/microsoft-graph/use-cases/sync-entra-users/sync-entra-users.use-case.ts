import { Injectable } from '@nestjs/common'
import { UserRepository } from 'src/app/users/repositories/user.repository.js'
import { CronjobUseCase } from '../../../cronjobs/cronjob.use-case.js'
import { FetchEntraUsersUseCase } from '../../use-cases/fetch-entra-users/fetch-entra-users.use-case.js'

@Injectable()
export class SyncEntraUsersCronjobUseCase implements CronjobUseCase {
  constructor (private readonly fetchEntraUsersUseCase: FetchEntraUsersUseCase) {}

  async execute (): Promise<void> {
    const entraUsers = await this.fetchEntraUsersUseCase.execute()

    for (const entraUser of entraUsers) {
      const user = await this.userRepository.findOne({
        where: {
          email: entraUser.email
        }
      })
    }

  }
}
