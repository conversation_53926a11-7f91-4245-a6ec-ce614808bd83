import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { FetchEntraUsersModule } from '../fetch-entra-users/fetch-entra-users.module.js'
import { User } from '../../../../app/users/entities/user.entity.js'
import { SyncEntraUsersCronjobUseCase } from './sync-entra-users.use-case.js'

@Module({
  imports: [
    FetchEntraUsersModule,
    TypeOrmModule.forFeature([User])
  ],
  providers: [SyncEntraUsersCronjobUseCase],
  exports: [SyncEntraUsersCronjobUseCase]
})
export class FetchEntraUsersCronjobModule {}
