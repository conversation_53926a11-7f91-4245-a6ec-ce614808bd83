import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import Joi from 'joi'
import { FetchEntraUsersModule } from './use-cases/fetch-entra-users/fetch-entra-users.module.js'
import { FetchEntraUsersCronjobModule } from './use-cases/sync-entra-users/sync-entra-users-cronjob.module.js'
import { SyncEntraUsersController } from './controllers/sync-entra-users-controller.js'
import { GetUserAppRolesModule } from './use-cases/get-user-app-roles/get-user-app-roles.module.js'

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: process.env.ENV_FILE,
      validationSchema: Joi.object({
        MICROSOFT_GRAPH_TENANT_ID: Joi.string().required(),
        MICROSOFT_GRAPH_CLIENT_ID: Joi.string().required(),
        MICROSOFT_GRAPH_CLIENT_SECRET: Joi.string().required(),
        MICROSOFT_ENTRA_SERVICE_PRINCIPAL_ID: Joi.string().required()
      })
    }),
    GetUserAppRolesModule,
    FetchEntraUsersModule,
    FetchEntraUsersCronjobModule
  ],
  controllers: [
    SyncEntraUsersController
  ],
  exports: [
    GetUserAppRolesModule
  ]
})
export class MicrosoftGraphModule { }
