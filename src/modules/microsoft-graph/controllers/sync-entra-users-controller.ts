import { Controller, Post, UseGuards } from '@nestjs/common'
import { FetchEntraUsersUseCase } from '../use-cases/fetch-entra-users/fetch-entra-users.use-case.js'
import { AuthGuard } from '../../auth/guards/auth.guard.js'

@Controller('api/sync')
@UseGuards(AuthGuard)
export class SyncEntraUsersController {
  constructor (private readonly fetchEntraUsersUseCase: FetchEntraUsersUseCase) {}

  @Post('entra-users')
  async syncEntraUsers () {
    const users = await this.fetchEntraUsersUseCase.execute()
    return { message: 'Sync complete', count: users.length }
  }
}
