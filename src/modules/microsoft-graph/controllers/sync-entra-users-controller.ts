import { Controller, Post, UseGuards } from '@nestjs/common'
import { SyncEntraUsersCronjobUseCase } from '../use-cases/sync-entra-users/sync-entra-users.use-case.js'
import { AuthGuard } from '../../auth/guards/auth.guard.js'

@Controller('api/sync')
@UseGuards(AuthGuard)
export class SyncEntraUsersController {
    constructor(private readonly syncEntraUsersUseCase: SyncEntraUsersCronjobUseCase) { }

    @Post('entra-users')
    async syncEntraUsers() {
        await this.syncEntraUsersUseCase.execute()
        return { message: 'Sync triggered successfully' }
    }
}
