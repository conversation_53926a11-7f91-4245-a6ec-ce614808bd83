import { Controller, Get, Query, UseGuards } from '@nestjs/common'
import { ApiOkResponse } from '@nestjs/swagger'
import { AuthGuard } from '../../auth/guards/auth.guard.js'
import { FetchEntraUsersUseCase } from '../use-cases/fetch-entra-users/fetch-entra-users.use-case.js'
import { EntraUserResponse } from '../use-cases/fetch-entra-users/entra-user.response.js'

@Controller('api/entra-users')
@UseGuards(AuthGuard)
export class FetchEntraUsersController {
  constructor (private readonly fetchEntraUsersUseCase: FetchEntraUsersUseCase) {}

  @Get()
  @ApiOkResponse({ type: EntraUserResponse, isArray: true })
  async getEntraUsers (@Query('search') search?: string): Promise<EntraUserResponse[]> {
    return await this.fetchEntraUsersUseCase.execute(search)
  }
}
