import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { EntityPart } from '../../files/enums/entity-part.enum.js'
import { SapCreatePickUpRequestUseCase } from '../../sap/use-cases/create-pick-up-request/create-pick-up-request.use-case.js'
import { SapUploadAndCreateDocumentUseCase } from '../../sap/use-cases/upload-and-create-document/upload-and-create-document.use-case.js'
import { CreatePickUpRequestSapMapper } from '../use-cases/create-pick-up-request-sap/create-pick-up-request-sap.mapper.js'

@Injectable()
export class SubmitPickUpRequestService {
  constructor (
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly sapCreatePickUpRequest: SapCreatePickUpRequestUseCase,
    private readonly sapCreateAndUploadDocument: SapUploadAndCreateDocumentUseCase
  ) {}

  async execute (
    pickUpRequest: PickUpRequest,
    weeklyPlanningId?: string
  ): Promise<string> {
    assert(pickUpRequest.additionalFiles !== undefined)

    let customerSalesOrganization: string | null = null
    if (pickUpRequest.customerId !== null) {
      customerSalesOrganization = await this.getCustomerSalesOrganisationId.getOrganisationId(
        pickUpRequest.customerId
      )
    }

    const sapCommand = CreatePickUpRequestSapMapper.mapSubmittedPickUpRequestToSapCommand(
      pickUpRequest,
      customerSalesOrganization,
      weeklyPlanningId
    )

    const sapResponse = await this.sapCreatePickUpRequest.execute(
      sapCommand
    )

    await Promise.all(
      pickUpRequest.additionalFiles.map(async (fileLink) => {
        assert(fileLink.file !== undefined)
        await this.sapCreateAndUploadDocument.execute(
          sapResponse.d.Reqno,
          fileLink.file,
          fileLink.entityPart as EntityPart
        )
      })
    )

    return sapResponse.d.Reqno
  }
}
