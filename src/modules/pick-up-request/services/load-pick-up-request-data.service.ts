import { Injectable } from '@nestjs/common'
import { MapContractLineSapService } from '../../contract-line/services/map-contract-line.service.js'
import { CustomerNotFoundError } from '../../customer/errors/customer-not-found.error.js'
import { MapCustomerSapService } from '../../customer/services/map-customer-sap.service.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { EntityPart } from '../../files/enums/entity-part.enum.js'
import { FileLinkService } from '../../files/services/file-link.service.js'
import { MapPickUpAddressSapService } from '../../pick-up-address/services/map-pick-up-address.service.js'
import { SapQuery } from '../../sap/query/sap-query.js'
import { SapFilterGroup } from '../../sap/query/types/sap-filter-group.js'
import { SapGetContractLineIndexResponse } from '../../sap/use-cases/get-contract-line-index/get-contract-line-index.response.js'
import { SapGetContractLineIndexUseCase } from '../../sap/use-cases/get-contract-line-index/get-contract-line-index.use-case.js'
import { SapGetCustomerIndexResponse } from '../../sap/use-cases/get-customer-index/get-customer-index.response.js'
import { SapGetCustomerIndexUseCase } from '../../sap/use-cases/get-customer-index/get-customer-index.use-case.js'
import { SapGetPickUpAddressIndexResponse } from '../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.response.js'
import { SapGetPickUpAddressIndexUseCase } from '../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.use-case.js'
import { SapGetWasteProducerIndexResponse } from '../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.response.js'
import { SapGetWasteProducerIndexUseCase } from '../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.use-case.js'
import { WasteProducerNotFoundError } from '../../waste-producer/errors/waste-producer-not-found.error.js'
import { MapWasteProducerSapService } from '../../waste-producer/services/map-waste-producer.service.js'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { PackagingRequestMaterial } from '../types/packaging-request-material.type.js'
import { PickUpRequestMaterial } from '../types/pick-up-request-material.type.js'

@Injectable()
export class LoadPickUpRequestDataService {
  constructor (
    private readonly sapGetContractLineIndex: SapGetContractLineIndexUseCase,
    private readonly sapGetCustomerIndex: SapGetCustomerIndexUseCase,
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly sapGetWasteProducerIndex: SapGetWasteProducerIndexUseCase,
    private readonly sapGetPickUpAddressIndex: SapGetPickUpAddressIndexUseCase,
    private readonly fileLinkService: FileLinkService
  ) {}

  async execute (pickUpRequest: PickUpRequest): Promise<void> {
    await Promise.all([
      this.loadCustomer(pickUpRequest),
      this.loadWasteProducer(pickUpRequest),
      this.loadPickUpAddresses(pickUpRequest),
      this.loadContracts(pickUpRequest),
      this.loadFiles(pickUpRequest)
    ])
  }

  private async loadCustomer (pickUpRequest: PickUpRequest): Promise<void> {
    pickUpRequest.customer = null

    if (pickUpRequest.customerId === null) return

    const sapQuery = new SapQuery<SapGetCustomerIndexResponse>()
      .where('Customer', pickUpRequest.customerId)
    const sapResult = await this.sapGetCustomerIndex.execute(sapQuery)

    if (sapResult.items.length === 0) throw new CustomerNotFoundError(pickUpRequest.customerId)

    pickUpRequest.customer = MapCustomerSapService.mapResultToCustomer(sapResult.items[0])
  }

  private async loadWasteProducer (pickUpRequest: PickUpRequest): Promise<void> {
    pickUpRequest.wasteProducer = null

    if (pickUpRequest.customerId === null || pickUpRequest.wasteProducerId === null) return

    const customerSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        pickUpRequest.customerId
      )

    const sapQuery = new SapQuery<SapGetWasteProducerIndexResponse>()
      .where('Customer', pickUpRequest.customerId)
      .andWhere('SalesOrganization', customerSalesOrganisationId)
      .andWhere('WasteProducer', pickUpRequest.wasteProducerId)
    const sapResult = await this.sapGetWasteProducerIndex.execute(sapQuery)

    if (sapResult.items.length === 0) {
      throw new WasteProducerNotFoundError(pickUpRequest.wasteProducerId)
    }

    pickUpRequest.wasteProducer = MapWasteProducerSapService.mapResultToWasteProducer(
      sapResult.items[0]
    )
  }

  private async loadPickUpAddresses (pickUpRequest: PickUpRequest): Promise<void> {
    pickUpRequest.pickUpAddresses = []

    if (pickUpRequest.customerId === null || pickUpRequest.pickUpAddressIds.length === 0) return

    const customerSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        pickUpRequest.customerId
      )

    const sapQuery = new SapQuery<SapGetPickUpAddressIndexResponse>()
      .where('Customer', pickUpRequest.customerId)
      .andWhere('SalesOrganization', customerSalesOrganisationId)
      .andWhere((qb) => {
        qb.where('PickUpAddress', pickUpRequest.pickUpAddressIds[0])

        for (let i = 1; i < pickUpRequest.pickUpAddressIds.length; i++) {
          qb.orWhere('PickUpAddress', pickUpRequest.pickUpAddressIds[i])
        }

        return qb
      })
    const sapResult = await this.sapGetPickUpAddressIndex.execute(sapQuery)

    pickUpRequest.pickUpAddresses = MapPickUpAddressSapService.mapResultsToPickUpAddresses(
      sapResult.items
    )
  }

  private getContractLineCondition (
    sapFilterGroup: SapFilterGroup<SapGetContractLineIndexResponse>,
    material: PickUpRequestMaterial | PackagingRequestMaterial
  ): SapFilterGroup<SapGetContractLineIndexResponse> {
    if ('quantity' in material) { // PackagingRequestMaterial
      sapFilterGroup.where((qb) => {
        return qb.where('MaterialType', 'YPAC')
          .orWhere('MaterialType', 'YPAA')
      })
    } else {
      sapFilterGroup.where('MaterialType', 'YWAS')
    }

    sapFilterGroup
      .andWhere('Vbeln', material.contractNumber)
      .andWhere('Posnr', material.contractItem)

    if ('tcNumber' in material && material.tcNumber != null) {
      sapFilterGroup.andWhere('ActionNr', material.tcNumber)
    }

    return sapFilterGroup
  }

  private async loadContracts (pickUpRequest: PickUpRequest): Promise<void> {
    pickUpRequest.materialsWithContractLine = []

    if (
      pickUpRequest.materials.length === 0
      && pickUpRequest.packagingRequestMaterials.length === 0
    ) return

    const query = new SapQuery<SapGetContractLineIndexResponse>()
      .where((qb) => {
        const materials = [
          ...pickUpRequest.materials,
          ...pickUpRequest.packagingRequestMaterials
        ]

        for (let i = 0; i < materials.length; i++) {
          if (i === 0) {
            qb = qb.where((qb1) => {
              return this.getContractLineCondition(qb1, materials[i])
            })
          } else {
            qb = qb.orWhere((qb1) => {
              return this.getContractLineCondition(qb1, materials[i])
            })
          }
        }
        return qb
      })
    const sapResult = await this.sapGetContractLineIndex.execute(query)

    const contractLines = MapContractLineSapService.mapResultsToContractLines(sapResult)

    pickUpRequest.materialsWithContractLine = pickUpRequest.materials.map(
      (material) => {
        const contractLine = contractLines.find(
          contractLine => contractLine.contractLineId === material.contractLineId
        ) ?? null

        return {
          ...material,
          contractLine,
          contractLineAccessible: contractLine !== null
        }
      })

    pickUpRequest.packagingRequestMaterials = pickUpRequest.packagingRequestMaterials.map(
      (material) => {
        const contractLine = contractLines.find(
          contractLine => contractLine.contractLineId === material.contractLineId
        ) ?? null

        return {
          ...material,
          contractLineAccessible: contractLine !== null
        }
      })
  }

  private async loadFiles (pickUpRequest: PickUpRequest): Promise<void> {
    pickUpRequest.additionalFiles = await this.fileLinkService.loadFileLinks(
      pickUpRequest.uuid,
      PickUpRequest.name,
      EntityPart.ADDITIONAL
    )
  }
}
