import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { LoadPickUpRequestDataService } from '../load-pick-up-request-data.service.js'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { PickUpRequestEntityBuilder } from '../../tests/pick-up-request-entity.builder.js'
import { FileLinkService } from '../../../files/services/file-link.service.js'
import { SapGetContractLineIndexUseCase } from '../../../sap/use-cases/get-contract-line-index/get-contract-line-index.use-case.js'
import { PickUpRequestMaterialBuilder } from '../../tests/pick-up-request-material.builder.js'
import { SapGetCustomerIndexUseCase } from '../../../sap/use-cases/get-customer-index/get-customer-index.use-case.js'
import { SapPaginatedResponseBuilder } from '../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetCustomerIndexResponse } from '../../../sap/use-cases/get-customer-index/get-customer-index.response.js'
import { SapGetWasteProducerIndexUseCase } from '../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.use-case.js'
import { SapGetWasteProducerIndexResponse } from '../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.response.js'
import { SapGetPickUpAddressIndexUseCase } from '../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.use-case.js'
import { SapGetPickUpAddressIndexResponse } from '../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.response.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { GetCustomerIndexResponseBuilder } from '../../../sap/use-cases/get-customer-index/tests/get-customer-index.response.builder.js'
import { GetWasteProducerIndexResponseBuilder } from '../../../sap/use-cases/get-waste-producer-index/tests/get-waste-producer-index.response.builder.js'
import { GetPickUpAddressIndexResponseBuilder } from '../../../sap/use-cases/get-pick-up-address-index/tests/get-pick-up-address-index.response.builder.js'
import { ValidPackagingRequestMaterialBuilder } from '../../../packaging-request/tests/valid-packaging-request-material.builder.js'

describe('LoadPickUpRequestDataService unit test', () => {
  let service: LoadPickUpRequestDataService

  let pickUpRequest: PickUpRequest

  let sapGetContractLineIndex: SinonStubbedInstance<SapGetContractLineIndexUseCase>
  let sapGetCustomerIndex: SinonStubbedInstance<SapGetCustomerIndexUseCase>
  let salesOrganisationUseCase: SinonStubbedInstance<GetCustomerDefaultSalesOrganisationIdUseCase>
  let sapGetWasteProducerIndex: SinonStubbedInstance<SapGetWasteProducerIndexUseCase>
  let sapGetPickUpAddressIndex: SinonStubbedInstance<SapGetPickUpAddressIndexUseCase>
  let fileLinkService: SinonStubbedInstance<FileLinkService>

  before(() => {
    TestBench.setupUnitTest()

    pickUpRequest = new PickUpRequestEntityBuilder().build()

    sapGetContractLineIndex = createStubInstance(SapGetContractLineIndexUseCase, {
      execute: Promise.resolve([])
    })

    sapGetCustomerIndex = createStubInstance(SapGetCustomerIndexUseCase)
    salesOrganisationUseCase = createStubInstance(GetCustomerDefaultSalesOrganisationIdUseCase)
    sapGetWasteProducerIndex = createStubInstance(SapGetWasteProducerIndexUseCase)
    sapGetPickUpAddressIndex = createStubInstance(SapGetPickUpAddressIndexUseCase)
    fileLinkService = createStubInstance(FileLinkService, {
      loadFileLinks: Promise.resolve([])
    })

    service = new LoadPickUpRequestDataService(
      sapGetContractLineIndex,
      sapGetCustomerIndex,
      salesOrganisationUseCase,
      sapGetWasteProducerIndex,
      sapGetPickUpAddressIndex,
      fileLinkService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    Sinon.resetHistory()

    sapGetCustomerIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetCustomerIndexResponse>()
        .addItem(
          new GetCustomerIndexResponseBuilder().build()
        )
        .build()
    )
    salesOrganisationUseCase.getOrganisationIdOrFail.resolves(randomUUID())
    sapGetWasteProducerIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetWasteProducerIndexResponse>()
        .addItem(
          new GetWasteProducerIndexResponseBuilder().build()
        )
        .build()
    )
    sapGetPickUpAddressIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetPickUpAddressIndexResponse>()
        .addItem(
          new GetPickUpAddressIndexResponseBuilder().build()
        )
        .build()
    )
  }

  it('Calls all methods once', async () => {
    await service.execute(pickUpRequest)

    assert.calledOnce(fileLinkService.loadFileLinks)
  })

  it('Calls sapGetCustomerIndex.execute when customerId not null', async () => {
    pickUpRequest.customerId = randomUUID()

    await service.execute(pickUpRequest)

    assert.calledOnce(sapGetCustomerIndex.execute)
  })

  it('Calls sapGetWasteProducerIndex.execute when customerId and wasteProducerId not null', async () => {
    pickUpRequest.customerId = randomUUID()
    pickUpRequest.wasteProducerId = randomUUID()

    await service.execute(pickUpRequest)

    assert.calledOnce(sapGetWasteProducerIndex.execute)
  })

  it('Calls sapGetPickUpAddressIndex.execute when customerId not null and pickUpAddressIds not empty', async () => {
    pickUpRequest.customerId = randomUUID()
    pickUpRequest.pickUpAddressIds = [randomUUID()]

    await service.execute(pickUpRequest)

    assert.calledOnce(sapGetPickUpAddressIndex.execute)
  })

  it('Calls sapGetContractLineIndex.execute when materials not empty', async () => {
    pickUpRequest.materials = [new PickUpRequestMaterialBuilder().build()]

    await service.execute(pickUpRequest)

    assert.calledOnce(sapGetContractLineIndex.execute)
  })

  it('Calls sapGetContractLineIndex.execute when packagingRequestMaterials not empty', async () => {
    pickUpRequest.packagingRequestMaterials = [new ValidPackagingRequestMaterialBuilder().build()]

    await service.execute(pickUpRequest)

    assert.calledOnce(sapGetContractLineIndex.execute)
  })
})
