import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { randNumber } from '@ngneat/falso'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { SubmitPickUpRequestService } from '../submit-pick-up-request.service.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapCreatePickUpRequestUseCase } from '../../../sap/use-cases/create-pick-up-request/create-pick-up-request.use-case.js'
import { SapUploadAndCreateDocumentUseCase } from '../../../sap/use-cases/upload-and-create-document/upload-and-create-document.use-case.js'
import { EntityPart } from '../../../files/enums/entity-part.enum.js'
import { FileLink } from '../../../files/entities/file-link.entity.js'
import { File } from '../../../files/entities/file.entity.js'
import { MimeType } from '../../../files/enums/mime-type.enum.js'
import { SapCreatePickUpRequestResponseBuilder } from '../../../sap/use-cases/create-pick-up-request/tests/create-pick-up-request.response.builder.js'
import { ValidPickUpRequestEntityBuilder } from '../../tests/valid-pick-up-request-entity.builder.js'
import { UserEntityBuilder } from '../../../../app/users/tests/user-entity.builder.js'

describe('SubmitPickUpRequestService unit test', () => {
  let service: SubmitPickUpRequestService

  let pickUpRequest: PickUpRequest
  let inquiryNumber: string
  let salesOrganizationId: string
  let requestNumber: string
  let file: File
  let fileLink: FileLink

  let getCustomerSalesOrganisationId: SinonStubbedInstance<
    GetCustomerDefaultSalesOrganisationIdUseCase
  >
  let sapCreatePickUpRequest: SinonStubbedInstance<SapCreatePickUpRequestUseCase>
  let sapCreateAndUploadDocument: SinonStubbedInstance<SapUploadAndCreateDocumentUseCase>

  before(() => {
    TestBench.setupUnitTest()

    pickUpRequest = new ValidPickUpRequestEntityBuilder()
      .createdByUser(new UserEntityBuilder().build())
      .build()
    inquiryNumber = randNumber({ min: 10000000, max: 99999999 }).toString()
    salesOrganizationId = randomUUID()
    requestNumber = randNumber({ min: 10000000, max: 99999999 }).toString()

    file = new File()
    file.uuid = randomUUID()
    file.name = 'test-file.pdf'
    file.mimeType = MimeType.PDF

    fileLink = new FileLink()
    fileLink.uuid = randomUUID()
    fileLink.entityPart = EntityPart.ADDITIONAL
    fileLink.file = file

    pickUpRequest.additionalFiles = [fileLink]

    getCustomerSalesOrganisationId = createStubInstance(
      GetCustomerDefaultSalesOrganisationIdUseCase
    )
    sapCreatePickUpRequest = createStubInstance(SapCreatePickUpRequestUseCase)
    sapCreateAndUploadDocument = createStubInstance(SapUploadAndCreateDocumentUseCase)

    service = new SubmitPickUpRequestService(
      getCustomerSalesOrganisationId,
      sapCreatePickUpRequest,
      sapCreateAndUploadDocument
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    getCustomerSalesOrganisationId.getOrganisationId.resolves(salesOrganizationId)

    sapCreatePickUpRequest.execute.resolves(
      new SapCreatePickUpRequestResponseBuilder()
        .withRequestNumber(requestNumber)
        .build()
    )

    sapCreateAndUploadDocument.execute.resolves()
  }

  it('Gets customer sales organization ID when customerId is not null', async () => {
    pickUpRequest.customerId = randomUUID()

    await service.execute(pickUpRequest)

    assert.calledOnceWithExactly(
      getCustomerSalesOrganisationId.getOrganisationId,
      pickUpRequest.customerId
    )
  })

  it('Does not get customer sales organization ID when customerId is null', async () => {
    pickUpRequest.customerId = null

    await service.execute(pickUpRequest)

    assert.notCalled(getCustomerSalesOrganisationId.getOrganisationId)
  })

  it('Creates SAP pick-up request with correct parameters', async () => {
    pickUpRequest.customerId = randomUUID()

    await service.execute(pickUpRequest, inquiryNumber)

    assert.calledOnce(sapCreatePickUpRequest.execute)
  })

  it('Uploads additional files to SAP', async () => {
    await service.execute(pickUpRequest)

    assert.calledOnceWithExactly(
      sapCreateAndUploadDocument.execute,
      requestNumber,
      Sinon.match.same(fileLink.file),
      fileLink.entityPart as EntityPart
    )
  })

  it('Uploads multiple additional files to SAP', async () => {
    const file2 = new File()
    file2.uuid = randomUUID()
    file2.name = 'test-file-2.pdf'
    file2.mimeType = MimeType.PDF

    const fileLink2 = new FileLink()
    fileLink2.uuid = randomUUID()
    fileLink2.entityPart = EntityPart.ADDITIONAL
    fileLink2.file = file2

    pickUpRequest.additionalFiles = [fileLink, fileLink2]

    await service.execute(pickUpRequest)

    assert.calledTwice(sapCreateAndUploadDocument.execute)
    assert.calledWithExactly(
      sapCreateAndUploadDocument.execute.firstCall,
      requestNumber,
      Sinon.match.same(fileLink.file),
      fileLink.entityPart as EntityPart
    )
    assert.calledWithExactly(
      sapCreateAndUploadDocument.execute.secondCall,
      requestNumber,
      Sinon.match.same(fileLink2.file),
      fileLink2.entityPart as EntityPart
    )
  })

  it('Does not attempt to upload files when additionalFiles is empty', async () => {
    pickUpRequest.additionalFiles = []

    await service.execute(pickUpRequest)

    assert.notCalled(sapCreateAndUploadDocument.execute)
  })

  it('Returns the request number from SAP response', async () => {
    const result = await service.execute(pickUpRequest)

    assert.match(result, requestNumber)
  })

  it('Passes weekly planning ID to mapper when provided', async () => {
    await service.execute(pickUpRequest, inquiryNumber)

    assert.calledOnce(sapCreatePickUpRequest.execute)
  })
})
