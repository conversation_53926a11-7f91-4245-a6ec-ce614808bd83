import assert from 'assert'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { UniformPickUpRequest } from '../types/uniform-pick-up-request.type.js'
import { PickUpRequestStatus } from '../enums/pick-up-request-status.enum.js'
import { PackagingRequestMode } from '../enums/pick-up-transport-mode.enum.js'

export class MapUniformPickUpRequestDbService {
  static mapResultToUniformPickUpRequest (
    pickUpRequest: PickUpRequest
  ): UniformPickUpRequest {
    assert(pickUpRequest.createdByUser !== undefined)

    return {
      uuid: pickUpRequest.uuid,
      requestNumber: pickUpRequest.requestNumber ?? null,
      status: pickUpRequest.submittedOn === null
        ? PickUpRequestStatus.DRAFT
        : PickUpRequestStatus.PENDING,
      wasteMaterial: null, // TODO https://linear.app/wisemen/issue/IND-577/as-a-user-i-can-add-all-supported-fields-to-the-pickup-overview#comment-3e4d80be
      customerId: pickUpRequest.customerId,
      customerName: pickUpRequest.customer?.name ?? null,
      wasteProducerId: pickUpRequest.wasteProducerId,
      wasteProducerName: pickUpRequest.wasteProducer?.name ?? null,
      pickUpAddressId: null, // TODO https://linear.app/wisemen/issue/IND-577/as-a-user-i-can-add-all-supported-fields-to-the-pickup-overview#comment-3e4d80be
      pickUpAddressName: null, // TODO https://linear.app/wisemen/issue/IND-577/as-a-user-i-can-add-all-supported-fields-to-the-pickup-overview#comment-3e4d80be
      customerReference: null,
      contractNumber: pickUpRequest.materials.length > 0
        ? pickUpRequest.materials.map(material => material.contractLineId).join(', ')
        : null, // TODO https://linear.app/wisemen/issue/IND-577/as-a-user-i-can-add-all-supported-fields-to-the-pickup-overview#comment-3e4d80be
      contractItem: null, // TODO https://linear.app/wisemen/issue/IND-577/as-a-user-i-can-add-all-supported-fields-to-the-pickup-overview#comment-3e4d80be
      transportMode: !pickUpRequest.isOnlyPackagingRequest
        ? pickUpRequest.transportMode
        : PackagingRequestMode.PACKAGING_REQUEST_ORDER,
      dateOfRequest: null,
      treatmentCenterName: null,
      accountManager: null,
      costCenter: null, // TODO https://linear.app/wisemen/issue/IND-577/as-a-user-i-can-add-all-supported-fields-to-the-pickup-overview#comment-3e4d80be
      isTransportByIndaver: pickUpRequest.isTransportByIndaver,
      requestedStartDate: pickUpRequest.startDate,
      requestedEndDate: pickUpRequest.endDate,
      confirmedTransportDate: null,
      salesOrder: null,
      isHazardous: null, // TODO https://linear.app/wisemen/issue/IND-577/as-a-user-i-can-add-all-supported-fields-to-the-pickup-overview#comment-3e4d80be
      nameOfApplicant: pickUpRequest.createdByUser?.fullName,
      orderNumber: null, // TODO https://linear.app/wisemen/issue/IND-577/as-a-user-i-can-add-all-supported-fields-to-the-pickup-overview#comment-3e4d80be
      containerNumber: null, // TODO https://linear.app/wisemen/issue/IND-577/as-a-user-i-can-add-all-supported-fields-to-the-pickup-overview#comment-3e4d80be
      materialAnalysis: null,
      deliveryInfo: null,
      nameInstallation: null,
      disposalCertificateNumber: null,
      ewc: null,
      tfsNumber: null
    }
  }

  static mapResultsToUniformPickUpRequests (
    pickUpRequests: PickUpRequest[]
  ): UniformPickUpRequest[] {
    return pickUpRequests.map(pickUpRequest => this.mapResultToUniformPickUpRequest(pickUpRequest))
  }
}
