import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { CustomerModule } from '../../customer/customer.module.js'
import { WasteProducerModule } from '../../waste-producer/waste-producer.module.js'
import { PickUpAddressModule } from '../../pick-up-address/pick-up-address.module.js'
import { ContractLineModule } from '../../contract-line/contract-line.module.js'
import { FileModule } from '../../files/file.module.js'
import { SapModule } from '../../sap/sap.module.js'
import { AuthModule } from '../../auth/auth.module.js'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { LoadPickUpRequestDataService } from './load-pick-up-request-data.service.js'
import { PickUpRequestTemplateRepository } from './pick-up-request-template.repository.js'
import { SubmitPickUpRequestService } from './submit-pick-up-request.service.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PickUpRequest
    ]),
    CustomerModule,
    WasteProducerModule,
    PickUpAddressModule,
    ContractLineModule,
    SapModule,
    FileModule,
    AuthModule
  ],
  providers: [
    LoadPickUpRequestDataService,
    PickUpRequestTemplateRepository,
    SubmitPickUpRequestService
  ],
  exports: [
    LoadPickUpRequestDataService,
    PickUpRequestTemplateRepository,
    SubmitPickUpRequestService
  ]
})
export class PickUpRequestServicesModule {}
