import { Injectable } from '@nestjs/common'
import { SapGetIsPoReferenceRequiredUseCase } from '../../../sap/use-cases/get-is-po-reference-required/get-is-po-reference-required.use-case.js'
import { SapGetIsCostCenterRequiredUseCase } from '../../../sap/use-cases/get-is-cost-center-required/get-is-cost-center-required.use-case.js'
import { GetIsPoNumberAndCostCenterRequiredQuery } from './query/get-is-po-number-and-cost-center-required.query.js'
import { GetIsPoNumberAndCostCenterRequiredResponse } from './get-is-po-number-and-cost-center-required.response.js'
import { GetIsPoNumberAndCostCenterRequiredValidator } from './get-is-po-number-and-cost-center-required.validator.js'

@Injectable()
export class GetIsPoNumberAndCostCenterRequiredUseCase {
  constructor (
    private readonly validator: GetIsPoNumberAndCostCenterRequiredValidator,
    private readonly isPoReferenceRequiredUseCase: SapGetIsPoReferenceRequiredUseCase,
    private readonly isCostCenterRequiredUseCase: SapGetIsCostCenterRequiredUseCase
  ) {}

  public async execute (
    query: GetIsPoNumberAndCostCenterRequiredQuery
  ): Promise<GetIsPoNumberAndCostCenterRequiredResponse> {
    this.validator.execute(query)

    const [isPoNumberRequired, isCostCenterRequired] = await Promise.all([
      this.isPoReferenceRequiredUseCase.execute(query.filter.customerId),
      this.isCostCenterRequiredUseCase.execute(query.filter.customerId)
    ])

    return new GetIsPoNumberAndCostCenterRequiredResponse(
      isPoNumberRequired,
      isCostCenterRequired
    )
  }
}
