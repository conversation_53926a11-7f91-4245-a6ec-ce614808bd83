import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { SelectedCustomerFilterMismatchError } from '../../../customer/errors/selected-customer-filter-mismatch.error.js'
import { GetIsPoNumberAndCostCenterRequiredQuery } from './query/get-is-po-number-and-cost-center-required.query.js'

@Injectable()
export class GetIsPoNumberAndCostCenterRequiredValidator {
  constructor (
    private readonly authContext: AuthContext
  ) {}

  execute (query: GetIsPoNumberAndCostCenterRequiredQuery): void {
    this.validateCustomer(query.filter.customerId)
  }

  private validateCustomer (customerId: string): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== customerId) {
      throw new SelectedCustomerFilterMismatchError({ pointer: '$.customerId' })
    }
  }
}
