import { afterEach, before, describe, it } from 'node:test'
import Sinon, { SinonStubbedInstance, createStubInstance, assert } from 'sinon'
import { GetIsPoNumberAndCostCenterRequiredUseCase } from '../get-is-po-number-and-cost-center-required.use-case.js'
import { GetIsPoNumberAndCostCenterRequiredValidator } from '../get-is-po-number-and-cost-center-required.validator.js'
import { SapGetIsPoReferenceRequiredUseCase } from '../../../../sap/use-cases/get-is-po-reference-required/get-is-po-reference-required.use-case.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { GetIsPoNumberAndCostCenterRequiredQuery } from '../query/get-is-po-number-and-cost-center-required.query.js'
import { SapGetIsCostCenterRequiredUseCase } from '../../../../sap/use-cases/get-is-cost-center-required/get-is-cost-center-required.use-case.js'
import { GetIsPoNumberAndCostCenterRequiredQueryBuilder } from './get-is-po-number-and-cost-center-required.query.builder.js'

describe('Get is PO number and cost center required use-case unit test', () => {
  let useCase: GetIsPoNumberAndCostCenterRequiredUseCase
  let query: GetIsPoNumberAndCostCenterRequiredQuery
  let validator: SinonStubbedInstance<GetIsPoNumberAndCostCenterRequiredValidator>
  let sapGetIsPoReferenceRequired: SinonStubbedInstance<SapGetIsPoReferenceRequiredUseCase>
  let sapGetIsCostCenterRequired: SinonStubbedInstance<SapGetIsCostCenterRequiredUseCase>

  before(() => {
    TestBench.setupUnitTest()

    query = new GetIsPoNumberAndCostCenterRequiredQueryBuilder().build()

    validator = createStubInstance(GetIsPoNumberAndCostCenterRequiredValidator)
    sapGetIsPoReferenceRequired = createStubInstance(SapGetIsPoReferenceRequiredUseCase)
    sapGetIsCostCenterRequired = createStubInstance(SapGetIsCostCenterRequiredUseCase)

    useCase = new GetIsPoNumberAndCostCenterRequiredUseCase(
      validator,
      sapGetIsPoReferenceRequired,
      sapGetIsCostCenterRequired
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    validator.execute.returns()
    sapGetIsPoReferenceRequired.execute.resolves(true)
    sapGetIsCostCenterRequired.execute.resolves(true)
  }

  it('Call all methods once', async () => {
    await useCase.execute(query)

    assert.calledOnce(validator.execute)
    assert.calledOnce(sapGetIsPoReferenceRequired.execute)
    assert.calledOnce(sapGetIsCostCenterRequired.execute)
  })
})
