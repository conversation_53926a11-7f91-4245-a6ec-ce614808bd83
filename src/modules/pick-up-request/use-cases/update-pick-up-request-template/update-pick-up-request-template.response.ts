import { ApiProperty } from '@nestjs/swagger'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'

export class UpdatePickUpRequestTemplateResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  constructor (pickUpRequest: PickUpRequest) {
    this.uuid = pickUpRequest.uuid
    this.createdAt = pickUpRequest.createdAt.toISOString()
    this.updatedAt = pickUpRequest.updatedAt.toISOString()
  }
}
