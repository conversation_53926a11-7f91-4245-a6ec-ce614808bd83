import { ApiProperty } from '@nestjs/swagger'
import { RegisterDomainEvent } from '../../../domain-events/register-domain-event.decorator.js'
import { DomainEventType } from '../../../domain-events/domain-event-type.js'
import { PickUpRequestEvent } from '../../events/pick-up-request.event.js'

export class PickUpRequestTemplateUpdatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly pickUpRequestUuid: string

  constructor (pickUpRequestUuid: string) {
    this.pickUpRequestUuid = pickUpRequestUuid
  }
}

@RegisterDomainEvent(DomainEventType.PICK_UP_REQUEST_TEMPLATE_UPDATED, 1)
export class PickUpRequestTemplateUpdatedEvent
  extends PickUpRequestEvent<PickUpRequestTemplateUpdatedEventContent> {
  constructor (pickUpRequestUuid: string) {
    super({
      pickUpRequestUuid: pickUpRequestUuid,
      content: new PickUpRequestTemplateUpdatedEventContent(pickUpRequestUuid)
    })
  }
}
