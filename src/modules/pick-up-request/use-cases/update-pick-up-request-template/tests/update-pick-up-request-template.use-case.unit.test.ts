import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { UpdatePickUpRequestTemplateUseCase } from '../update-pick-up-request-template.use-case.js'
import { UpdatePickUpRequestValidator } from '../../../validators/update-pick-up-request.validator.js'
import { PickUpRequest } from '../../../entities/pick-up-request.entity.js'
import { PickUpRequestEntityBuilder } from '../../../tests/pick-up-request-entity.builder.js'
import { FileLinkService } from '../../../../files/services/file-link.service.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { PickUpRequestTemplateUpdatedEvent } from '../pick-up-request-template-updated.event.js'
import { PickUpRequestTemplateRepository } from '../../../services/pick-up-request-template.repository.js'
import { UpdatePickUpRequestTemplateCommandBuilder } from './update-pick-up-request-template-command.builder.js'

describe('Update pick-up request template use-case unit test', () => {
  let useCase: UpdatePickUpRequestTemplateUseCase

  let userUuid: string
  let pickUpRequestUuid: string

  let validator: SinonStubbedInstance<UpdatePickUpRequestValidator>
  let fileLinkService: SinonStubbedInstance<FileLinkService>
  let pickUpRequestRepository: SinonStubbedInstance<Repository<PickUpRequest>>
  let pickUpRequestTemplateService: SinonStubbedInstance<PickUpRequestTemplateRepository>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()
    pickUpRequestUuid = randomUUID()

    const authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: userUuid
    })

    validator = createStubInstance(UpdatePickUpRequestValidator)
    fileLinkService = createStubInstance(FileLinkService)
    pickUpRequestTemplateService = createStubInstance(PickUpRequestTemplateRepository)

    const pickUpRequest = new PickUpRequestEntityBuilder()
      .withUuid(pickUpRequestUuid)
      .createdByUserUuid(userUuid)
      .withTemplateName('Test Template')
      .build()

    pickUpRequestRepository = createStubInstance<Repository<PickUpRequest>>(
      Repository<PickUpRequest>, {
        findOneOrFail: Promise.resolve(pickUpRequest)
      }
    )

    pickUpRequestRepository.update.resolves({
      raw: {},
      affected: 1,
      generatedMaps: []
    })

    pickUpRequestTemplateService.findPickUpRequestTemplateOrFail.resolves(
      pickUpRequest
    )

    fileLinkService.sync.resolves()

    eventEmitter = createStubInstance(DomainEventEmitter)

    useCase = new UpdatePickUpRequestTemplateUseCase(
      stubDataSource(),
      authContext,
      validator,
      pickUpRequestRepository,
      pickUpRequestTemplateService,
      fileLinkService,
      eventEmitter
    )
  })

  it('Calls all methods once', async () => {
    const command = new UpdatePickUpRequestTemplateCommandBuilder()
      .withTemplateName('Updated Template')
      .addAdditionalFile({
        fileUuid: randomUUID(),
        order: 1
      })
      .build()

    await useCase.execute(pickUpRequestUuid, command)

    assert.calledOnce(validator.validate)
    assert.calledOnce(pickUpRequestRepository.update)
    assert.calledOnce(pickUpRequestRepository.findOneOrFail)
    assert.calledOnce(fileLinkService.sync)
  })

  it('Emits a PickUpRequestTemplateUpdatedEvent event', async () => {
    const command = new UpdatePickUpRequestTemplateCommandBuilder()
      .withTemplateName('Updated Template')
      .build()

    await useCase.execute(pickUpRequestUuid, command)

    expect(eventEmitter).toHaveEmitted(new PickUpRequestTemplateUpdatedEvent(pickUpRequestUuid))
  })
})
