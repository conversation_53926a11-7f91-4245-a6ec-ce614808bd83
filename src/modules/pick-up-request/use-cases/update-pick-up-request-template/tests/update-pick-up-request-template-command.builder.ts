import { CreateFileLinkCommand } from '../../../../files/commands/create-file-link.command.js'
import { UpdatePickUpRequestTemplateCommand } from '../update-pick-up-request-template.command.js'

export class UpdatePickUpRequestTemplateCommandBuilder {
  private command: UpdatePickUpRequestTemplateCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new UpdatePickUpRequestTemplateCommand()
    return this
  }

  withTemplateName (templateName: string): this {
    this.command.templateName = templateName
    return this
  }

  addAdditionalFile (file: CreateFileLinkCommand): this {
    if (this.command.additionalFiles === undefined) {
      this.command.additionalFiles = []
    }

    this.command.additionalFiles.push(file)

    return this
  }

  build (): UpdatePickUpRequestTemplateCommand {
    const result = this.command
    this.reset()
    return result
  }
}
