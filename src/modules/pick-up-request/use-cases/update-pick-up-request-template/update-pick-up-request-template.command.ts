import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { UpdatePickUpRequestCommand } from '../update-pick-up-request/update-pick-up-request.command.js'

export class UpdatePickUpRequestTemplateCommand extends UpdatePickUpRequestCommand {
  @ApiProperty({ type: String, required: false, nullable: true })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  templateName?: string | null
}
