import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { FileLinkService } from '../../../files/services/file-link.service.js'
import { EntityPart } from '../../../files/enums/entity-part.enum.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { UpdatePickUpRequestValidator } from '../../validators/update-pick-up-request.validator.js'
import { UpdatePickUpRequestMapper } from '../update-pick-up-request/mappers/update-pick-up-request.mapper.js'
import { PickUpRequestTemplateRepository } from '../../services/pick-up-request-template.repository.js'
import { UpdatePickUpRequestTemplateCommand } from './update-pick-up-request-template.command.js'
import { UpdatePickUpRequestTemplateResponse } from './update-pick-up-request-template.response.js'
import { PickUpRequestTemplateUpdatedEvent } from './pick-up-request-template-updated.event.js'

@Injectable()
export class UpdatePickUpRequestTemplateUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly authContext: AuthContext,
    private readonly validator: UpdatePickUpRequestValidator,
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    private readonly pickUpRequestTemplateService: PickUpRequestTemplateRepository,
    private readonly fileLinkService: FileLinkService,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (
    pickUpRequestUuid: string,
    command: UpdatePickUpRequestTemplateCommand
  ): Promise<UpdatePickUpRequestTemplateResponse> {
    const authUserUuid = this.authContext.getUserUuidOrFail()

    const pickUpRequest = await this.pickUpRequestTemplateService
      .findPickUpRequestTemplateOrFail(
        pickUpRequestUuid
      )

    await this.validator.validate(pickUpRequest, command)

    const updatePickUpRequest = {
      ...UpdatePickUpRequestMapper.mapToPickUpRequest(command),
      templateName: command.templateName,
      templateUpdatedByUserUuid: authUserUuid
    }

    await transaction(this.dataSource, async () => {
      const updateResult = await this.pickUpRequestRepository.update({
        uuid: pickUpRequestUuid
      }, updatePickUpRequest)

      if (updateResult.affected === 0) {
        throw new PickUpRequestNotFoundError({ uuid: pickUpRequestUuid })
      }

      if (command.additionalFiles !== undefined) {
        await this.fileLinkService.sync(
          command.additionalFiles,
          pickUpRequestUuid,
          PickUpRequest.name,
          EntityPart.ADDITIONAL
        )
      }

      await this.eventEmitter.emit([new PickUpRequestTemplateUpdatedEvent(pickUpRequestUuid)])
    })

    const updatedPickUpRequest = await this.pickUpRequestRepository.findOneOrFail({
      select: {
        uuid: true,
        createdAt: true,
        updatedAt: true
      },
      where: {
        uuid: pickUpRequestUuid
      }
    })

    return new UpdatePickUpRequestTemplateResponse(updatedPickUpRequest)
  }
}
