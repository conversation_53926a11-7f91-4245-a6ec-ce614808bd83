import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { PickUpRequestGeneralInfoValidator } from '../../validators/pick-up-request-general-info.validator.js'
import { PickUpRequestWasteDetailValidator } from '../../validators/pick-up-request-waste-detail.validator.js'
import { ContractLineModule } from '../../../contract-line/contract-line.module.js'
import { PickUpRequestTransportValidator } from '../../validators/pick-up-request-transport.validator.js'
import { PickUpRequestPlanningValidator } from '../../validators/pick-up-request-planning.validator.js'
import { FileModule } from '../../../files/file.module.js'
import { File } from '../../../files/entities/file.entity.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { SapModule } from '../../../sap/sap.module.js'
import { CustomerModule } from '../../../customer/customer.module.js'
import { PickUpRequestPackagingValidator } from '../../validators/pick-up-request-packaging.validator.js'
import { UpdatePickUpRequestValidator } from '../../validators/update-pick-up-request.validator.js'
import { PickUpRequestServicesModule } from '../../services/services.module.js'
import { UpdatePickUpRequestTemplateController } from './update-pick-up-request-template.controller.js'
import { UpdatePickUpRequestTemplateUseCase } from './update-pick-up-request-template.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([PickUpRequest, File]),
    DomainEventEmitterModule,
    ContractLineModule,
    FileModule,
    SapModule,
    CustomerModule,
    PickUpRequestServicesModule
  ],
  controllers: [
    UpdatePickUpRequestTemplateController
  ],
  providers: [
    UpdatePickUpRequestTemplateUseCase,
    UpdatePickUpRequestValidator,
    PickUpRequestGeneralInfoValidator,
    PickUpRequestWasteDetailValidator,
    PickUpRequestTransportValidator,
    PickUpRequestPlanningValidator,
    PickUpRequestPackagingValidator
  ]
})
export class UpdatePickUpRequestTemplateModule {}
