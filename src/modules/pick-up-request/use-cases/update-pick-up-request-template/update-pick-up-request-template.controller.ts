import { <PERSON>, Controller, Patch } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiBadRequestErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { CustomerNotAccessibleError } from '../../../customer/errors/customer-not-accessible.error.js'
import { MissingEwcLevelsError } from '../../../ewc-code/errors/missing-ewc-levels.error.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { UpdatePickUpRequestTemplateCommand } from './update-pick-up-request-template.command.js'
import { UpdatePickUpRequestTemplateResponse } from './update-pick-up-request-template.response.js'
import { UpdatePickUpRequestTemplateUseCase } from './update-pick-up-request-template.use-case.js'

@ApiTags('Pick-up request template')
@Controller('pick-up-request-templates/:uuid')
@ApiOAuth2([])
export class UpdatePickUpRequestTemplateController {
  constructor (
    private readonly useCase: UpdatePickUpRequestTemplateUseCase
  ) {}

  @Patch()
  @GlobalCustomerRequired()
  @Permissions(Permission.PICK_UP_REQUEST_MANAGE)
  @ApiOkResponse({ type: UpdatePickUpRequestTemplateResponse })
  @ApiNotFoundErrorResponse(PickUpRequestNotFoundError)
  @ApiBadRequestErrorResponse(
    CustomerNotAccessibleError,
    MissingEwcLevelsError
  )
  async updatePickUpRequestTemplate (
    @UuidParam('uuid') pickUpRequestUuid: string,
    @Body() command: UpdatePickUpRequestTemplateCommand
  ): Promise<UpdatePickUpRequestTemplateResponse> {
    return await this.useCase.execute(pickUpRequestUuid, command)
  }
}
