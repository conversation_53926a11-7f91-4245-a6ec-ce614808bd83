import { Injectable } from '@nestjs/common'
import { Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { SapUpdatePickUpRequestUseCase } from '../../../sap/use-cases/update-pick-up-request/update-pick-up-request.use-case.js'
import { User } from '../../../../app/users/entities/user.entity.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { SapGetIndascanDetailUseCase } from '../../../sap/use-cases/get-indascan-detail/get-indascan-detail.use-case.js'
import { SapGetIndascanDetailResponse } from '../../../sap/use-cases/get-indascan-detail/get-indascan-detail.response.js'
import { UpdatePickUpRequestSapCommand } from './update-pick-up-request-sap.command.js'
import { UpdatePickUpRequestSapCommandMapper } from './mappers/update-pick-up-request-sap.command.mapper.js'
import { UpdatePickUpRequestSapValidator } from './update-pick-up-request-sap.validator.js'

@Injectable()
export class UpdatePickUpRequestSapUseCase {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly sapUpdatePickUpRequestUseCase: SapUpdatePickUpRequestUseCase,
    private readonly sapGetIndascanDetailUseCase: SapGetIndascanDetailUseCase,
    private readonly validator: UpdatePickUpRequestSapValidator
  ) {}

  async execute (
    requestNumber: string,
    command: UpdatePickUpRequestSapCommand
  ): Promise<void> {
    const currentUser = await this.getCurrentUser()
    const indascanDetails = await this.getIndaScanPickRequestDetails(requestNumber)

    if (indascanDetails.length === 0) {
      throw new PickUpRequestNotFoundError({ requestNumber: requestNumber })
    }

    await this.validator.validate(indascanDetails)

    const sapCommand = UpdatePickUpRequestSapCommandMapper.fromUpdatePickUpRequestSapCommand(
      requestNumber,
      currentUser,
      command,
      indascanDetails
    )

    await this.sapUpdatePickUpRequestUseCase.execute(sapCommand)
  }

  private async getCurrentUser (): Promise<User> {
    const userUuid = this.authContext.getUserUuidOrFail()

    return this.userRepository.findOneOrFail({
      where: { uuid: userUuid },
      select: {
        uuid: true,
        firstName: true,
        lastName: true
      }
    })
  }

  private async getIndaScanPickRequestDetails (
    requestNumber: string
  ): Promise<SapGetIndascanDetailResponse[]> {
    const query = new SapQuery<SapGetIndascanDetailResponse>()
      .where('Reqno', requestNumber)

    return this.sapGetIndascanDetailUseCase.execute(query)
  }
}
