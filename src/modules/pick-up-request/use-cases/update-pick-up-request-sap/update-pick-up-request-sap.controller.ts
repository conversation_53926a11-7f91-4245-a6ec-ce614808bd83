import { <PERSON>, Controller, Param, Patch } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiBadRequestErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { PickUpRequestContractLineNotFoundError } from '../../errors/pick-up-request-contract-line-not-found.error.js'
import { InvalidUpdateSapPickUpRequestError } from '../../errors/invalid-update-sap-pick-up-request-error.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { UpdatePickUpRequestSapCommand } from './update-pick-up-request-sap.command.js'
import { UpdatePickUpRequestSapUseCase } from './update-pick-up-request-sap.use-case.js'

@ApiTags('Pick-up request')
@Controller('pick-up-requests/sap/:requestNumber')
@ApiOAuth2([])
export class UpdatePickUpRequestSapController {
  constructor (
    private readonly useCase: UpdatePickUpRequestSapUseCase
  ) {}

  @Patch()
  @GlobalCustomerRequired()
  @Permissions(Permission.PICK_UP_REQUEST_MANAGE)
  @ApiOkResponse()
  @ApiNotFoundErrorResponse(PickUpRequestNotFoundError, PickUpRequestContractLineNotFoundError)
  @ApiBadRequestErrorResponse(InvalidUpdateSapPickUpRequestError)
  async updatePickUpRequestSap (
    @Param('requestNumber') requestNumber: string,
    @Body() command: UpdatePickUpRequestSapCommand
  ): Promise<void> {
    return await this.useCase.execute(requestNumber, command)
  }
}
