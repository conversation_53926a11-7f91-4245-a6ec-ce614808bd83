import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { User } from '../../../../app/users/entities/user.entity.js'
import { SapModule } from '../../../sap/sap.module.js'
import { UpdatePickUpRequestSapController } from './update-pick-up-request-sap.controller.js'
import { UpdatePickUpRequestSapUseCase } from './update-pick-up-request-sap.use-case.js'
import { UpdatePickUpRequestSapValidator } from './update-pick-up-request-sap.validator.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    SapModule
  ],
  controllers: [
    UpdatePickUpRequestSapController
  ],
  providers: [
    UpdatePickUpRequestSapUseCase,
    UpdatePickUpRequestSapValidator
  ]
})
export class UpdatePickUpRequestSapModule {}
