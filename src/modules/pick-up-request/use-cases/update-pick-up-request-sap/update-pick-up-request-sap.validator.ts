import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { SapGetIndascanDetailResponse } from '../../../sap/use-cases/get-indascan-detail/get-indascan-detail.response.js'

@Injectable()
export class UpdatePickUpRequestSapValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  async validate (pickUpRequests: SapGetIndascanDetailResponse[]): Promise<void> {
    await this.validatePickUpRequestAccessible(pickUpRequests)
  }

  private async validatePickUpRequestAccessible (
    pickUpRequests: SapGetIndascanDetailResponse[]
  ): Promise<void> {
    for (const pickUpRequest of pickUpRequests) {
      if (pickUpRequest.Kunnr === undefined || pickUpRequest.Kunnr === '') {
        return
      }

      const selectedCustomerId = this.authContext.getSelectedCustomerId()
      if (selectedCustomerId != null && selectedCustomerId !== pickUpRequest.Kunnr) {
        throw new PickUpRequestNotFoundError({ requestNumber: pickUpRequest.Reqno ?? 'Reqno not given' })
      }
      if (pickUpRequest.Kunnr === undefined || pickUpRequest.Kunnr === '') {
        return
      }

      const userId = this.authContext.getAzureEntraUpnOrFail()
      const canUserAccessWasteProducer = await this.userWasteProducerAuthService
        .canUserAccessWasteProducer(
          userId,
          pickUpRequest.Kunnr,
          pickUpRequest.KunnrY2
        )
      if (!canUserAccessWasteProducer) {
        throw new PickUpRequestNotFoundError({ requestNumber: pickUpRequest.Reqno ?? 'Reqno not given' })
      }
    }
  }
}
