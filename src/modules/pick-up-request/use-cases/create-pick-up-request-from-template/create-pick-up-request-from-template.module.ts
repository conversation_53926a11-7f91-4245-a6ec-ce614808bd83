import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { PickUpRequestServicesModule } from '../../services/services.module.js'
import { CreatePickUpRequestFromTemplateController } from './create-pick-up-request-from-template.controller.js'
import { CreatePickUpRequestFromTemplateUseCase } from './create-pick-up-request-from-template.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([PickUpRequest]),
    DomainEventEmitterModule,
    PickUpRequestServicesModule
  ],
  controllers: [
    CreatePickUpRequestFromTemplateController
  ],
  providers: [
    CreatePickUpRequestFromTemplateUseCase
  ]
})
export class CreatePickUpRequestFromTemplateModule {}
