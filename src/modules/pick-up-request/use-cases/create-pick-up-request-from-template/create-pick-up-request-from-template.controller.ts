import { Body, Controller, Post } from '@nestjs/common'
import { ApiCreatedResponse, ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { CreatePickUpRequestFromTemplateCommand } from './create-pick-up-request-from-template.command.js'
import { CreatePickUpRequestFromTemplateResponse } from './create-pick-up-request-from-template.response.js'
import { CreatePickUpRequestFromTemplateUseCase } from './create-pick-up-request-from-template.use-case.js'

@ApiTags('Pick-up request templates')
@Controller('pick-up-request-templates/:uuid/create-pick-up-request')
@ApiOAuth2([])
export class CreatePickUpRequestFromTemplateController {
  constructor (
    private readonly useCase: CreatePickUpRequestFromTemplateUseCase
  ) {}

  @Post()
  @GlobalCustomerRequired()
  @Permissions(Permission.PICK_UP_REQUEST_MANAGE)
  @ApiCreatedResponse({ type: CreatePickUpRequestFromTemplateResponse })
  async createPickUpRequestFromTemplate (
    @UuidParam('uuid') templateUuid: string,
    @Body() command: CreatePickUpRequestFromTemplateCommand
  ): Promise<CreatePickUpRequestFromTemplateResponse> {
    return await this.useCase.execute(
      templateUuid,
      command
    )
  }
}
