import { randomUUID } from 'crypto'
import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { PickUpRequestEntityBuilder } from '../../tests/pick-up-request-entity.builder.js'
import { PickUpRequestCreatedEvent } from '../create-pick-up-request/pick-up-request-created.event.js'
import { PickUpRequestTemplateRepository } from '../../services/pick-up-request-template.repository.js'
import { CreatePickUpRequestFromTemplateCommand } from './create-pick-up-request-from-template.command.js'
import { CreatePickUpRequestFromTemplateResponse } from './create-pick-up-request-from-template.response.js'

@Injectable()
export class CreatePickUpRequestFromTemplateUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly authContext: AuthContext,
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    private readonly pickUpRequestTemplateService: PickUpRequestTemplateRepository,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (
    pickUpRequestTemplateUuid: string,
    _command: CreatePickUpRequestFromTemplateCommand
  ): Promise<CreatePickUpRequestFromTemplateResponse> {
    const pickUpRequestTempate = await this.pickUpRequestTemplateService
      .findPickUpRequestTemplateOrFail(
        pickUpRequestTemplateUuid
      )

    const pickUpRequest = this.createPickUpRequestFromTemplate(pickUpRequestTempate)

    await transaction(this.dataSource, async () => {
      await this.pickUpRequestRepository.save(pickUpRequest)
      await this.eventEmitter.emit(
        [new PickUpRequestCreatedEvent(pickUpRequest, { pickUpRequestTemplateUuid })]
      )
    })

    return new CreatePickUpRequestFromTemplateResponse(pickUpRequest)
  }

  private createPickUpRequestFromTemplate (pickUpRequestTemplate: PickUpRequest): PickUpRequest {
    return new PickUpRequestEntityBuilder()
      .withUuid(randomUUID())
      .withCustomerId(pickUpRequestTemplate.customerId ?? null)
      .withWasteProducerId(pickUpRequestTemplate.wasteProducerId ?? null)
      .withPickUpAddressId(pickUpRequestTemplate.pickUpAddressIds)
      .withTransportMode(pickUpRequestTemplate.transportMode)
      .withIsTransportByIndaver(pickUpRequestTemplate.isTransportByIndaver)
      .withPackagingRequestMaterials(pickUpRequestTemplate.packagingRequestMaterials)
      .withMaterials(pickUpRequestTemplate.materials)
      .withIsOnlyPackagingRequest(false)
      .withTotalQuantityPallets(pickUpRequestTemplate.totalQuantityPallets)
      .withIsReturnPackaging(pickUpRequestTemplate.isReturnPackaging)
      .withPackagingRemark(pickUpRequestTemplate.packagingRemark)
      .withCreatedByUserUuid(this.authContext.getUserUuidOrFail())
      .withStartDate(pickUpRequestTemplate.startDate)
      .withEndDate(pickUpRequestTemplate.endDate)
      .withStartTime(pickUpRequestTemplate.startTime)
      .withRemarks(pickUpRequestTemplate.remarks)
      .withSubmittedOn(null)
      .withRequestNumber(null)
      .withDeliveryAddressId(pickUpRequestTemplate.deliveryAddressId)
      .withSendCopyToContracts(pickUpRequestTemplate.sendCopyToContacts)
      .build()
  }
}
