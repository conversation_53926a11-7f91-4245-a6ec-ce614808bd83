import { after, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { PickUpRequestEntityBuilder } from '../../../tests/pick-up-request-entity.builder.js'
import { CreatePickUpRequestFromTemplateCommandBuilder } from './create-pick-up-request-from-template-command.builder.js'

describe('Create pick-up request from template e2e test', () => {
  let setup: EndToEndTestSetup
  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const templateUuid = randomUUID()

    const response = await request(setup.httpServer)
      .post(`/pick-up-request-templates/${templateUuid}/create-pick-up-request`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const templateUuid = randomUUID()
    const user = await setup.authContext.getUser([])

    const response = await request(setup.httpServer)
      .post(`/pick-up-request-templates/${templateUuid}/create-pick-up-request`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 404 when template not found', async () => {
    const templateUuid = randomUUID()
    const user = await setup.authContext.getUser([Permission.PICK_UP_REQUEST_MANAGE])
    const command = new CreatePickUpRequestFromTemplateCommandBuilder().build()

    const response = await request(setup.httpServer)
      .post(`/pick-up-request-templates/${templateUuid}/create-pick-up-request`)
      .set('Authorization', `Bearer ${user.token}`)
      .send(command)

    expect(response).toHaveStatus(404)
  })

  it('returns 201 when template exists and user is authorized', async () => {
    const user = await setup.authContext.getUser([Permission.PICK_UP_REQUEST_MANAGE])

    const template = await setup.entityManager.save(
      new PickUpRequestEntityBuilder()
        .withTemplateName('Test Template')
        .withCreatedByUserUuid(user.user.uuid)
        .build()
    )

    const command = new CreatePickUpRequestFromTemplateCommandBuilder().build()

    const response = await request(setup.httpServer)
      .post(`/pick-up-request-templates/${template.uuid}/create-pick-up-request`)
      .set('Authorization', `Bearer ${user.token}`)
      .send(command)

    expect(response).toHaveStatus(201)
    expect(response.body).toMatchObject({
      uuid: expect.uuid(),
      createdAt: expect.any(String),
      updatedAt: expect.any(String)
    })
  })
})
