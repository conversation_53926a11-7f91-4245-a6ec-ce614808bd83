import { CreatePickUpRequestFromTemplateCommand } from '../create-pick-up-request-from-template.command.js'

export class CreatePickUpRequestFromTemplateCommandBuilder {
  private command: CreatePickUpRequestFromTemplateCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new CreatePickUpRequestFromTemplateCommand()

    return this
  }

  build (): CreatePickUpRequestFromTemplateCommand {
    const result = this.command

    this.reset()

    return result
  }
}
