import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { PickUpRequest } from '../../../entities/pick-up-request.entity.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { CreatePickUpRequestFromTemplateUseCase } from '../create-pick-up-request-from-template.use-case.js'
import { PickUpRequestCreatedEvent } from '../../create-pick-up-request/pick-up-request-created.event.js'
import { ValidPickUpRequestEntityBuilder } from '../../../tests/valid-pick-up-request-entity.builder.js'
import { PickUpRequestEntityBuilder } from '../../../tests/pick-up-request-entity.builder.js'
import { PickUpRequestTemplateRepository } from '../../../services/pick-up-request-template.repository.js'
import { CreatePickUpRequestFromTemplateCommandBuilder } from './create-pick-up-request-from-template-command.builder.js'

describe('Create pick-up request from template use-case unit test', () => {
  let useCase: CreatePickUpRequestFromTemplateUseCase

  let userUuid: string
  let templateUuid: string
  let pickUpRequest: PickUpRequest
  let pickUpRequestTemplate: PickUpRequest

  let pickUpRequestRepository: SinonStubbedInstance<Repository<PickUpRequest>>
  let pickUpRequestTemplateService: SinonStubbedInstance<PickUpRequestTemplateRepository>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()
    templateUuid = randomUUID()

    const authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: userUuid
    })

    pickUpRequestTemplate = new ValidPickUpRequestEntityBuilder()
      .withUuid(templateUuid)
      .withTemplateName('Test Template')
      .build()

    pickUpRequest = new ValidPickUpRequestEntityBuilder()
      .withUuid(randomUUID())
      .createdByUserUuid(userUuid)
      .build()

    pickUpRequestTemplateService = createStubInstance(PickUpRequestTemplateRepository, {
      findPickUpRequestTemplateOrFail: Promise.resolve(pickUpRequestTemplate)
    })

    pickUpRequestRepository = createStubInstance<Repository<PickUpRequest>>(
      Repository<PickUpRequest>, {
        save: Promise.resolve(pickUpRequest)
      }
    )

    eventEmitter = createStubInstance(DomainEventEmitter)

    useCase = new CreatePickUpRequestFromTemplateUseCase(
      stubDataSource(),
      authContext,
      pickUpRequestRepository,
      pickUpRequestTemplateService,
      eventEmitter
    )
  })

  it('Fetches the template and creates a new pick-up request', async () => {
    const command = new CreatePickUpRequestFromTemplateCommandBuilder().build()

    await useCase.execute(templateUuid, command)

    assert.calledOnce(pickUpRequestTemplateService.findPickUpRequestTemplateOrFail)
    assert.calledOnce(pickUpRequestRepository.save)
  })

  it('Emits PickUpRequestCreatedEvent event', async () => {
    const command = new CreatePickUpRequestFromTemplateCommandBuilder().build()

    const result = await useCase.execute(templateUuid, command)

    const expectedPickUpRequest = new PickUpRequestEntityBuilder()
      .withUuid(result.uuid)
      .createdByUserUuid(userUuid)
      .build()

    expect(eventEmitter).toHaveEmitted(new PickUpRequestCreatedEvent(
      expectedPickUpRequest,
      { pickUpRequestTemplateUuid: templateUuid }
    ))
  })
})
