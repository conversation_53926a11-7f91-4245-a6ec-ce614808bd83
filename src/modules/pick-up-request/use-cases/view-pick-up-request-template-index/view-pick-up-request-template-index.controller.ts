import { Controller, Get, Query } from '@nestjs/common'
import { ApiOAuth2, <PERSON>pi<PERSON><PERSON><PERSON>, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewPickUpRequestTemplateIndexUseCase } from './view-pick-up-request-template-index.use-case.js'
import { ViewPickUpRequestTemplateIndexResponse } from './view-pick-up-request-template-index.response.js'
import { ViewPickUpRequestTemplateIndexQuery } from './query/view-pick-up-request-template-index.query.js'

@ApiTags('Pick-up request template')
@ApiOAuth2([])
@Controller('pick-up-request-templates')
export class ViewPickUpRequestTemplateIndexController {
  constructor (
    private readonly useCase: ViewPickUpRequestTemplateIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.PICK_UP_REQUEST_MANAGE)
  @ApiOkResponse({ type: ViewPickUpRequestTemplateIndexResponse })
  public async viewPickUpRequestTemplateIndex (
    @Query() query: ViewPickUpRequestTemplateIndexQuery
  ): Promise<ViewPickUpRequestTemplateIndexResponse> {
    return this.useCase.execute(query)
  }
}
