import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { ViewPickUpRequestTemplateIndexResponse } from './view-pick-up-request-template-index.response.js'
import { ViewPickUpRequestTemplateIndexRepository } from './view-pick-up-request-template-index.repository.js'
import { ViewPickUpRequestTemplateIndexQuery } from './query/view-pick-up-request-template-index.query.js'

@Injectable()
export class ViewPickUpRequestTemplateIndexUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly repository: ViewPickUpRequestTemplateIndexRepository,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) { }

  public async execute (
    query: ViewPickUpRequestTemplateIndexQuery
  ): Promise<ViewPickUpRequestTemplateIndexResponse> {
    const pagination = typeormPagination(query.pagination)

    const selectedCustomerId = this.authContext.getSelectedCustomerId()

    if (selectedCustomerId === null) {
      const [contacts, count] = await this.repository.findPaginated(query)

      return new ViewPickUpRequestTemplateIndexResponse(
        contacts,
        count,
        pagination.take,
        pagination.skip
      )
    }

    const userUuid = this.authContext.getUserUuidOrFail()
    const restrictedWasteProducerIds = await this.userWasteProducerAuthService
      .getRestrictedWasteProducerIds(
        this.authContext.getAzureEntraUpnOrFail(),
        selectedCustomerId
      )

    const [contacts, count] = await this.repository.findPaginated(
      query, userUuid, selectedCustomerId, restrictedWasteProducerIds
    )

    return new ViewPickUpRequestTemplateIndexResponse(
      contacts,
      count,
      pagination.take,
      pagination.skip
    )
  }
}
