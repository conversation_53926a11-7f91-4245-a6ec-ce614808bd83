import { Body, Controller, Delete } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { BulkDeletePickUpRequestTemplatesUseCase } from './bulk-delete-pick-up-request-templates.use-case.js'
import { BulkDeletePickUpRequestTemplatesCommand } from './bulk-delete-pick-up-request-templates.command.js'

@ApiTags('Pick-up request template')
@Controller('pick-up-request-templates/bulk')
@ApiOAuth2([])
export class BulkDeletePickUpRequestTemplatesController {
  constructor (
    private readonly useCase: BulkDeletePickUpRequestTemplatesUseCase
  ) {}

  @Delete()
  @GlobalCustomerRequired()
  @Permissions(Permission.PICK_UP_REQUEST_MANAGE)
  @ApiOkResponse()
  @ApiNotFoundErrorResponse(NotFoundError)
  async bulkDeletePickUpRequestTemplates (
   @Body() command: BulkDeletePickUpRequestTemplatesCommand
  ): Promise<void> {
    await this.useCase.execute(command.pickUpRequestUuids)
  }
}
