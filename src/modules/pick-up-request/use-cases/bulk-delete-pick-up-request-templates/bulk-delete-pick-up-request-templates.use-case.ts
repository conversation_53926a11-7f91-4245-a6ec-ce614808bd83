import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { FileLinkService } from '../../../files/services/file-link.service.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { PickUpRequestTemplateRepository } from '../../services/pick-up-request-template.repository.js'
import { PickUpRequestTemplateDeletedEvent } from './pick-up-request-template-deleted.event.js'

@Injectable()
export class BulkDeletePickUpRequestTemplatesUseCase {
  constructor (
    private readonly dataSource: DataSource,
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    private readonly pickUpRequestTemplateService: PickUpRequestTemplateRepository,
    private readonly fileLinkService: FileLinkService,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (
    pickUpRequestUuids: string[]
  ): Promise<void> {
    const pickUpRequestTemplates = await this.pickUpRequestTemplateService
      .findPickUpRequestTemplates(
        pickUpRequestUuids,
        {
          selects: ['uuid']
        }
      )

    this.validatePickUpRequestTemplates(pickUpRequestTemplates)

    const events = pickUpRequestTemplates.map(template =>
      new PickUpRequestTemplateDeletedEvent(template.uuid)
    )

    await transaction(this.dataSource, async () => {
      await this.deleteFiles(pickUpRequestUuids)

      await this.pickUpRequestRepository.delete(pickUpRequestUuids)
      await this.eventEmitter.emit(events)
    })
  }

  private validatePickUpRequestTemplates (pickUpRequests: PickUpRequest[]): void {
    if (pickUpRequests.length === 0) {
      throw new NotFoundError()
    }
  }

  private async deleteFiles (pickUpRequestUuids: string[]): Promise<void> {
    await this.fileLinkService.deleteMany(
      pickUpRequestUuids,
      PickUpRequest.name
    )
  }
}
