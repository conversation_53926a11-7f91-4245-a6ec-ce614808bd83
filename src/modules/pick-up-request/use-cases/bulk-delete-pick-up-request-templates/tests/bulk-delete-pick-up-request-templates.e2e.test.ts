import { after, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { In, Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { PickUpRequest } from '../../../entities/pick-up-request.entity.js'
import { PickUpRequestEntityBuilder } from '../../../tests/pick-up-request-entity.builder.js'
import { DeletePickUpRequestTemplatesCommandBuilder } from './bulk-delete-pick-up-request-templates-command.builder.js'

describe('Delete pick-up request templates e2e test', () => {
  let setup: EndToEndTestSetup

  let pickUpRequestRepository: Repository<PickUpRequest>

  let unauthorizedUser: TestUser
  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    pickUpRequestRepository = setup.dataSource.getRepository(PickUpRequest)

    const [_unauthorizedUser, _authorizedUser] = await Promise.all([
      await setup.authContext.getUser([]),
      await setup.authContext.getUser([
        Permission.PICK_UP_REQUEST_MANAGE
      ])
    ])

    unauthorizedUser = _unauthorizedUser
    authorizedUser = _authorizedUser
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .delete(`/pick-up-request-templates/bulk`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const response = await request(setup.httpServer)
      .delete(`/pick-up-request-templates/bulk`)
      .set('Authorization', `Bearer ${unauthorizedUser.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 404 when pick-up request templates not found', async () => {
    const command = new DeletePickUpRequestTemplatesCommandBuilder()
      .addPickUpRequestUuid(randomUUID())
      .build()

    const response = await request(setup.httpServer)
      .delete(`/pick-up-request-templates/bulk`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)
      .send(command)

    expect(response).toHaveStatus(404)
  })

  it('returns 200 when pick-up request templates are deleted', async () => {
    const pickUpRequestTemplates = await pickUpRequestRepository.save([
      new PickUpRequestEntityBuilder()
        .withTemplateName('Template One')
        .createdByUserUuid(authorizedUser.user.uuid)
        .build(),
      new PickUpRequestEntityBuilder()
        .withTemplateName('Template Two')
        .createdByUserUuid(authorizedUser.user.uuid)
        .build()
    ])

    const uuids = pickUpRequestTemplates.map(template => template.uuid)

    const builder = new DeletePickUpRequestTemplatesCommandBuilder()

    pickUpRequestTemplates.forEach((template) => {
      builder.addPickUpRequestUuid(template.uuid)
    })

    const command = builder.build()

    const response = await request(setup.httpServer)
      .delete(`/pick-up-request-templates/bulk`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)
      .send(command)

    const exists = await pickUpRequestRepository.exists({
      where: {
        uuid: In(uuids)
      }
    })

    expect(response).toHaveStatus(200)
    expect(exists).toBeFalsy()
  })
})
