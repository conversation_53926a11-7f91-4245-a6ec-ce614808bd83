import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { PickUpRequest } from '../../../entities/pick-up-request.entity.js'
import { BulkDeletePickUpRequestTemplatesUseCase } from '../bulk-delete-pick-up-request-templates.use-case.js'
import { PickUpRequestEntityBuilder } from '../../../tests/pick-up-request-entity.builder.js'
import { FileLinkService } from '../../../../files/services/file-link.service.js'
import { NotFoundError } from '../../../../exceptions/generic/not-found.error.js'
import { PickUpRequestTemplateDeletedEvent } from '../pick-up-request-template-deleted.event.js'
import { PickUpRequestTemplateRepository } from '../../../services/pick-up-request-template.repository.js'

describe('Delete pick-up request templates use-case unit test', () => {
  let useCase: BulkDeletePickUpRequestTemplatesUseCase

  let userUuid: string

  let pickUpRequest: PickUpRequest

  let pickUpRequestRepository: SinonStubbedInstance<Repository<PickUpRequest>>
  let pickUpRequestTemplateServie: SinonStubbedInstance<PickUpRequestTemplateRepository>
  let fileLinkService: SinonStubbedInstance<FileLinkService>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()

    pickUpRequest = new PickUpRequestEntityBuilder()
      .withTemplateName('Template Name')
      .createdByUserUuid(userUuid)
      .build()

    eventEmitter = createStubInstance(DomainEventEmitter)
    fileLinkService = createStubInstance(FileLinkService)
    pickUpRequestTemplateServie = createStubInstance(PickUpRequestTemplateRepository)
    pickUpRequestRepository = createStubInstance<Repository<PickUpRequest>>(
      Repository<PickUpRequest>
    )

    useCase = new BulkDeletePickUpRequestTemplatesUseCase(
      stubDataSource(),
      pickUpRequestRepository,
      pickUpRequestTemplateServie,
      fileLinkService,
      eventEmitter
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    pickUpRequestTemplateServie.findPickUpRequestTemplates.resolves([pickUpRequest])
    pickUpRequestRepository.delete.resolves()
  }

  it('Throws not found error when no pick-up request templates were found', async () => {
    pickUpRequestTemplateServie.findPickUpRequestTemplates.resolves([])

    await expect(useCase.execute([pickUpRequest.uuid]))
      .rejects.toThrow(NotFoundError)
  })

  it('Calls all methods once', async () => {
    await useCase.execute([pickUpRequest.uuid])

    assert.calledOnce(pickUpRequestTemplateServie.findPickUpRequestTemplates)
    assert.calledOnce(fileLinkService.deleteMany)
    assert.calledOnce(pickUpRequestRepository.delete)
  })

  it('Emits a PickUpRequestTemplateDeletedEvent event', async () => {
    await useCase.execute([pickUpRequest.uuid])

    expect(eventEmitter).toHaveEmitted(new PickUpRequestTemplateDeletedEvent(pickUpRequest.uuid))
  })
})
