import { BulkDeletePickUpRequestTemplatesCommand } from '../bulk-delete-pick-up-request-templates.command.js'

export class DeletePickUpRequestTemplatesCommandBuilder {
  private deletePickUpRequestTemplatesCommand: BulkDeletePickUpRequestTemplatesCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.deletePickUpRequestTemplatesCommand = new BulkDeletePickUpRequestTemplatesCommand()
    this.deletePickUpRequestTemplatesCommand.pickUpRequestUuids = []
    return this
  }

  addPickUpRequestUuid (pickUpRequestUuid: string): this {
    this.deletePickUpRequestTemplatesCommand.pickUpRequestUuids.push(pickUpRequestUuid)
    return this
  }

  build (): BulkDeletePickUpRequestTemplatesCommand {
    const command = this.deletePickUpRequestTemplatesCommand
    this.reset()
    return command
  }
}
