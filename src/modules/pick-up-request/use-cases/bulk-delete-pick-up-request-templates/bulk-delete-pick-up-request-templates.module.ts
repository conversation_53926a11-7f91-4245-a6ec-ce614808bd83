import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { File } from '../../../files/entities/file.entity.js'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { FileModule } from '../../../files/file.module.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { PickUpRequestServicesModule } from '../../services/services.module.js'
import { BulkDeletePickUpRequestTemplatesUseCase } from './bulk-delete-pick-up-request-templates.use-case.js'
import { BulkDeletePickUpRequestTemplatesController } from './bulk-delete-pick-up-request-templates.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([PickUpRequest, File]),
    DomainEventEmitterModule,
    FileModule,
    PickUpRequestServicesModule
  ],
  controllers: [
    BulkDeletePickUpRequestTemplatesController
  ],
  providers: [
    BulkDeletePickUpRequestTemplatesUseCase
  ]
})
export class BulkDeletePickUpRequestTemplatesModule {}
