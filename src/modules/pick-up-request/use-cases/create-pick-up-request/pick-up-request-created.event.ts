import { ApiProperty } from '@nestjs/swagger'
import { RegisterDomainEvent } from '../../../domain-events/register-domain-event.decorator.js'
import { DomainEventType } from '../../../domain-events/domain-event-type.js'
import { PickUpRequestEvent } from '../../events/pick-up-request.event.js'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'

interface PickUpRequestCreatedEventOptions {
  pickUpRequestTemplateUuid?: string
}

export class PickUpRequestCreatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly pickUpRequestUuid: string

  @ApiProperty({ format: 'uuid', required: false })
  readonly pickUpRequestTemplateUuid?: string

  constructor (
    pickUpRequestUuid: string,
    pickUpRequestTemplateUuid?: string
  ) {
    this.pickUpRequestUuid = pickUpRequestUuid
    this.pickUpRequestTemplateUuid = pickUpRequestTemplateUuid
  }
}

@RegisterDomainEvent(DomainEventType.PICK_UP_REQUEST_CREATED, 1)
export class PickUpRequestCreatedEvent
  extends PickUpRequestEvent<PickUpRequestCreatedEventContent> {
  constructor (
    pickUpRequest: PickUpRequest,
    options?: PickUpRequestCreatedEventOptions
  ) {
    super({
      pickUpRequestUuid: pickUpRequest.uuid,
      content: new PickUpRequestCreatedEventContent(
        pickUpRequest.uuid,
        options?.pickUpRequestTemplateUuid
      )
    })
  }
}
