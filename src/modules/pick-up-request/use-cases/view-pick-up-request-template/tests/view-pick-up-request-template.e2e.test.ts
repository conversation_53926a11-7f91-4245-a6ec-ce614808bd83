import { after, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { PickUpRequest } from '../../../entities/pick-up-request.entity.js'
import { PickUpRequestEntityBuilder } from '../../../tests/pick-up-request-entity.builder.js'

describe('View pick up request template e2e test', () => {
  let setup: EndToEndTestSetup

  let pickUpRequestRepository: Repository<PickUpRequest>

  let unauthorizedUser: TestUser
  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    pickUpRequestRepository = setup.dataSource.getRepository(PickUpRequest)

    const [_unauthorizedUser, _authorizedUser] = await Promise.all([
      await setup.authContext.getUser([]),
      await setup.authContext.getUser([Permission.PICK_UP_REQUEST_MANAGE])
    ])

    unauthorizedUser = _unauthorizedUser
    authorizedUser = _authorizedUser
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/pick-up-request-templates/${randomUUID()}`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const response = await request(setup.httpServer)
      .get(`/pick-up-request-templates/${randomUUID()}`)
      .set('Authorization', `Bearer ${unauthorizedUser.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 200 when authorized and pick up request template created by self', async () => {
    const pickUpRequest = await pickUpRequestRepository.save(
      new PickUpRequestEntityBuilder()
        .createdByUserUuid(authorizedUser.user.uuid)
        .withTemplateName('My Template')
        .build()
    )

    const response = await request(setup.httpServer)
      .get(`/pick-up-request-templates/${pickUpRequest.uuid}`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)

    expect(response).toHaveStatus(200)
    expect(response.body).toMatchObject({
      uuid: pickUpRequest.uuid,
      createdAt: pickUpRequest.createdAt.toISOString(),
      updatedAt: pickUpRequest.updatedAt.toISOString(),
      templateName: pickUpRequest.templateName
    })
  })
})
