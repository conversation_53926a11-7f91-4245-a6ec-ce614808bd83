import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewPickUpRequestTemplateUseCase } from '../view-pick-up-request-template.use-case.js'
import { PickUpRequest } from '../../../entities/pick-up-request.entity.js'
import { PickUpRequestEntityBuilder } from '../../../tests/pick-up-request-entity.builder.js'
import { LoadPickUpRequestDataService } from '../../../services/load-pick-up-request-data.service.js'
import { WasteProducerBuilder } from '../../../../waste-producer/tests/waste-producer.builder.js'
import { PickUpAddressBuilder } from '../../../../pick-up-address/tests/pick-up-address.builder.js'
import { CustomerBuilder } from '../../../../customer/tests/customer.builder.js'
import { PickUpRequestTemplateRepository } from '../../../services/pick-up-request-template.repository.js'

describe('View pick up request use-case unit test', () => {
  let useCase: ViewPickUpRequestTemplateUseCase

  let pickUpRequest: PickUpRequest
  let pickUpRequestUuid: string

  let pickUpRequestTemplateService: SinonStubbedInstance<PickUpRequestTemplateRepository>
  let loadPickUpRequestDataService: SinonStubbedInstance<LoadPickUpRequestDataService>

  before(() => {
    TestBench.setupUnitTest()

    pickUpRequestUuid = randomUUID()
    pickUpRequest = new PickUpRequestEntityBuilder()
      .withUuid(pickUpRequestUuid)
      .withCustomer(new CustomerBuilder().build())
      .withWasteProducer(new WasteProducerBuilder().build())
      .withPickUpAddresses([new PickUpAddressBuilder().build()])
      .build()

    pickUpRequestTemplateService = createStubInstance(PickUpRequestTemplateRepository)
    pickUpRequestTemplateService.findPickUpRequestTemplateOrFail.resolves(pickUpRequest)

    loadPickUpRequestDataService = createStubInstance(LoadPickUpRequestDataService)
    loadPickUpRequestDataService.execute.resolves()

    useCase = new ViewPickUpRequestTemplateUseCase(
      pickUpRequestTemplateService,
      loadPickUpRequestDataService
    )
  })

  it('Calls all methods once', async () => {
    await useCase.execute(pickUpRequestUuid)

    assert.calledOnce(pickUpRequestTemplateService.findPickUpRequestTemplateOrFail)
    assert.calledOnce(loadPickUpRequestDataService.execute)
  })
})
