import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { File } from '../../../files/entities/file.entity.js'
import { PickUpRequestServicesModule } from '../../services/services.module.js'
import { ViewPickUpRequestTemplateUseCase } from './view-pick-up-request-template.use-case.js'
import { ViewPickUpRequestTemplateController } from './view-pick-up-request-template.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([PickUpRequest, File]),
    PickUpRequestServicesModule
  ],
  controllers: [
    ViewPickUpRequestTemplateController
  ],
  providers: [
    ViewPickUpRequestTemplateUseCase
  ]
})
export class ViewPickUpRequestTemplateModule {}
