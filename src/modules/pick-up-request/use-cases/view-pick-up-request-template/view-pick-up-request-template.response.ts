import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { AbstractViewPickUpRequestResponse } from '../../responses/abstract-view-pick-up-request.response.js'
import { UpdatedByUserResponse } from './updated-by-user.response.js'

export class ViewPickUpRequestTemplateResponse extends AbstractViewPickUpRequestResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: String })
  templateName: string

  @ApiProperty({ type: UpdatedByUserResponse, required: false })
  templateUpdatedByUser: UpdatedByUserResponse | null

  constructor (pickUpRequest: PickUpRequest) {
    super(pickUpRequest)

    assert(pickUpRequest.templateName !== null)

    this.uuid = pickUpRequest.uuid
    this.createdAt = pickUpRequest.createdAt.toISOString()
    this.updatedAt = pickUpRequest.updatedAt.toISOString()
    this.templateName = pickUpRequest.templateName

    this.templateUpdatedByUser = pickUpRequest.templateUpdatedByUserUuid != null
      ? (
          assert(pickUpRequest.templateUpdatedByUser !== undefined),
          new UpdatedByUserResponse(pickUpRequest.templateUpdatedByUser)
        )
      : null
  }
}
