import { Injectable } from '@nestjs/common'
import { LoadPickUpRequestDataService } from '../../services/load-pick-up-request-data.service.js'
import { PickUpRequestTemplateRepository } from '../../services/pick-up-request-template.repository.js'
import { ViewPickUpRequestTemplateResponse } from './view-pick-up-request-template.response.js'

@Injectable()
export class ViewPickUpRequestTemplateUseCase {
  constructor (
    private readonly pickUpRequestTemplateService: PickUpRequestTemplateRepository,
    private readonly loadPickUpRequestDataService: LoadPickUpRequestDataService
  ) {}

  async execute (pickUpRequestUuid: string): Promise<ViewPickUpRequestTemplateResponse> {
    const pickUpRequest = await this.pickUpRequestTemplateService.findPickUpRequestTemplateOrFail(
      pickUpRequestUuid
    )

    await this.loadPickUpRequestDataService.execute(pickUpRequest)

    return new ViewPickUpRequestTemplateResponse(pickUpRequest)
  }
}
