import { Controller, Get } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewPickUpRequestTemplateUseCase } from './view-pick-up-request-template.use-case.js'
import { ViewPickUpRequestTemplateResponse } from './view-pick-up-request-template.response.js'

@ApiTags('Pick-up request templates')
@Controller('pick-up-request-templates/:uuid')
@ApiOAuth2([])
export class ViewPickUpRequestTemplateController {
  constructor (
    private readonly useCase: ViewPickUpRequestTemplateUseCase
  ) {}

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.PICK_UP_REQUEST_MANAGE)
  @ApiOkResponse({ type: ViewPickUpRequestTemplateResponse })
  @ApiNotFoundErrorResponse(NotFoundError)
  async viewPickUpRequestTemplate (
    @UuidParam('uuid') pickUpRequestUuid: string
  ): Promise<ViewPickUpRequestTemplateResponse> {
    return await this.useCase.execute(pickUpRequestUuid)
  }
}
