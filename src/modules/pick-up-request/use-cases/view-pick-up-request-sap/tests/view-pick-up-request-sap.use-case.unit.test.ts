import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { SinonStubbedInstance, createStubInstance } from 'sinon'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { NotFoundError } from '../../../../exceptions/generic/not-found.error.js'
import { ViewPickUpRequestSapUseCase } from '../view-pick-up-request-sap.use-case.js'
import { SapGetPickUpRequestDetailUseCase } from '../../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.use-case.js'
import { SapGetPickUpRequestDetailResponseBuilder } from '../../../../sap/use-cases/get-pick-up-request-detail/tests/get-pick-up-request-detail.response.builder.js'
import { SapGetPickUpRequestCommentUseCase } from '../../../../sap/use-cases/get-pick-up-request-comment/get-pick-up-request-comment.use-case.js'
import { GetPickUpRequestCommentResponseBuilder } from '../../../../sap/use-cases/get-pick-up-request-comment/tests/get-pick-up-request-comment.response.builder.js'
import { SapGetPickUpRequestContactsUseCase } from '../../../../sap/use-cases/get-pick-up-request-contacts/get-pick-up-request-contacts.use-case.js'
import { SapGetPickUpRequestAttachmentsUseCase } from '../../../../sap/use-cases/get-pick-up-request-attachments/get-pick-up-request-attachments.use-case.js'
import { ViewPickUpRequestSapValidator } from '../view-pick-up-request-sap.validator.js'
import { UnNumber } from '../../../../un-number/entities/un-number.entity.js'

describe('View pick-up request SAP use-case unit test', () => {
  let useCase: ViewPickUpRequestSapUseCase

  let sapGetPickUpRequestDetail: SinonStubbedInstance<SapGetPickUpRequestDetailUseCase>
  let sapGetPickUpRequestComment: SinonStubbedInstance<SapGetPickUpRequestCommentUseCase>
  let sapGetPickUpRequestContacts: SinonStubbedInstance<SapGetPickUpRequestContactsUseCase>
  let sapGetPickUpRequestAttachments: SinonStubbedInstance<SapGetPickUpRequestAttachmentsUseCase>
  let validator: SinonStubbedInstance<ViewPickUpRequestSapValidator>
  let unNumberRepository: SinonStubbedInstance<Repository<UnNumber>>

  before(() => {
    TestBench.setupUnitTest()

    sapGetPickUpRequestDetail = createStubInstance(SapGetPickUpRequestDetailUseCase)
    sapGetPickUpRequestComment = createStubInstance(SapGetPickUpRequestCommentUseCase)
    sapGetPickUpRequestContacts = createStubInstance(SapGetPickUpRequestContactsUseCase)
    sapGetPickUpRequestAttachments = createStubInstance(SapGetPickUpRequestAttachmentsUseCase)
    validator = createStubInstance(ViewPickUpRequestSapValidator)
    unNumberRepository = createStubInstance<Repository<UnNumber>>(Repository<UnNumber>)

    useCase = new ViewPickUpRequestSapUseCase(
      sapGetPickUpRequestDetail,
      sapGetPickUpRequestComment,
      sapGetPickUpRequestContacts,
      sapGetPickUpRequestAttachments,
      validator,
      unNumberRepository
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    Sinon.resetHistory()

    sapGetPickUpRequestDetail.execute.resolves([])
    sapGetPickUpRequestComment.execute.resolves([])
    sapGetPickUpRequestContacts.execute.resolves([])
    sapGetPickUpRequestAttachments.execute.resolves([])
    validator.validate.resolves()
    unNumberRepository.find.resolves([])
  }

  it('Throws an error when pick-up request not found', async () => {
    await expect(useCase.execute(randomUUID())).rejects.toThrow(NotFoundError)
  })

  it('Returns pick-up request when found', async () => {
    const requestNumber = randomUUID()
    sapGetPickUpRequestDetail.execute.resolves([
      new SapGetPickUpRequestDetailResponseBuilder().withRequestNumber(requestNumber).build()
    ])
    sapGetPickUpRequestComment.execute.resolves([
      new GetPickUpRequestCommentResponseBuilder().withRequestNumber(requestNumber).build()
    ])

    const response = await useCase.execute(requestNumber)

    expect(response).toBeDefined()
    expect(response.requestNumber).toBe(requestNumber)
  })

  it('Calls all methods once', async () => {
    const requestNumber = randomUUID()
    sapGetPickUpRequestDetail.execute.resolves([
      new SapGetPickUpRequestDetailResponseBuilder().withRequestNumber(requestNumber).build()
    ])

    await useCase.execute(requestNumber)

    expect(sapGetPickUpRequestDetail.execute.calledOnce).toBeTruthy()
    expect(sapGetPickUpRequestComment.execute.calledOnce).toBeTruthy()
    expect(sapGetPickUpRequestContacts.execute.calledOnce).toBeTruthy()
    expect(sapGetPickUpRequestAttachments.execute.calledOnce).toBeTruthy()
    expect(validator.validate.calledOnce).toBeTruthy()
  })

  it('Loads UN number details when UN numbers are present', async () => {
    const requestNumber = randomUUID()
    sapGetPickUpRequestDetail.execute.resolves([
      new SapGetPickUpRequestDetailResponseBuilder()
        .withRequestNumber(requestNumber)
        .withWasteType('01')
        .withYyun('1549')
        .build()
    ])

    await useCase.execute(requestNumber)

    expect(unNumberRepository.find.calledOnce).toBeTruthy()
  })
})
