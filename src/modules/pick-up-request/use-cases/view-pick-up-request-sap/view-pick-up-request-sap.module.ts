import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { SapModule } from '../../../sap/sap.module.js'
import { UnNumber } from '../../../un-number/entities/un-number.entity.js'
import { ViewPickUpRequestSapController } from './view-pick-up-request-sap.controller.js'
import { ViewPickUpRequestSapUseCase } from './view-pick-up-request-sap.use-case.js'
import { ViewPickUpRequestSapValidator } from './view-pick-up-request-sap.validator.js'

@Module({
  imports: [
    SapModule,
    TypeOrmModule.forFeature([UnNumber])
  ],
  controllers: [
    ViewPickUpRequestSapController
  ],
  providers: [
    ViewPickUpRequestSapUseCase,
    ViewPickUpRequestSapValidator
  ]
})
export class ViewPickUpRequestSapModule {}
