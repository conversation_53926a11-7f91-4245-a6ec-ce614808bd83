import { Injectable } from '@nestjs/common'
import { UniformDetailPickUpRequest } from '../../types/uniform-pick-up-request-detail.type.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'

@Injectable()
export class ViewPickUpRequestSapValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  async validate (pickUpRequest: UniformDetailPickUpRequest): Promise<void> {
    await this.validatePickUpRequestAccessible(pickUpRequest)
  }

  private async validatePickUpRequestAccessible (
    pickUpRequest: UniformDetailPickUpRequest
  ): Promise<void> {
    if (pickUpRequest.customer?.id === undefined || pickUpRequest.customer?.id === '') {
      return
    }

    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== pickUpRequest.customer.id) {
      throw new PickUpRequestNotFoundError({ requestNumber: pickUpRequest.requestNumber ?? 'Reqno not given' })
    }
    if (pickUpRequest.wasteProducer?.id === undefined || pickUpRequest.wasteProducer?.id === '') {
      return
    }

    const userId = this.authContext.getAzureEntraUpnOrFail()
    const canUserAccessWasteProducer = await this.userWasteProducerAuthService
      .canUserAccessWasteProducer(
        userId,
        pickUpRequest.customer.id,
        pickUpRequest.wasteProducer.id
      )
    if (!canUserAccessWasteProducer) {
      throw new PickUpRequestNotFoundError({ requestNumber: pickUpRequest.requestNumber ?? 'Reqno not given' })
    }
  }
}
