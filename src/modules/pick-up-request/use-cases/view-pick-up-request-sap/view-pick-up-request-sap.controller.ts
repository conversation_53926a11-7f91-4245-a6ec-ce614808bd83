import { <PERSON>, Get, Param } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewPickUpRequestSapUseCase } from './view-pick-up-request-sap.use-case.js'
import { ViewPickUpRequestSapResponse } from './view-pick-up-request-sap.response.js'

@ApiTags('Pick-up request')
@ApiOAuth2([])
@Controller('pick-up-requests/sap/:requestNumber')
export class ViewPickUpRequestSapController {
  constructor (
    private readonly useCase: ViewPickUpRequestSapUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.PICK_UP_REQUEST_READ)
  @ApiOkResponse({ type: ViewPickUpRequestSapResponse })
  @ApiNotFoundErrorResponse()
  public async viewPickUpRequestSap (
    @Param('requestNumber') requestNumber: string
  ): Promise<ViewPickUpRequestSapResponse> {
    return await this.useCase.execute(requestNumber)
  }
}
