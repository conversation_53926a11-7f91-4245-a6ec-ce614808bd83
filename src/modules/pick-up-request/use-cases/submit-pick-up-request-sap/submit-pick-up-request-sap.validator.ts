import { Injectable } from '@nestjs/common'
import { SapGetPickUpRequestDetailResponse } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.response.js'
import { mapPickUpRequestStatusToSapValue, PickUpRequestStatus } from '../../enums/pick-up-request-status.enum.js'
import { InvalidIndascanSubmitStatusError } from '../../errors/invalid-indascan-submit-status.error.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'

@Injectable()
export class SubmitPickUpRequestSapValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  async validate (details: SapGetPickUpRequestDetailResponse[]): Promise<void> {
    const indascanStatus = mapPickUpRequestStatusToSapValue(PickUpRequestStatus.INDASCAN_DRAFT)

    const isNotAllIndascanDraft = details.some((detail) => {
      return detail.Status === undefined || !indascanStatus.includes(detail.Status)
    })

    if (isNotAllIndascanDraft) {
      throw new InvalidIndascanSubmitStatusError()
    }

    await this.validatePickUpRequestAccessible(details)
  }

  private async validatePickUpRequestAccessible (
    responses: SapGetPickUpRequestDetailResponse[]
  ): Promise<void> {
    for (const packagingRequest of responses) {
      if (packagingRequest.Kunnr === undefined || packagingRequest.Kunnr === '') {
        continue
      }

      const selectedCustomerId = this.authContext.getSelectedCustomerId()
      if (selectedCustomerId != null && selectedCustomerId !== packagingRequest.Kunnr) {
        throw new PickUpRequestNotFoundError({ requestNumber: packagingRequest.Reqno ?? 'Reqno not given' })
      }

      if (packagingRequest.KunnrY2 === undefined || packagingRequest.KunnrY2 === '') {
        continue
      }

      const userId = this.authContext.getAzureEntraUpnOrFail()
      const canUserAccessWasteProducer = await this.userWasteProducerAuthService
        .canUserAccessWasteProducer(
          userId,
          packagingRequest.Kunnr,
          packagingRequest.KunnrY2
        )
      if (!canUserAccessWasteProducer) {
        throw new PickUpRequestNotFoundError({ requestNumber: packagingRequest.Reqno ?? 'Reqno not given' })
      }
    }
  }
}
