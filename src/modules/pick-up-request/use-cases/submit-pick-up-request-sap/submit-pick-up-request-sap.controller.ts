import { <PERSON>, HttpC<PERSON>, Param, Post } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiConflictErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { InvalidIndascanSubmitStatusError } from '../../errors/invalid-indascan-submit-status.error.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { SubmitPickUpRequestSapUseCase } from './submit-pick-up-request-sap.use-case.js'

@ApiTags('Pick-up request')
@Controller('pick-up-requests/sap/:requestNumber/submit')
@ApiOAuth2([])
export class SubmitPickUpRequestSapController {
  constructor (
    private readonly useCase: SubmitPickUpRequestSapUseCase
  ) {}

  @Post()
  @GlobalCustomerRequired()
  @HttpCode(200)
  @Permissions(Permission.PICK_UP_REQUEST_MANAGE)
  @ApiOkResponse()
  @ApiConflictErrorResponse(InvalidIndascanSubmitStatusError)
  @ApiNotFoundErrorResponse(PickUpRequestNotFoundError)
  async submitPickUpRequest (
    @Param('requestNumber') requestNumber: string
  ): Promise<void> {
    return await this.useCase.execute(requestNumber)
  }
}
