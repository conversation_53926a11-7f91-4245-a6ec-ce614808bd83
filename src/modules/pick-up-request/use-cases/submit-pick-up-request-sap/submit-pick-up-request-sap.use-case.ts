import { Injectable } from '@nestjs/common'
import { SapUpdatePickUpRequestUseCase } from '../../../sap/use-cases/update-pick-up-request/update-pick-up-request.use-case.js'
import { SapGetPickUpRequestDetailUseCase } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.use-case.js'
import { SapGetPickUpRequestDetailResponse } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.response.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SubmitPickUpRequestSapMapper } from './submit-pick-up-request-sap.mapper.js'
import { SubmitPickUpRequestSapValidator } from './submit-pick-up-request-sap.validator.js'

@Injectable()
export class SubmitPickUpRequestSapUseCase {
  constructor (
    private readonly sapGetPickUpRequestDetailUseCase: SapGetPickUpRequestDetailUseCase,
    private readonly sapUpdatePickUpRequestUseCase: SapUpdatePickUpRequestUseCase,
    private readonly validator: SubmitPickUpRequestSapValidator
  ) {}

  async execute (
    requestNumber: string
  ): Promise<void> {
    const details = await this.getPickUpRequestDetails(requestNumber)

    await this.validator.validate(details)

    const sapCommand = SubmitPickUpRequestSapMapper.mapSubmitToSapCommand(requestNumber, details)

    await this.sapUpdatePickUpRequestUseCase.execute(sapCommand)
  }

  private async getPickUpRequestDetails (
    requestNumber: string
  ): Promise<SapGetPickUpRequestDetailResponse[]> {
    const sapQuery = new SapQuery<SapGetPickUpRequestDetailResponse>()
      .where('Reqno', requestNumber)

    return await this.sapGetPickUpRequestDetailUseCase.execute(sapQuery)
  }
}
