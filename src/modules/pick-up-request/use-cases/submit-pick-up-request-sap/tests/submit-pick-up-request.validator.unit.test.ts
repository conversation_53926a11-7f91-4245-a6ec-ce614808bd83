import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { expect } from 'expect'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { SapGetPickUpRequestDetailResponseBuilder } from '../../../../sap/use-cases/get-pick-up-request-detail/tests/get-pick-up-request-detail.response.builder.js'
import { SubmitPickUpRequestSapValidator } from '../submit-pick-up-request-sap.validator.js'
import { InvalidIndascanSubmitStatusError } from '../../../errors/invalid-indascan-submit-status.error.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../../auth/services/user-waste-producer-auth.service.js'
import { PickUpRequestNotFoundError } from '../../../errors/pick-up-request-sap-not-found.error.js'

describe('SubmitPickUpRequestValidator - Unit Tests', () => {
  let validator: SubmitPickUpRequestSapValidator

  let authContext: SinonStubbedInstance<AuthContext>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>

  before(() => {
    TestBench.setupUnitTest()

    authContext = createStubInstance(AuthContext)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)

    validator = new SubmitPickUpRequestSapValidator(
      authContext,
      userWasteProducerAuthService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods (): void {
    authContext.getSelectedCustomerId.returns(null)
    authContext.getAzureEntraUpnOrFail.returns(randomUUID())
    userWasteProducerAuthService.canUserAccessWasteProducer.resolves(true)
  }

  it('throws InvalidIndascanSubmitStatusError when a detail is not of status Indascan', async () => {
    const details = Array.from({ length: 3 }, () =>
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('004')
        .build()
    )

    const nonIndaScanDetail = new SapGetPickUpRequestDetailResponseBuilder()
      .withStatus('000')
      .build()

    await expect(validator.validate([...details, nonIndaScanDetail]))
      .rejects
      .toThrow(InvalidIndascanSubmitStatusError)
  })

  it('throws PickUpRequestNotFoundError when customer is not accessible', async () => {
    authContext.getSelectedCustomerId.returns(randomUUID())

    const details = Array.from({ length: 3 }, () =>
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('004')
        .build()
    )

    await expect(validator.validate(details))
      .rejects
      .toThrow(PickUpRequestNotFoundError)
  })

  it('throws PickUpRequestNotFoundError when waste producer is not accessible', async () => {
    const customerId = randomUUID()
    authContext.getSelectedCustomerId.returns(customerId)
    userWasteProducerAuthService.canUserAccessWasteProducer.resolves(false)

    const details = Array.from({ length: 3 }, () =>
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('004')
        .build()
    )

    await expect(validator.validate(details))
      .rejects
      .toThrow(PickUpRequestNotFoundError)
  })

  it('throws no error when all detail is of status Indascan', async () => {
    const details = Array.from({ length: 3 }, () =>
      new SapGetPickUpRequestDetailResponseBuilder()
        .withStatus('004')
        .build()
    )

    await expect(validator.validate(details))
      .resolves
      .not
      .toThrow(InvalidIndascanSubmitStatusError)
  })
})
