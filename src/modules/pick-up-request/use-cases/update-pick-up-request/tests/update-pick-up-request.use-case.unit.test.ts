import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { UpdatePickUpRequestUseCase } from '../update-pick-up-request.use-case.js'
import { UpdatePickUpRequestValidator } from '../../../validators/update-pick-up-request.validator.js'
import { PickUpRequest } from '../../../entities/pick-up-request.entity.js'
import { PickUpRequestEntityBuilder } from '../../../tests/pick-up-request-entity.builder.js'
import { PickUpRequestUpdatedEvent } from '../pick-up-request-updated.event.js'
import { FileLinkService } from '../../../../files/services/file-link.service.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { UpdatePickUpRequestCommandBuilder } from './update-pick-up-request-command.builder.js'

describe('Update pick-up request use-case unit test', () => {
  let useCase: UpdatePickUpRequestUseCase

  let userUuid: string

  let validator: SinonStubbedInstance<UpdatePickUpRequestValidator>
  let fileLinkService: SinonStubbedInstance<FileLinkService>
  let pickUpRequestRepository: SinonStubbedInstance<Repository<PickUpRequest>>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()

    const authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: userUuid
    })

    validator = createStubInstance(UpdatePickUpRequestValidator)
    fileLinkService = createStubInstance(FileLinkService)

    pickUpRequestRepository = createStubInstance<Repository<PickUpRequest>>(
      Repository<PickUpRequest>
    )

    pickUpRequestRepository.update.resolves({
      raw: {},
      affected: 1,
      generatedMaps: []
    })

    fileLinkService.sync.resolves()

    pickUpRequestRepository.findOneOrFail.resolves(new PickUpRequestEntityBuilder().build())
    eventEmitter = createStubInstance(DomainEventEmitter)

    useCase = new UpdatePickUpRequestUseCase(
      stubDataSource(),
      authContext,
      validator,
      pickUpRequestRepository,
      fileLinkService,
      eventEmitter
    )
  })

  it('Calls all methods once', async () => {
    const pickUpRequestUuid = randomUUID()
    const command = new UpdatePickUpRequestCommandBuilder()
      .addAdditionalFile({
        fileUuid: randomUUID(),
        order: 1
      })
      .build()

    await useCase.execute(pickUpRequestUuid, command)

    assert.calledOnce(validator.validate)
    assert.calledOnce(pickUpRequestRepository.update)
    assert.calledTwice(pickUpRequestRepository.findOneOrFail)
    assert.calledOnce(fileLinkService.sync)
  })

  it('Emits a PickUpRequestUpdatedEvent event', async () => {
    const pickUpRequestUuid = randomUUID()
    const command = new UpdatePickUpRequestCommandBuilder().build()

    await useCase.execute(pickUpRequestUuid, command)

    expect(eventEmitter).toHaveEmitted(new PickUpRequestUpdatedEvent(pickUpRequestUuid))
  })
})
