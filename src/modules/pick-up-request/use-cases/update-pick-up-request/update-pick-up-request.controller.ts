import { <PERSON>, Controller, Patch } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiBadRequestErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { CustomerNotAccessibleError } from '../../../customer/errors/customer-not-accessible.error.js'
import { MissingEwcLevelsError } from '../../../ewc-code/errors/missing-ewc-levels.error.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { UpdatePickUpRequestCommand } from './update-pick-up-request.command.js'
import { UpdatePickUpRequestResponse } from './update-pick-up-request.response.js'
import { UpdatePickUpRequestUseCase } from './update-pick-up-request.use-case.js'

@ApiTags('Pick-up request')
@Controller('pick-up-requests/:uuid')
@ApiOAuth2([])
export class UpdatePickUpRequestController {
  constructor (
    private readonly useCase: UpdatePickUpRequestUseCase
  ) {}

  @Patch()
  @GlobalCustomerRequired()
  @Permissions(Permission.PICK_UP_REQUEST_MANAGE)
  @ApiOkResponse({ type: UpdatePickUpRequestResponse })
  @ApiNotFoundErrorResponse(PickUpRequestNotFoundError)
  @ApiBadRequestErrorResponse(
    CustomerNotAccessibleError,
    MissingEwcLevelsError
  )
  async updatePickUpRequest (
    @UuidParam('uuid') pickUpRequestUuid: string,
    @Body() command: UpdatePickUpRequestCommand
  ): Promise<UpdatePickUpRequestResponse> {
    return await this.useCase.execute(pickUpRequestUuid, command)
  }
}
