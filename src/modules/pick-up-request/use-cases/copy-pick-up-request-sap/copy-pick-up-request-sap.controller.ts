import { <PERSON>, <PERSON><PERSON>, <PERSON> } from '@nestjs/common'
import { ApiT<PERSON><PERSON>, ApiOAuth2, ApiCreatedResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ApiConflictErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { CopyNonSubmittedPickUpRequestError } from '../../errors/copy-non-submitted-pick-up-request.error.js'
import { PickUpRequestNotFoundError } from '../../errors/pick-up-request-sap-not-found.error.js'
import { InvalidPickUpRequestCopyError } from '../../errors/invalid-pick-up-request-copy.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { CopyPickUpRequestSapUseCase } from './copy-pick-up-request-sap.use-case.js'
import { CopyPickUpRequestSapResponse } from './copy-pick-up-request-sap.response.js'

@ApiTags('Pick-up request')
@ApiOAuth2([])
@Controller('pick-up-requests/sap/:requestNumber/copy')
export class CopyPickUpRequestSapController {
  constructor (
    private readonly useCase: CopyPickUpRequestSapUseCase
  ) {}

  @Post()
  @GlobalCustomerRequired()
  @Permissions(Permission.PICK_UP_REQUEST_MANAGE)
  @ApiCreatedResponse({ type: CopyPickUpRequestSapResponse })
  @ApiConflictErrorResponse(CopyNonSubmittedPickUpRequestError, InvalidPickUpRequestCopyError)
  @ApiNotFoundErrorResponse(PickUpRequestNotFoundError)
  public async copyPickUpRequest (
    @Param('requestNumber') requestNumber: string
  ): Promise<CopyPickUpRequestSapResponse> {
    return await this.useCase.execute(requestNumber)
  }
}
