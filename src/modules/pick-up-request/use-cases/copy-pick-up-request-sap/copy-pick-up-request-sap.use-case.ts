import { randomUUID } from 'crypto'
import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { SapGetPickUpRequestDetailUseCase } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetPickUpRequestDetailResponse } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.response.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { PickUpRequestEntityBuilder } from '../../tests/pick-up-request-entity.builder.js'
import { MapUniformPickUpRequestDetailSapService } from '../../services/map-uniform-pick-up-request-detail-sap.service.js'
import { UniformDetailPickUpRequest } from '../../types/uniform-pick-up-request-detail.type.js'
import { CopyPickUpRequestValidator } from './copy-pick-up-request.validator.js'
import { CopyPickUpRequestSapResponse } from './copy-pick-up-request-sap.response.js'
import { PickUpRequestSapCopiedEvent } from './pick-up-request-sap-copied.event.js'

@Injectable()
export class CopyPickUpRequestSapUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(PickUpRequest)
    private readonly repository: Repository<PickUpRequest>,
    private readonly validator: CopyPickUpRequestValidator,
    private readonly sapGetPickUpRequestDetailUseCase: SapGetPickUpRequestDetailUseCase
  ) {}

  async execute (
    requestNumber: string
  ): Promise<CopyPickUpRequestSapResponse> {
    const sapPickUpRequest = await this.getSapPickUpRequest(requestNumber)

    await this.validator.validate(requestNumber, sapPickUpRequest)

    const uniFormPickUpRequest = MapUniformPickUpRequestDetailSapService
      .mapResultToUniformPickUpRequest(sapPickUpRequest, null, [], [])

    const copy = this.copyFromUniformDetailPickUpRequest(uniFormPickUpRequest)

    await transaction(this.dataSource, async () => {
      await this.repository.insert(copy)
      await this.eventEmitter.emitOne(new PickUpRequestSapCopiedEvent(requestNumber, copy.uuid))
    })

    return new CopyPickUpRequestSapResponse(copy)
  }

  private async getSapPickUpRequest (
    requestNumber: string
  ): Promise<SapGetPickUpRequestDetailResponse[]> {
    const sapQuery = new SapQuery<SapGetPickUpRequestDetailResponse>()
      .where('Reqno', requestNumber)

    return await this.sapGetPickUpRequestDetailUseCase.execute(sapQuery)
  }

  private copyFromUniformDetailPickUpRequest (
    uniformPickUpRequest: UniformDetailPickUpRequest
  ): PickUpRequest {
    return new PickUpRequestEntityBuilder()
      .withUuid(randomUUID())
      .withCustomerId(uniformPickUpRequest.customer?.id ?? null)
      .withWasteProducerId(uniformPickUpRequest.wasteProducer?.id ?? null)
      .withPickUpAddressId(uniformPickUpRequest.pickUpAddresses.map(address => address.id))
      .withTransportMode(uniformPickUpRequest.transportMode)
      .withIsTransportByIndaver(uniformPickUpRequest.isTransportByIndaver)
      .withMaterials(uniformPickUpRequest.materialsWithContractLine)
      .withIsOnlyPackagingRequest(false)
      .withTotalQuantityPallets(uniformPickUpRequest.totalQuantityPallets)
      .withIsReturnPackaging(uniformPickUpRequest.isReturnPackaging)
      .withPackagingRemark(uniformPickUpRequest.packagingRemark)
      .withCreatedByUserUuid(this.authContext.getUserUuidOrFail())
      .withRequestNumber(null)
      .withStartDate(null)
      .withEndDate(null)
      .withStartTime(null)
      .withRemarks(null)
      .withSubmittedOn(null)
      .withRequestNumber(null)
      .withDeliveryAddressId(null)
      .withSendCopyToContracts([])
      .build()
  }
}
