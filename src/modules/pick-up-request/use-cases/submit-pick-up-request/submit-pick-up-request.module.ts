import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { PickUpRequestGeneralInfoValidator } from '../../validators/pick-up-request-general-info.validator.js'
import { PickUpRequestTransportValidator } from '../../validators/pick-up-request-transport.validator.js'
import { PickUpRequestWasteDetailValidator } from '../../validators/pick-up-request-waste-detail.validator.js'
import { ContractLineModule } from '../../../contract-line/contract-line.module.js'
import { PickUpRequestPlanningValidator } from '../../validators/pick-up-request-planning.validator.js'
import { FileModule } from '../../../files/file.module.js'
import { CreatePickUpRequestSapUseCase } from '../create-pick-up-request-sap/create-pick-up-request-sap.use-case.js'
import { SapModule } from '../../../sap/sap.module.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { CustomerModule } from '../../../customer/customer.module.js'
import { PickUpRequestServicesModule } from '../../services/services.module.js'
import { SubmitPickUpRequestController } from './submit-pick-up-request.controller.js'
import { SubmitPickUpRequestUseCase } from './submit-pick-up-request.use-case.js'
import { SubmitPickUpRequestValidator } from './submit-pick-up-request.validator.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([PickUpRequest, File]),
    FileModule,
    ContractLineModule,
    DomainEventEmitterModule,
    SapModule,
    CustomerModule,
    PickUpRequestServicesModule
  ],
  controllers: [
    SubmitPickUpRequestController
  ],
  providers: [
    SubmitPickUpRequestUseCase,
    CreatePickUpRequestSapUseCase,

    SubmitPickUpRequestValidator,
    PickUpRequestGeneralInfoValidator,
    PickUpRequestTransportValidator,
    PickUpRequestWasteDetailValidator,
    PickUpRequestPlanningValidator
  ]
})
export class SubmitPickUpRequestModule {}
