import { Controller, HttpCode, Post } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { ApiBadRequestErrorResponse, ApiConflictErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { PickUpRequestAlreadySubmitted } from '../../errors/pick-up-request-already-submitted.error.js'
import { ContractLineNotAccessibleError } from '../../../contract-line/errors/contract-line-not-accessible.error.js'
import { ContractLineNotOfCustomerError } from '../../../contract-line/errors/contract-line-not-of-customer.error.js'
import { ContractLineNotOfPickUpAddressesError } from '../../../contract-line/errors/contract-line-not-of-pick-up-addresses.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { SubmitPickUpRequestResponse } from './submit-pick-up-request.response.js'
import { SubmitPickUpRequestUseCase } from './submit-pick-up-request.use-case.js'

@ApiTags('Pick-up request')
@Controller('pick-up-requests/:uuid/submit')
@ApiOAuth2([])
export class SubmitPickUpRequestController {
  constructor (
    private readonly useCase: SubmitPickUpRequestUseCase
  ) {}

  @Post()
  @GlobalCustomerRequired()
  @HttpCode(200)
  @Permissions(Permission.PICK_UP_REQUEST_MANAGE)
  @ApiOkResponse({
    description: 'Pick up request submitted',
    type: SubmitPickUpRequestResponse
  })
  @ApiNotFoundErrorResponse(NotFoundError)
  @ApiConflictErrorResponse(PickUpRequestAlreadySubmitted)
  @ApiBadRequestErrorResponse(
    ContractLineNotAccessibleError,
    ContractLineNotOfCustomerError,
    ContractLineNotOfPickUpAddressesError
  )
  async submitPickUpRequest (
    @UuidParam('uuid') pickUpRequestUuid: string
  ): Promise<SubmitPickUpRequestResponse> {
    return await this.useCase.execute(pickUpRequestUuid)
  }
}
