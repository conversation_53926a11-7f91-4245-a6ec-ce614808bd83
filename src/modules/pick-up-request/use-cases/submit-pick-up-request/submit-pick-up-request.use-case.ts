import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { CreatePickUpRequestSapUseCase } from '../create-pick-up-request-sap/create-pick-up-request-sap.use-case.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { PickUpRequestSubmittedEvent } from './pick-up-request-submitted.event.js'
import { SubmitPickUpRequestValidator } from './submit-pick-up-request.validator.js'
import { SubmitPickUpRequestResponse } from './submit-pick-up-request.response.js'

@Injectable()
export class SubmitPickUpRequestUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly validator: SubmitPickUpRequestValidator,
    private readonly createPickUpRequestSapUseCase: CreatePickUpRequestSapUseCase,
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (pickUpRequestUuid: string): Promise<SubmitPickUpRequestResponse> {
    const pickUpRequest = await this.validator.validate(pickUpRequestUuid)

    const requestNumber = await this.createPickUpRequestSapUseCase.execute(pickUpRequestUuid)

    pickUpRequest.submittedOn = new Date()
    pickUpRequest.requestNumber = requestNumber

    await transaction(this.dataSource, async () => {
      await this.pickUpRequestRepository.save(pickUpRequest)

      await this.eventEmitter.emitOne(new PickUpRequestSubmittedEvent(pickUpRequestUuid))
    })

    return new SubmitPickUpRequestResponse(pickUpRequest)
  }
}
