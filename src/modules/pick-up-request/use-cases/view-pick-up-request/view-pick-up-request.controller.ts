import { Controller, Get } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewPickUpRequestUseCase } from './view-pick-up-request.use-case.js'
import { ViewPickUpRequestResponse } from './view-pick-up-request.response.js'

@ApiTags('Pick-up request')
@Controller('pick-up-requests/:uuid')
@ApiOAuth2([])
export class ViewPickUpRequestController {
  constructor (
    private readonly useCase: ViewPickUpRequestUseCase
  ) {}

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.PICK_UP_REQUEST_READ, Permission.PICK_UP_REQUEST_MANAGE)
  @ApiOkResponse({ type: ViewPickUpRequestResponse })
  @ApiNotFoundErrorResponse(NotFoundError)
  async viewPickUpRequest (
    @UuidParam('uuid') pickUpRequestUuid: string
  ): Promise<ViewPickUpRequestResponse> {
    return await this.useCase.execute(pickUpRequestUuid)
  }
}
