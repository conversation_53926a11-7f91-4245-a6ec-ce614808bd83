import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewPickUpRequestUseCase } from '../view-pick-up-request.use-case.js'
import { PickUpRequest } from '../../../entities/pick-up-request.entity.js'
import { PickUpRequestEntityBuilder } from '../../../tests/pick-up-request-entity.builder.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { LoadPickUpRequestDataService } from '../../../services/load-pick-up-request-data.service.js'
import { WasteProducerBuilder } from '../../../../waste-producer/tests/waste-producer.builder.js'
import { PickUpAddressBuilder } from '../../../../pick-up-address/tests/pick-up-address.builder.js'
import { CustomerBuilder } from '../../../../customer/tests/customer.builder.js'

describe('View pick up request use-case unit test', () => {
  let useCase: ViewPickUpRequestUseCase

  let userUuid: string
  let pickUpRequest: PickUpRequest

  let pickUpRequestRepository: SinonStubbedInstance<Repository<PickUpRequest>>
  let loadPickUpRequestDataService: SinonStubbedInstance<LoadPickUpRequestDataService>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()
    pickUpRequest = new PickUpRequestEntityBuilder()
      .withCustomer(new CustomerBuilder().build())
      .withWasteProducer(new WasteProducerBuilder().build())
      .withPickUpAddresses([new PickUpAddressBuilder().build()])
      .build()

    const authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: userUuid
    })

    pickUpRequestRepository = createStubInstance<Repository<PickUpRequest>>(
      Repository<PickUpRequest>
    )
    pickUpRequestRepository.findOneOrFail.resolves(pickUpRequest)

    loadPickUpRequestDataService = createStubInstance(LoadPickUpRequestDataService)

    useCase = new ViewPickUpRequestUseCase(
      authContext,
      pickUpRequestRepository,
      loadPickUpRequestDataService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    loadPickUpRequestDataService.execute.resolves()
  }

  it('Calls all methods once', async () => {
    await useCase.execute(pickUpRequest.uuid)

    assert.calledOnce(pickUpRequestRepository.findOneOrFail)
    assert.calledOnce(loadPickUpRequestDataService.execute)
  })
})
