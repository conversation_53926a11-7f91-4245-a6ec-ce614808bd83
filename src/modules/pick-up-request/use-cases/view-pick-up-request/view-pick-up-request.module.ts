import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { File } from '../../../files/entities/file.entity.js'
import { PickUpRequestServicesModule } from '../../services/services.module.js'
import { ViewPickUpRequestController } from './view-pick-up-request.controller.js'
import { ViewPickUpRequestUseCase } from './view-pick-up-request.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([PickUpRequest, File]),
    PickUpRequestServicesModule
  ],
  controllers: [
    ViewPickUpRequestController
  ],
  providers: [
    ViewPickUpRequestUseCase
  ]
})
export class ViewPickUpRequestModule {}
