import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { IsNull, Repository } from 'typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { LoadPickUpRequestDataService } from '../../services/load-pick-up-request-data.service.js'
import { ViewPickUpRequestResponse } from './view-pick-up-request.response.js'

@Injectable()
export class ViewPickUpRequestUseCase {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    private readonly loadPickUpRequestDataService: LoadPickUpRequestDataService
  ) {}

  async execute (pickUpRequestUuid: string): Promise<ViewPickUpRequestResponse> {
    const authUserUuid = this.authContext.getUserUuidOrFail()

    const pickUpRequest = await this.pickUpRequestRepository.findOneOrFail({
      where: [
        {
          uuid: pickUpRequestUuid,
          createdByUserUuid: authUserUuid,
          customerId: this.authContext.getSelectedCustomerId() ?? undefined,
          templateName: IsNull()
        },
        {
          uuid: pickUpRequestUuid,
          createdByUserUuid: authUserUuid,
          customerId: IsNull(),
          templateName: IsNull()
        }
      ]
    })

    await this.loadPickUpRequestDataService.execute(pickUpRequest)

    return new ViewPickUpRequestResponse(pickUpRequest)
  }
}
