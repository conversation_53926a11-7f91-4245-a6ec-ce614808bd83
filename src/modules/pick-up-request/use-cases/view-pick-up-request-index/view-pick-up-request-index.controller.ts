import { Controller, Get, Query } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewPickUpRequestIndexResponse } from './view-pick-up-request-index.response.js'
import { ViewPickUpRequestIndexQuery } from './query/view-pick-up-request-index.query.js'
import { ViewPickUpRequestIndexUseCase } from './view-pick-up-request-index.use-case.js'

@ApiTags('Pick-up request')
@ApiOAuth2([])
@Controller('pick-up-requests')
export class ViewPickUpRequestIndexController {
  constructor (
    private readonly useCase: ViewPickUpRequestIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(
    Permission.PICK_UP_REQUEST_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ
  )
  @ApiOkResponse({ type: ViewPickUpRequestIndexResponse })
  public async viewPickUpRequestIndex (
    @Query() query: ViewPickUpRequestIndexQuery
  ): Promise<ViewPickUpRequestIndexResponse> {
    return await this.useCase.execute(query)
  }
}
