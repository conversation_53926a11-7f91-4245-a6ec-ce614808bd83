import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { mapPickUpRequestStatusToSapValue, PickUpRequestStatus } from '../../enums/pick-up-request-status.enum.js'
import { MapUniformPickUpRequestDbService } from '../../services/map-uniform-pick-up-request-db.service.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetPickUpRequestIndexResponse } from '../../../sap/use-cases/get-pick-up-request-index/get-pick-up-request-index.response.js'
import { SapGetPickUpRequestIndexUseCase } from '../../../sap/use-cases/get-pick-up-request-index/get-pick-up-request-index.use-case.js'
import { MapUniformPickUpRequestSapService } from '../../services/map-uniform-pick-up-request-sap.service.js'
import { FilterOperator } from '../../../sap/enums/odata-filter-operator.enum.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { ViewPickUpRequestIndexResponse, ViewPickUpRequestIndexResponseMeta } from './view-pick-up-request-index.response.js'
import { ViewPickUpRequestIndexQuery } from './query/view-pick-up-request-index.query.js'
import { ViewPickUpRequestIndexValidator } from './view-pick-up-request-index.validator.js'
import { ViewPickUpRequestIndexRepository } from './view-pick-up-request-index.repository.js'
import { ViewPickUpRequestIndexDbQueryKey } from './query/view-pick-up-request-index.db-query-key.js'
import { ViewPickUpRequestIndexSapQueryKey } from './query/view-pick-up-request-index.sap-query-key.js'

@Injectable()
export class ViewPickUpRequestIndexUseCase {
  constructor (
    private readonly validator: ViewPickUpRequestIndexValidator,
    private readonly authContext: AuthContext,
    private readonly repository: ViewPickUpRequestIndexRepository,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly sapGetPickUpRequestIndex: SapGetPickUpRequestIndexUseCase
  ) {}

  async execute (
    query: ViewPickUpRequestIndexQuery
  ): Promise<ViewPickUpRequestIndexResponse> {
    this.validator.validate(query)

    const user = this.authContext.getAuthOrFail()

    if (query.filter.statuses.includes(PickUpRequestStatus.DRAFT)) {
      const dbPickUpRequests = await this.repository.getDraftPickUpRequestsByUserUuid(
        user.uuid,
        query.pagination
      )

      const uniformPickUpRequests = MapUniformPickUpRequestDbService
        .mapResultsToUniformPickUpRequests(
          dbPickUpRequests
        )

      return new ViewPickUpRequestIndexResponse(
        uniformPickUpRequests,
        new ViewPickUpRequestIndexResponseMeta(
          dbPickUpRequests.length > 0
            ? new ViewPickUpRequestIndexDbQueryKey(
              dbPickUpRequests.at(-1)!.startDate === null ? 'null' : dbPickUpRequests.at(-1)!.startDate,
              dbPickUpRequests.at(-1)!.uuid
            )
            : null
        )
      )
    }

    const sapQuery = await this.getSapQuery(query)
    const sapResponse = await this.sapGetPickUpRequestIndex.execute(sapQuery)

    const uniformPickUpRequests = MapUniformPickUpRequestSapService
      .mapResultsToUniformPickUpRequests(
        sapResponse.items
      )

    return new ViewPickUpRequestIndexResponse(
      uniformPickUpRequests,
      new ViewPickUpRequestIndexResponseMeta(
        sapResponse.skipToken !== null
          ? new ViewPickUpRequestIndexSapQueryKey(sapResponse.skipToken)
          : null
      )
    )
  }

  private async getSapQuery (
    query: ViewPickUpRequestIndexQuery
  ): Promise<SapQuery<SapGetPickUpRequestIndexResponse>> {
    const sapStatuses: string[] = []

    const filterStatuses = query.filter.statuses
    const filterStatusesWithoutIndaScan = filterStatuses
      .filter(status => status !== PickUpRequestStatus.INDASCAN_DRAFT)

    for (const status of filterStatusesWithoutIndaScan) {
      sapStatuses.push(...mapPickUpRequestStatusToSapValue(status))
    }

    const sapQuery = new SapQuery<SapGetPickUpRequestIndexResponse>(query)
      .addSelect([
        'Reqno',
        'Status',
        'Arktx',
        'Kunnr',
        'Kunnrname',
        'Kunnry2',
        'Kunnry2name',
        'Kunnrwe',
        'Kunnrwename',
        'Yyklantmat',
        'Vbeln',
        'Posnr',
        'Wastetype',
        'Dateapplication',
        'Accountmanager',
        'Costcenter',
        'Transportby',
        'Requesteddate',
        'Confirmeddate',
        'Salesdoc',
        'Dangerous',
        'Nameapplicant',
        'Ordernumber',
        'Cont1nr',
        'Eural',
        'Tfsnumber'
      ])

    if (filterStatuses.includes(PickUpRequestStatus.INDASCAN_DRAFT)) {
      sapQuery.where((qb) => {
        return qb.where('Status', mapPickUpRequestStatusToSapValue(PickUpRequestStatus.INDASCAN_DRAFT)[0])
          .andWhere('Extid', '', FilterOperator.NOT_EQUAL)
      })

      if (filterStatusesWithoutIndaScan.length > 0) {
        sapQuery.orWhere((qb) => {
          qb.where('Status', sapStatuses[0])
          for (let i = 1; i < sapStatuses.length; i++) {
            qb.orWhere('Status', sapStatuses[i])
          }
          return qb
        })
      }
    } else if (filterStatusesWithoutIndaScan.length > 0) {
      sapQuery.where((qb) => {
        qb.where('Status', sapStatuses[0])
        for (let i = 1; i < sapStatuses.length; i++) {
          qb.orWhere('Status', sapStatuses[i])
        }
        return qb
      })
    }

    // Add filters based on selected customer and accessible waste producers
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null) {
      sapQuery.andWhere('Kunnr', selectedCustomerId)

      const restrictedWasteProducerIds = await this.userWasteProducerAuthService
        .getRestrictedWasteProducerIds(
          this.authContext.getAzureEntraUpnOrFail(),
          selectedCustomerId
        )
      if (restrictedWasteProducerIds !== undefined && restrictedWasteProducerIds.length > 0) {
        sapQuery.andWhere((qb) => {
          for (let i = 0; i < restrictedWasteProducerIds.length; i++) {
            if (i === 0) {
              qb.where('Kunnry2', restrictedWasteProducerIds[i])
            } else {
              qb.orWhere('Kunnry2', restrictedWasteProducerIds[i])
            }
          }
          return qb
        })
      }
    }

    // TODO https://linear.app/wisemen/issue/IND-322/api-retrieve-submitted-pick-up-requests-from-sap#comment-b5958772
    // .addOrderBy('Requesteddate', 'desc')

    return sapQuery
  }
}
