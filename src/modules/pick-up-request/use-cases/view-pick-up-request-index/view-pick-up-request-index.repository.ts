import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Brackets, Repository } from 'typeorm'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { DEFAULT_LIMIT } from '../../../typesense/param-builders/search-params.builder.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewPickUpRequestIndexPaginationQuery } from './query/view-pick-up-request-index.pagination-query.js'
import { ViewPickUpRequestIndexDbQueryKey } from './query/view-pick-up-request-index.db-query-key.js'

@Injectable()
export class ViewPickUpRequestIndexRepository {
  constructor (
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    private readonly authContext: AuthContext
  ) {}

  async getDraftPickUpRequestsByUserUuid (
    userUuid: string,
    pagination?: ViewPickUpRequestIndexPaginationQuery
  ): Promise<PickUpRequest[]> {
    const query = this.pickUpRequestRepository.createQueryBuilder('pickUpRequest')
      .where('pickUpRequest.submittedOn IS NULL')
      .andWhere('pickUpRequest.createdByUserUuid = :userUuid', { userUuid })
      .andWhere('pickUpRequest.weeklyPlanningRequestUuid IS NULL')
      .andWhere('pickUpRequest.templateName IS NULL')
      .leftJoinAndSelect('pickUpRequest.createdByUser', 'createdByUser')
      .orderBy('pickUpRequest.startDate', 'DESC', 'NULLS LAST')
      .addOrderBy('pickUpRequest.uuid', 'DESC')
      .limit(pagination?.limit ?? DEFAULT_LIMIT)

    if (pagination?.key != null && pagination.key instanceof ViewPickUpRequestIndexDbQueryKey) {
      const { startDate, uuid } = pagination.key

      if (startDate === null) {
        query.andWhere('pickUpRequest.startDate IS NULL AND pickUpRequest.uuid < :uuid', { uuid })
      } else {
        query.andWhere(
          new Brackets((qb) => {
            qb.where('(pickUpRequest.startDate, pickUpRequest.uuid) < (:startDate, :uuid)', { startDate, uuid })
              .orWhere('pickUpRequest.startDate IS NULL')
          })
        )
      }
    }

    if (this.authContext.getSelectedCustomerId() != null) {
      query.andWhere(
        new Brackets((qb) => {
          qb.where('pickUpRequest.customerId IS NULL')
            .orWhere('pickUpRequest.customerId = :selectedCustomerId', {
              selectedCustomerId: this.authContext.getSelectedCustomerId()
            })
        })
      )
    }

    const pickUpRequest = await query.getMany()

    return pickUpRequest
  }
}
