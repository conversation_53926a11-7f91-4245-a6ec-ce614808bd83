import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { SinonStubbedInstance, createStubInstance, assert } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { ViewPickUpRequestIndexUseCase } from '../view-pick-up-request-index.use-case.js'
import { ViewPickUpRequestIndexValidator } from '../view-pick-up-request-index.validator.js'
import { ViewPickUpRequestIndexRepository } from '../view-pick-up-request-index.repository.js'
import { SapGetPickUpRequestIndexUseCase } from '../../../../sap/use-cases/get-pick-up-request-index/get-pick-up-request-index.use-case.js'
import { PickUpRequestStatus } from '../../../enums/pick-up-request-status.enum.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetPickUpRequestIndexResponse } from '../../../../sap/use-cases/get-pick-up-request-index/get-pick-up-request-index.response.js'
import { UserWasteProducerAuthService } from '../../../../auth/services/user-waste-producer-auth.service.js'
import { ViewPickUpRequestIndexQueryBuilder } from './view-pick-up-request-index-query.builder.js'
import { ViewPickUpRequestIndexFilterQueryBuilder } from './view-pick-up-request-index.filter.builder.js'

describe('View pick-up request index use-case unit test', () => {
  let useCase: ViewPickUpRequestIndexUseCase

  let validator: SinonStubbedInstance<ViewPickUpRequestIndexValidator>
  let authContext: SinonStubbedInstance<AuthContext>
  let repository: SinonStubbedInstance<ViewPickUpRequestIndexRepository>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>
  let sapGetPickUpRequestIndex: SinonStubbedInstance<SapGetPickUpRequestIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    validator = createStubInstance(ViewPickUpRequestIndexValidator)
    authContext = createStubInstance(AuthContext)
    repository = createStubInstance(ViewPickUpRequestIndexRepository)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)
    sapGetPickUpRequestIndex = createStubInstance(SapGetPickUpRequestIndexUseCase)

    useCase = new ViewPickUpRequestIndexUseCase(
      validator,
      authContext,
      repository,
      userWasteProducerAuthService,
      sapGetPickUpRequestIndex
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    validator.validate.resolves()
    authContext.getAuthOrFail.returns({
      zitadelSub: randomUUID(),
      uuid: randomUUID(),
      impersonateUserUuid: null,
      azureEntraId: randomUUID(),
      azureEntraUpn: randomUUID(),
      selectedCustomerId: null
    })
    repository.getDraftPickUpRequestsByUserUuid.resolves([])
    userWasteProducerAuthService.getRestrictedWasteProducerIds.resolves(undefined)
    sapGetPickUpRequestIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetPickUpRequestIndexResponse>().build()
    )
  }

  describe('Source database', () => {
    it('Retrieves result from database when database source given', async () => {
      const query = new ViewPickUpRequestIndexQueryBuilder()
        .withFilter(
          new ViewPickUpRequestIndexFilterQueryBuilder()
            .addStatus(PickUpRequestStatus.DRAFT)
            .build()
        )
        .build()

      await useCase.execute(query)

      assert.calledOnce(repository.getDraftPickUpRequestsByUserUuid)
    })
  })

  describe('Source SAP', () => {
    it('Retrieves result from SAP when SAP source given', async () => {
      const query = new ViewPickUpRequestIndexQueryBuilder()
        .withFilter(
          new ViewPickUpRequestIndexFilterQueryBuilder()
            .addStatus(PickUpRequestStatus.PENDING)
            .build()
        )
        .build()

      await useCase.execute(query)

      assert.calledOnce(sapGetPickUpRequestIndex.execute)
    })
  })
})
