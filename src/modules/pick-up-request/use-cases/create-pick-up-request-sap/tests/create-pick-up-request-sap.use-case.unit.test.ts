import { afterEach, before, describe, it } from 'node:test'
import Sinon, { createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { randNumber } from '@ngneat/falso'
import { expect } from 'expect'
import { CreatePickUpRequestSapUseCase } from '../create-pick-up-request-sap.use-case.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { PickUpRequest } from '../../../entities/pick-up-request.entity.js'
import { ValidPickUpRequestEntityBuilder } from '../../../tests/valid-pick-up-request-entity.builder.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'
import { FileLinkService } from '../../../../files/services/file-link.service.js'
import { SubmitPickUpRequestService } from '../../../services/submit-pick-up-request.service.js'

describe('Create pick-up request SAP use-case unit test', () => {
  let useCase: CreatePickUpRequestSapUseCase

  let pickUpRequest: PickUpRequest

  let requestNumber: string

  let pickUpRequestRepository: SinonStubbedInstance<Repository<PickUpRequest>>
  let fileLinkService: SinonStubbedInstance<FileLinkService>
  let submitPickUpRequestService: SinonStubbedInstance<SubmitPickUpRequestService>

  before(() => {
    TestBench.setupUnitTest()

    pickUpRequest = new ValidPickUpRequestEntityBuilder()
      .createdByUser(new UserEntityBuilder().build())
      .build()

    pickUpRequestRepository = createStubInstance<Repository<PickUpRequest>>(
        Repository<PickUpRequest>
    )
    fileLinkService = createStubInstance(FileLinkService)
    submitPickUpRequestService = createStubInstance(SubmitPickUpRequestService)

    requestNumber = randNumber({ min: 10000000, max: 99999999 }).toString()

    useCase = new CreatePickUpRequestSapUseCase(
      pickUpRequestRepository,
      fileLinkService,
      submitPickUpRequestService
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    fileLinkService.loadFileLinks.resolves([])
    pickUpRequestRepository.findOneOrFail.resolves(pickUpRequest)
    submitPickUpRequestService.execute.resolves(requestNumber)
  }

  it('Returns inquiry number', async () => {
    const response = await useCase.execute(pickUpRequest.uuid)

    expect(response).toEqual(requestNumber)
  })
})
