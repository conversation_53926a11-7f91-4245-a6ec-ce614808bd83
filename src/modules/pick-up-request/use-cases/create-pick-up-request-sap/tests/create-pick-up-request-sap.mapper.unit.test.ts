import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { expect } from 'expect'
import dayjs from 'dayjs'
import { CreatePickUpRequestSapMapper } from '../create-pick-up-request-sap.mapper.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { PickUpRequest } from '../../../entities/pick-up-request.entity.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'
import { ValidPickUpRequestEntityBuilder } from '../../../tests/valid-pick-up-request-entity.builder.js'
import { mapTransportModeToSapValue } from '../../../enums/pick-up-transport-mode.enum.js'
import { mapWasteMeasurementUnitToSapValue } from '../../../../waste-inquiry/enums/waste-measurement-unit.enum.js'

describe('Create pick-up request SAP mapper unit test', () => {
  let pickUpRequest: PickUpRequest

  before(() => {
    TestBench.setupUnitTest()

    pickUpRequest = new ValidPickUpRequestEntityBuilder()
      .createdByUser(new UserEntityBuilder().build())
      .build()
  })

  it ('maps a valid pick-up request', () => {
    const customerSalesOrganizationId = randomUUID()
    const result = CreatePickUpRequestSapMapper.mapSubmittedPickUpRequestToSapCommand(
      pickUpRequest,
      customerSalesOrganizationId
    )

    expect(result).toStrictEqual({
      Reqno: '',
      ToDisposalRequest: pickUpRequest.materials.map(material => ({
        Reqno: '',
        NameApplicant: pickUpRequest.createdByUser!.fullName,
        EmailApplicant: pickUpRequest.createdByUser!.email,
        Kunnr: pickUpRequest.customerId,
        Vkorg: customerSalesOrganizationId,
        Vbeln: material.contractNumber,
        KunnrY2: pickUpRequest.wasteProducerId,
        KunnrWe: undefined,
        ActionNr: undefined,
        Arktx: undefined,
        Dangerous: undefined,
        Eural: undefined,
        Matnr: undefined,
        Posnr: material.contractItem,
        Yyasn: undefined,
        Yyklantmat: undefined,
        Yypackaginggrp: undefined,
        QuantityPallets: pickUpRequest.totalQuantityPallets!.toString(),
        RequestedDate: dayjs(pickUpRequest.startDate).format('YYYY-MM-DDTHH:mm'),
        ReqDateTo: dayjs(pickUpRequest.endDate).format('YYYY-MM-DDTHH:mm'),
        TransportBy: 'X',
        ReturnPackaging: pickUpRequest.isReturnPackaging,
        ReturnPackagingRemark: pickUpRequest.packagingRemark,
        WasteType: mapTransportModeToSapValue(pickUpRequest.transportMode!),
        IdWeekPlanning: undefined,
        WeightVolume: material.estimatedWeightOrVolumeValue!.toString(),
        WeightUom: mapWasteMeasurementUnitToSapValue(material.estimatedWeightOrVolumeUnit!),
        TypeRecipient: material.packagingType,
        NrOPallets: material.quantityPallets!.toString(),
        QuantityBarrels: material.quantityPackages?.toString(),
        QuantityLabels: material.quantityLabels?.toString(),
        CostCenter: material.costCenter,
        OrderNumber: material.poNumber,
        Yyun: material.unNumber,

        ContainerType: undefined,
        Cont1Weight: undefined,
        Cont1Volume: undefined,
        Cont1Uom: undefined,
        Cont1Nr: undefined,
        Cont1Transport: undefined,
        BulkType: undefined,
        Cont1Cover: undefined,
        HazardInducers: undefined,
        QuanContainers: undefined,
        ReconciliationNr: undefined,
        SerialNumber: undefined,
        Tfsnumber: undefined,
        CreateLangu: 'EN',
        DateApplication: expect.any(String)
      })),
      ToWMRComment: undefined,
      ToWMRContacts: [],
      ToWMRAttachments: []
    })
  })
})
