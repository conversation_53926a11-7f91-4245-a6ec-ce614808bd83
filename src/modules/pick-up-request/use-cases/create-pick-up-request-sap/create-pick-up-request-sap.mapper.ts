import assert from 'assert'
import dayjs from 'dayjs'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { mapTransportModeToSapValue, PickUpTransportMode } from '../../enums/pick-up-transport-mode.enum.js'
import { mapWasteMeasurementUnitToSapValue } from '../../../waste-inquiry/enums/waste-measurement-unit.enum.js'
import { SapCreatePickUpRequestCommand, SapCreatePickUpRequestWasteItemCommand } from '../../../sap/use-cases/create-pick-up-request/create-pick-up-request.command.js'
import { mapLocaleToSapLocale } from '../../../localization/enums/locale.enum.js'
import { getCurrentLanguage } from '../../../localization/helpers/translate.helper.js'
import { SapCreatePackagingRequestCommand } from '../../../sap/use-cases/create-pick-up-request/types/create-package-request.comand.type.js'
import { PickUpRequestMaterial } from '../../types/pick-up-request-material.type.js'
import { CreatePackagingRequestSapMapper } from '../../../packaging-request/use-cases/create-packaging-request-sap/create-packaging-request-sap.mapper.js'
import { mapPackingGroupToSapValue } from '../../../waste-inquiry/enums/packaging-group.enum.js'

type SapMaterial = SapCreatePickUpRequestWasteItemCommand | SapCreatePackagingRequestCommand

export class CreatePickUpRequestSapMapper {
  static mapSubmittedPickUpRequestToSapCommand (
    pickUpRequest: PickUpRequest,
    customerSalesOrganization: string | null,
    weeklyPlanningId?: string
  ): SapCreatePickUpRequestCommand {
    assert(pickUpRequest.createdByUser !== undefined)
    assert(pickUpRequest.additionalFiles !== undefined)

    const wasteItems: SapMaterial[] = []

    for (const material of pickUpRequest.materials) {
      const sapCommand = this.addWasteMaterialSapCommand(
        pickUpRequest,
        material,
        customerSalesOrganization,
        weeklyPlanningId
      )

      wasteItems.push(sapCommand)
    }

    for (const material of pickUpRequest.packagingRequestMaterials) {
      const sapCommand = CreatePackagingRequestSapMapper.mapPackagingRequestMaterialSapCommand(
        pickUpRequest,
        material,
        customerSalesOrganization
      )

      wasteItems.push(sapCommand)
    }

    return {
      Reqno: '',
      ToDisposalRequest: wasteItems,
      ToWMRComment: pickUpRequest.remarks != null
        ? [{
            Reqno: '',
            Comment: pickUpRequest.remarks
          }]
        : undefined,
      ToWMRContacts: pickUpRequest.sendCopyToContacts.map(contact => ({
        Reqno: '',
        Email: contact.email,
        Name1: contact.firstName ?? '',
        Name2: contact.lastName ?? ''
      })),
      ToWMRAttachments: pickUpRequest.additionalFiles.map((fileLink, index) => {
        assert(fileLink.file !== undefined)

        return {
          Reqno: '',
          Posnr: fileLink.order?.toString() ?? (index + 1).toString(),
          Filename: fileLink.file.name
        }
      })
    }
  }

  static addWasteMaterialSapCommand (
    pickUpRequest: PickUpRequest,
    material: PickUpRequestMaterial,
    customerSalesOrganization: string | null,
    inquiryNumber?: string
  ): SapCreatePickUpRequestWasteItemCommand {
    assert(pickUpRequest.createdByUser !== undefined)

    return {
      Reqno: '',
      IdWeekPlanning: inquiryNumber,
      NameApplicant: pickUpRequest.createdByUser.fullName,
      EmailApplicant: pickUpRequest.createdByUser.email,
      Kunnr: pickUpRequest.customerId ?? undefined,
      Vkorg: customerSalesOrganization ?? undefined,
      KunnrY2: pickUpRequest.wasteProducerId ?? undefined,
      KunnrWe: material.pickUpAddressId ?? undefined,
      WasteType: pickUpRequest.transportMode !== null
        ? mapTransportModeToSapValue(pickUpRequest.transportMode)
        : undefined,
      Vbeln: material.contractNumber,
      Posnr: material.contractItem,
      ActionNr: material.tcNumber ?? undefined,
      Matnr: material.materialNumber ?? undefined,
      Yyklantmat: material.customerReference ?? undefined,
      Dangerous: material.isHazardous != null
        ? material.isHazardous ? 'X' : ''
        : undefined,
      Arktx: material.wasteMaterial ?? undefined,
      Eural: material.ewcCode ?? undefined,
      Yyasn: material.asn ?? undefined,
      QuantityPallets: pickUpRequest.totalQuantityPallets?.toString() ?? undefined,
      NrOPallets: material.quantityPallets?.toString() ?? undefined,
      CostCenter: material.costCenter ?? undefined,
      OrderNumber: material.poNumber ?? undefined,
      Yyun: material.unNumber ?? undefined,
      TypeRecipient: material.packagingType ?? undefined,
      QuantityBarrels: material.quantityPackages?.toString() ?? undefined,
      QuanContainers: material.quantityContainers?.toString() ?? undefined,
      QuantityLabels: material.quantityLabels?.toString() ?? undefined,
      ContainerType: material.containerType ?? undefined,
      WeightVolume: pickUpRequest.transportMode !== PickUpTransportMode.BULK_SKIPS_CONTAINER
        ? material.estimatedWeightOrVolumeValue?.toString() ?? undefined
        : undefined,
      WeightUom: pickUpRequest.transportMode !== PickUpTransportMode.BULK_SKIPS_CONTAINER
        ? material.estimatedWeightOrVolumeUnit != null
          ? mapWasteMeasurementUnitToSapValue(material.estimatedWeightOrVolumeUnit)
          : undefined
        : undefined,
      Cont1Weight: pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
        ? material.estimatedWeightOrVolumeValue?.toString() ?? undefined
        : undefined,
      Cont1Uom: pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
        ? material.estimatedWeightOrVolumeUnit != null
          ? mapWasteMeasurementUnitToSapValue(material.estimatedWeightOrVolumeUnit)
          : undefined
        : undefined,
      Cont1Volume: pickUpRequest.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
        ? material.containerVolumeSize?.toString() ?? undefined
        : undefined,
      Cont1Nr: material.containerNumber ?? undefined,
      Cont1Transport: material.containerTransportType != null
        ? material.containerTransportType
        : undefined,
      BulkType: material.tankerType != null
        ? material.tankerType
        : undefined,
      Cont1Cover: material.isContainerCovered != null
        ? material.isContainerCovered ? '01' : '02' // Yes: 01, No: 02 (SAP)
        : undefined,
      RequestedDate: pickUpRequest.startDate !== null
        ? dayjs(
            pickUpRequest.startTime != null
              ? `${pickUpRequest.startDate} ${pickUpRequest.startTime}`
              : pickUpRequest.startDate
          ).format('YYYY-MM-DDTHH:mm')
        : undefined,
      ReqDateTo: pickUpRequest.endDate !== null
        ? dayjs(pickUpRequest.endDate).format('YYYY-MM-DDTHH:mm')
        : undefined,
      TransportBy: (pickUpRequest.isTransportByIndaver ?? false) ? 'X' : '',
      ReturnPackaging: pickUpRequest.isReturnPackaging ?? undefined,
      ReturnPackagingRemark: pickUpRequest.packagingRemark ?? undefined,
      Yypackaginggrp: material.packingGroup != null
        ? mapPackingGroupToSapValue(material.packingGroup)
        : undefined,
      ReconciliationNr: material.reconciliationNumber?.toString() ?? undefined,
      HazardInducers: material.hazardInducers ?? undefined,
      Tfsnumber: material.tfsNumber ?? undefined,
      SerialNumber: material.serialNumber ?? undefined,
      CreateLangu: mapLocaleToSapLocale(getCurrentLanguage()),
      DateApplication: dayjs().format('YYYY-MM-DDTHH:mm')
    }
  }
}
