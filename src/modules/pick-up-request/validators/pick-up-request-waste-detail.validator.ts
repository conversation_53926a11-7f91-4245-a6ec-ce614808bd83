import { Injectable } from '@nestjs/common'
import { ContractLineValidatorService } from '../../contract-line/validators/contract-line-validator.service.js'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { UpdatePickUpRequestCommand } from '../use-cases/update-pick-up-request/update-pick-up-request.command.js'
import { PickUpRequestWasteDetailsValidationUtil } from '../utils/pick-up-request-waste-detail-validation.util.js'
import { PickUpRequestMaterial } from '../types/pick-up-request-material.type.js'
import { MissingRequiredFieldError } from '../../exceptions/generic/missing-required-field.error.js'
import { FieldMustBeNullError } from '../../exceptions/generic/field-must-be-null.error.js'
import { numberOfMaterialsAllowed, PickUpTransportMode } from '../enums/pick-up-transport-mode.enum.js'
import { TooManyContractLines } from '../errors/too-many-contract-lines.error.js'
import { ExactlyOneContractLineExpectedError } from '../errors/exactly-one-contract-line-expected.error.js'
import { AuthContext } from '../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../auth/services/user-waste-producer-auth.service.js'

interface ValidateMaterialsOptions {
  materials?: PickUpRequestMaterial[]
  customerId: string | null
  pickUpAddressIds: string[]
}

@Injectable()
export class PickUpRequestWasteDetailValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly contractLineValidatorService: ContractLineValidatorService,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  private validationUtil: PickUpRequestWasteDetailsValidationUtil

  private errorPointerPrefix: string = '$'

  validateUpdate (
    _command: UpdatePickUpRequestCommand,
    pickUpRequest: PickUpRequest
  ): void {
    this.validationUtil = new PickUpRequestWasteDetailsValidationUtil(pickUpRequest)

    this.validateWasteDetail(pickUpRequest, false)
  }

  async validateSubmit (
    pickUpRequest: PickUpRequest,
    errorPointerPrefix?: string
  ): Promise<void> {
    this.errorPointerPrefix = errorPointerPrefix !== undefined
      ? errorPointerPrefix
      : '$'

    this.validationUtil = new PickUpRequestWasteDetailsValidationUtil(pickUpRequest)

    await this.validateMaterialAccess (
      {
        materials: pickUpRequest.materials,
        customerId: pickUpRequest.customerId,
        pickUpAddressIds: pickUpRequest.pickUpAddressIds
      }
    )

    this.validateWasteDetail(pickUpRequest)
  }

  private validateWasteDetail (pickUpRequest: PickUpRequest, isSubmit: boolean = true) {
    this.validateTransportMode(
      pickUpRequest.transportMode ?? null,
      isSubmit
    )

    this.validateTransportByIndaver(
      pickUpRequest.isTransportByIndaver ?? null,
      isSubmit
    )

    this.validateMaterials(
      pickUpRequest,
      pickUpRequest.transportMode ?? null,
      isSubmit
    )
  }

  private validateMaterials (
    pickUpRequest: PickUpRequest,
    transportMode: PickUpTransportMode | null,
    isSubmit: boolean
  ) {
    const materials = pickUpRequest.materials ?? []

    if (!this.validationUtil.isMaterialsAllowed) {
      if (materials.length > 0) {
        throw new FieldMustBeNullError({ pointer: `${this.errorPointerPrefix}.materials` })
      }
    } else {
      if (materials.length > numberOfMaterialsAllowed(transportMode)) {
        throw new TooManyContractLines()
      }
    }

    if (pickUpRequest.isWprPickUpRequest) {
      if (materials.length !== 1) {
        throw new ExactlyOneContractLineExpectedError()
      }
    }

    if (isSubmit && this.validationUtil.isMaterialsRequired) {
      if (materials.length === 0) {
        throw new MissingRequiredFieldError({ pointer: `${this.errorPointerPrefix}.materials` })
      }
    }
  }

  private validateTransportByIndaver (
    isTransportByIndaver: boolean | null,
    isSubmit: boolean
  ) {
    if (!this.validationUtil.isTransportByIndaverAllowed) {
      if (isTransportByIndaver !== null) {
        throw new FieldMustBeNullError({ pointer: `${this.errorPointerPrefix}.isTransportByIndaver` })
      }
    }

    if (isSubmit && this.validationUtil.isTransportByIndaverRequired) {
      if (isTransportByIndaver === null) {
        throw new MissingRequiredFieldError({ pointer: `${this.errorPointerPrefix}.isTransportByIndaver` })
      }
    }
  }

  private validateTransportMode (
    transportMode: PickUpTransportMode | null,
    isSubmit: boolean
  ) {
    if (!this.validationUtil.isTransportModeAllowed) {
      if (transportMode !== null) {
        throw new FieldMustBeNullError({ pointer: `${this.errorPointerPrefix}.transportMode` })
      }
    }
    if (isSubmit && this.validationUtil.isTransportModeRequired) {
      if (transportMode === null) {
        throw new MissingRequiredFieldError({ pointer: `${this.errorPointerPrefix}.transportMode` })
      }
    }
  }

  private async validateMaterialAccess (
    options: ValidateMaterialsOptions
  ): Promise<void> {
    const { materials, customerId, pickUpAddressIds } = options

    if (materials == null || materials.length === 0) {
      return
    }

    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    let restrictedWasteProducerIds: string[] | undefined

    if (selectedCustomerId != null) {
      restrictedWasteProducerIds = await this.userWasteProducerAuthService
        .getRestrictedWasteProducerIds(
          this.authContext.getAzureEntraUpnOrFail(),
          selectedCustomerId
        )
    }

    await this.contractLineValidatorService.checkAccessContractLines(
      'pick-up',
      materials.map(material => ({
        contractNumber: material.contractNumber,
        contractItem: material.contractItem,
        tcNumber: material.tcNumber
      })),
      {
        customerId: customerId ?? selectedCustomerId ?? undefined,
        wasteProducerIds: restrictedWasteProducerIds
      },
      {
        pickUpAddressIds: pickUpAddressIds
      }
    )
  }
}
