import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { UpdatePickUpRequestValidator } from '../update-pick-up-request.validator.js'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { PickUpRequestEntityBuilder } from '../../tests/pick-up-request-entity.builder.js'
import { PickUpRequestGeneralInfoValidator } from '../pick-up-request-general-info.validator.js'
import { UpdatePickUpRequestCommand } from '../../use-cases/update-pick-up-request/update-pick-up-request.command.js'
import { PickUpRequestAlreadySubmitted } from '../../errors/pick-up-request-already-submitted.error.js'
import { PickUpRequestWasteDetailValidator } from '../pick-up-request-waste-detail.validator.js'
import { PickUpRequestTransportValidator } from '../pick-up-request-transport.validator.js'
import { PickUpRequestPlanningValidator } from '../pick-up-request-planning.validator.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ValidPickUpRequestEntityBuilder } from '../../tests/valid-pick-up-request-entity.builder.js'
import { ContractLineIdMismatchError } from '../../errors/contract-line-id-mismatch.error.js'
import { ExactlyOneContractLineExpectedError } from '../../errors/exactly-one-contract-line-expected.error.js'
import { PickUpRequestPackagingValidator } from '../pick-up-request-packaging.validator.js'
import { UpdatePickUpRequestMaterialCommandBuilder } from '../../use-cases/update-pick-up-request/tests/update-pick-up-request-material-command.builder.js'
import { UpdatePickUpRequestCommandBuilder } from '../../use-cases/update-pick-up-request/tests/update-pick-up-request-command.builder.js'

describe('Update pick-up request validator unit test', () => {
  let validator: UpdatePickUpRequestValidator

  let userUuid: string

  let pickUpRequest: PickUpRequest
  let command: UpdatePickUpRequestCommand

  let authContext: SinonStubbedInstance<AuthContext>
  let pickUpRequestRepository: SinonStubbedInstance<Repository<PickUpRequest>>
  let pickUpRequestGeneralInfoValidator: SinonStubbedInstance<PickUpRequestGeneralInfoValidator>
  let pickUpRequestWasteDetailValidator: SinonStubbedInstance<PickUpRequestWasteDetailValidator>
  let pickUpRequestTransportValidator: SinonStubbedInstance<PickUpRequestTransportValidator>
  let pickUpRequestPlanningValidator: SinonStubbedInstance<PickUpRequestPlanningValidator>
  let pickUpRequestPackagingValidator: SinonStubbedInstance<PickUpRequestPackagingValidator>

  before(() => {
    TestBench.setupUnitTest()

    pickUpRequest = new PickUpRequestEntityBuilder()
      .withCustomerId(randomUUID())
      .build()

    command = new UpdatePickUpRequestCommandBuilder()
      .build()

    userUuid = randomUUID()
    authContext = createStubInstance(AuthContext)

    pickUpRequestRepository = createStubInstance<Repository<PickUpRequest>>(
      Repository<PickUpRequest>
    )

    pickUpRequestGeneralInfoValidator = createStubInstance(PickUpRequestGeneralInfoValidator)
    pickUpRequestWasteDetailValidator = createStubInstance(PickUpRequestWasteDetailValidator)
    pickUpRequestTransportValidator = createStubInstance(PickUpRequestTransportValidator)
    pickUpRequestPlanningValidator = createStubInstance(PickUpRequestPlanningValidator)
    pickUpRequestPackagingValidator = createStubInstance(PickUpRequestPackagingValidator)

    validator = new UpdatePickUpRequestValidator(
      authContext,
      pickUpRequestRepository,
      pickUpRequestGeneralInfoValidator,
      pickUpRequestWasteDetailValidator,
      pickUpRequestTransportValidator,
      pickUpRequestPlanningValidator,
      pickUpRequestPackagingValidator
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    Sinon.resetHistory()

    authContext.getUserUuidOrFail.returns(userUuid)
    pickUpRequestRepository.findOneByOrFail.resolves(pickUpRequest)
    pickUpRequestGeneralInfoValidator.validateUpdate.resolves()
  }

  it('Should throw error when pick-up request already submitted', async () => {
    const submittedPickUpRequest = new PickUpRequest()

    Object.assign(submittedPickUpRequest, pickUpRequest, {
      submittedOn: new Date()
    })

    pickUpRequestRepository.findOneByOrFail.resolves(submittedPickUpRequest)

    await expect(validator.validate(submittedPickUpRequest, command))
      .rejects.toThrow(PickUpRequestAlreadySubmitted)
  })

  it(`Should throw error when wpr pick-up request are not exactly 1 do not match`, async () => {
    const command = new UpdatePickUpRequestCommandBuilder()
      .addMaterial(
        new UpdatePickUpRequestMaterialCommandBuilder()
          .build()
      )
      .addMaterial(
        new UpdatePickUpRequestMaterialCommandBuilder()
          .build()
      )
      .build()

    const pickUpRequest = new ValidPickUpRequestEntityBuilder()
      .withWeeklyPlanningRequestUuid(randomUUID())
      .build()

    pickUpRequestRepository.findOneByOrFail.resolves(pickUpRequest)

    await expect(validator.validate(pickUpRequest, command))
      .rejects.toThrow(ExactlyOneContractLineExpectedError)
  })

  it(`Should throw error when wpr pick-up request contract line id's do not match`, async () => {
    const command = new UpdatePickUpRequestCommandBuilder()
      .addMaterial(
        new UpdatePickUpRequestMaterialCommandBuilder()
          .build()
      )
      .build()

    const pickUpRequest = new ValidPickUpRequestEntityBuilder()
      .withWeeklyPlanningRequestUuid(randomUUID())
      .build()

    pickUpRequestRepository.findOneByOrFail.resolves(pickUpRequest)

    await expect(validator.validate(pickUpRequest, command))
      .rejects.toThrow(ContractLineIdMismatchError)
  })

  it('Should call every validation step once', async () => {
    await validator.validate(pickUpRequest, command)

    assert.calledOnce(pickUpRequestGeneralInfoValidator.validateUpdate)
    assert.calledOnce(pickUpRequestWasteDetailValidator.validateUpdate)
    assert.calledOnce(pickUpRequestTransportValidator.validateUpdate)
    assert.calledOnce(pickUpRequestPlanningValidator.validateUpdate)
    assert.calledOnce(pickUpRequestPackagingValidator.validateUpdate)
  })
})
