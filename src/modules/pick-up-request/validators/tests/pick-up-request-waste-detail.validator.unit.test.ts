import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance, assert } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { ContractLineValidatorService } from '../../../contract-line/validators/contract-line-validator.service.js'
import { PickUpRequestWasteDetailValidator } from '../pick-up-request-waste-detail.validator.js'
import { ValidPickUpRequestEntityBuilder } from '../../tests/valid-pick-up-request-entity.builder.js'
import { MissingRequiredFieldError } from '../../../exceptions/generic/missing-required-field.error.js'
import { TooManyContractLines } from '../../errors/too-many-contract-lines.error.js'
import { PickUpTransportMode } from '../../enums/pick-up-transport-mode.enum.js'
import { ValidPickUpRequestMaterialBuilder } from '../../tests/valid-pick-up-request-material.builder.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'

describe('Pick-up request waste detail validator unit test', () => {
  let validator: PickUpRequestWasteDetailValidator

  let pickUpRequestBuilder: ValidPickUpRequestEntityBuilder
  let materialBuilder: ValidPickUpRequestMaterialBuilder

  let authContext: SinonStubbedInstance<AuthContext>
  let contractLineValidatorService: SinonStubbedInstance<ContractLineValidatorService>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>

  before(() => {
    TestBench.setupUnitTest()

    pickUpRequestBuilder = new ValidPickUpRequestEntityBuilder()
    materialBuilder = new ValidPickUpRequestMaterialBuilder()

    authContext = createStubInstance(AuthContext)
    contractLineValidatorService = createStubInstance(ContractLineValidatorService)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)

    validator = new PickUpRequestWasteDetailValidator(
      authContext,
      contractLineValidatorService,
      userWasteProducerAuthService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getSelectedCustomerId.returns(null)
    authContext.getAzureEntraUpnOrFail.returns(randomUUID())
    contractLineValidatorService.checkAccessContractLines.resolves()
    userWasteProducerAuthService.getRestrictedWasteProducerIds.resolves(undefined)
  }

  describe('Update validation', () => {
  })

  describe('Submit validation', () => {
    it('should call contractLineValidatorService.checkUserAccessContractLines when contract lines selected', async () => {
      const pickUpRequest = pickUpRequestBuilder
        .addMaterial(
          new ValidPickUpRequestMaterialBuilder()
            .withPackageCurtainSiderTruckProperties()
            .build(),
          true
        )
        .build()

      await validator.validateSubmit(pickUpRequest)

      assert.calledOnce(contractLineValidatorService.checkAccessContractLines)
    })

    it('throws an error when transport mode is null', async () => {
      const pickUpRequest = pickUpRequestBuilder.build()

      pickUpRequest.transportMode = null

      await expect(validator.validateSubmit(pickUpRequest))
        .rejects.toThrow(MissingRequiredFieldError)
    })

    it('throws an error when materials is empty', async () => {
      const pickUpRequest = pickUpRequestBuilder.build()

      pickUpRequest.materials = []

      await expect(validator.validateSubmit(pickUpRequest))
        .rejects.toThrow(MissingRequiredFieldError)
    })

    it('throws an error when too many materials are given', async () => {
      const pickUpRequest = pickUpRequestBuilder
        .withTransportMode(PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS)
        .addMaterial(
          materialBuilder.withBulkVacuumTankersRoadTankers().build(),
          true
        )
        .addMaterial(
          materialBuilder.withBulkVacuumTankersRoadTankers().build()
        )
        .build()

      await expect(validator.validateSubmit(pickUpRequest))
        .rejects.toThrow(TooManyContractLines)
    })
  })
})
