import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { PickUpRequest } from '../../entities/pick-up-request.entity.js'
import { PickUpRequestEntityBuilder } from '../../tests/pick-up-request-entity.builder.js'
import { UpdatePickUpRequestCommandBuilder } from '../../use-cases/update-pick-up-request/tests/update-pick-up-request-command.builder.js'
import { UpdatePickUpRequestCommand } from '../../use-cases/update-pick-up-request/update-pick-up-request.command.js'
import { PickUpRequestGeneralInfoValidator } from '../pick-up-request-general-info.validator.js'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { CustomerPickUpAddressAuthService } from '../../../auth/services/customer-pick-up-address-auth.service.js'
import { CustomerWasteProducerAuthService } from '../../../auth/services/customer-waste-producer-auth.service.js'
import { UserCustomerAuthService } from '../../../auth/services/user-customer-auth.service.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { PickUpAddressNotAccessibleError } from '../../../pick-up-address/errors/pick-up-address-not-accessible.error.js'
import { WasteProducerNotAccessibleError } from '../../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { FieldMustBeNullError } from '../../../exceptions/generic/field-must-be-null.error.js'
import { UpdatePickUpRequestMapper } from '../../use-cases/update-pick-up-request/mappers/update-pick-up-request.mapper.js'
import { ValidPickUpRequestEntityBuilder } from '../../tests/valid-pick-up-request-entity.builder.js'

describe('Pick-up request general info unit test', () => {
  let validator: PickUpRequestGeneralInfoValidator

  let userUuid: string
  let pickUpRequest: PickUpRequest
  let mergedPickUpRequest: PickUpRequest
  let command: UpdatePickUpRequestCommand

  let pickUpRequestBuilder: PickUpRequestEntityBuilder
  let validPickUpRequestBuilder: ValidPickUpRequestEntityBuilder

  let authContext: SinonStubbedInstance<AuthContext>
  let userCustomerAuthService: SinonStubbedInstance<UserCustomerAuthService>
  let customerWasteProducerAuthService: SinonStubbedInstance<CustomerWasteProducerAuthService>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>
  let customerPickUpAddressAuthService: SinonStubbedInstance<CustomerPickUpAddressAuthService>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()

    pickUpRequestBuilder = new PickUpRequestEntityBuilder()
    validPickUpRequestBuilder = new ValidPickUpRequestEntityBuilder()

    pickUpRequest = pickUpRequestBuilder
      .createdByUserUuid(userUuid)
      .build()

    command = new UpdatePickUpRequestCommandBuilder()
      .withCustomerId(randomUUID())
      .withPickUpAddressIds([randomUUID()])
      .withWasteProducerId(randomUUID())
      .build()

    mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
      command,
      pickUpRequest
    )

    authContext = createStubInstance(AuthContext)
    userCustomerAuthService = createStubInstance(UserCustomerAuthService)
    customerWasteProducerAuthService = createStubInstance(CustomerWasteProducerAuthService)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)
    customerPickUpAddressAuthService = createStubInstance(CustomerPickUpAddressAuthService)

    validator = new PickUpRequestGeneralInfoValidator(
      authContext,
      userCustomerAuthService,
      customerWasteProducerAuthService,
      userWasteProducerAuthService,
      customerPickUpAddressAuthService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    userCustomerAuthService.canUserAccessCustomer.resolves(true)
    userWasteProducerAuthService.canUserAccessWasteProducer.resolves(true)
    customerWasteProducerAuthService.canCustomerAccessWasteProducer.resolves(true)
    customerPickUpAddressAuthService.canCustomerAccessPickUpAddresses.resolves(true)
  }

  describe('Update validation', () => {
    describe('Customer', () => {
      it(`throws an error when customerId is given for weekly planning request's pick-up request`, async () => {
        const pickUpRequest = pickUpRequestBuilder
          .withCustomerId(randomUUID())
          .withWeeklyPlanningRequestUuid(randomUUID())
          .build()

        const mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
          command,
          pickUpRequest
        )

        await expect(validator.validateUpdate(command, mergedPickUpRequest))
          .rejects.toThrow(FieldMustBeNullError)
      })
    })

    describe('Waste producer', () => {
      it('throws an error when waste producer is not accessible by customer', async () => {
        customerWasteProducerAuthService.canCustomerAccessWasteProducer.resolves(false)

        await expect(validator.validateUpdate(command, mergedPickUpRequest))
          .rejects.toThrow(WasteProducerNotAccessibleError)
      })

      it('throws an error when waste producer is not accessible by auth user', async () => {
        userWasteProducerAuthService.canUserAccessWasteProducer.resolves(false)

        await expect(validator.validateUpdate(command, mergedPickUpRequest))
          .rejects.toThrow(WasteProducerNotAccessibleError)
      })

      it(`throws an error when wasteProducerId is given for weekly planning request's pick-up request`, async () => {
        const pickUpRequest = pickUpRequestBuilder
          .withWasteProducerId(randomUUID())
          .withWeeklyPlanningRequestUuid(randomUUID())
          .build()

        const mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
          command,
          pickUpRequest
        )

        await expect(validator.validateUpdate(command, mergedPickUpRequest))
          .rejects.toThrow(FieldMustBeNullError)
      })
    })

    describe('Pick up addresses', () => {
      it('throws an error when pickUpAddresses not accessible by customer', async () => {
        customerPickUpAddressAuthService.canCustomerAccessPickUpAddresses.resolves(false)

        await expect(validator.validateUpdate(command, mergedPickUpRequest))
          .rejects.toThrow(PickUpAddressNotAccessibleError)
      })

      it(`throws an error when pickUpAddressId is given for weekly planning request's pick-up request`, async () => {
        const pickUpRequest = pickUpRequestBuilder
          .addPickUpAddressId(randomUUID())
          .withWeeklyPlanningRequestUuid(randomUUID())
          .build()

        const mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
          command,
          pickUpRequest
        )

        await expect(validator.validateUpdate(command, mergedPickUpRequest))
          .rejects.toThrow(FieldMustBeNullError)
      })
    })
  })

  describe('Submit validation', () => {
    describe('Waste producer', () => {
      it('throws an error when waste producer is not accessible by customer', async () => {
        customerWasteProducerAuthService.canCustomerAccessWasteProducer.resolves(false)

        const pickUpRequest = validPickUpRequestBuilder
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(WasteProducerNotAccessibleError)
      })

      it('throws an error when waste producer is not accessible by auth user', async () => {
        userWasteProducerAuthService.canUserAccessWasteProducer.resolves(false)

        const pickUpRequest = validPickUpRequestBuilder
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(WasteProducerNotAccessibleError)
      })
    })

    describe('Pick up addresses', () => {
      it('throws an error when pickUpAddresses not accessible by customer', async () => {
        customerPickUpAddressAuthService.canCustomerAccessPickUpAddresses.resolves(false)

        const pickUpRequest = validPickUpRequestBuilder
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .rejects.toThrow(PickUpAddressNotAccessibleError)
      })
    })

    describe(`Weekly planning request's pick-up request`, () => {
      it(`doesn't throw an error when wpr pick-up request is submitted`, async () => {
        const pickUpRequest = validPickUpRequestBuilder
          .withWeeklyPlanningRequestUuid(randomUUID())
          .build()

        await expect(validator.validateSubmit(pickUpRequest))
          .resolves.not.toThrow()
      })
    })
  })
})
