import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { Injectable } from '@nestjs/common'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { PickUpRequestAlreadySubmitted } from '../errors/pick-up-request-already-submitted.error.js'
import { AuthContext } from '../../auth/auth.context.js'
import { ExactlyOneContractLineExpectedError } from '../errors/exactly-one-contract-line-expected.error.js'
import { ContractLineIdMismatchError } from '../errors/contract-line-id-mismatch.error.js'
import { UpdatePickUpRequestCommand } from '../use-cases/update-pick-up-request/update-pick-up-request.command.js'
import { UpdatePickUpRequestMapper } from '../use-cases/update-pick-up-request/mappers/update-pick-up-request.mapper.js'
import { PickUpRequestGeneralInfoValidator } from './pick-up-request-general-info.validator.js'
import { PickUpRequestWasteDetailValidator } from './pick-up-request-waste-detail.validator.js'
import { PickUpRequestTransportValidator } from './pick-up-request-transport.validator.js'
import { PickUpRequestPlanningValidator } from './pick-up-request-planning.validator.js'
import { PickUpRequestPackagingValidator } from './pick-up-request-packaging.validator.js'

@Injectable()
export class UpdatePickUpRequestValidator {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    private readonly pickUpRequestGeneralInfoValidator: PickUpRequestGeneralInfoValidator,
    private readonly pickUpRequestWasteDetailValidator: PickUpRequestWasteDetailValidator,
    private readonly pickUpRequestTransportValidator: PickUpRequestTransportValidator,
    private readonly pickUpRequestPlanningValidator: PickUpRequestPlanningValidator,
    private readonly pickUpRequestPackagingValidator: PickUpRequestPackagingValidator
  ) {}

  private command: UpdatePickUpRequestCommand
  private pickUpRequest: PickUpRequest

  async validate (
    pickUpRequest: PickUpRequest,
    command: UpdatePickUpRequestCommand
  ): Promise<void> {
    this.command = command
    this.pickUpRequest = pickUpRequest

    if (this.pickUpRequest.submittedOn !== null) {
      throw new PickUpRequestAlreadySubmitted()
    }

    const mergedPickUpRequest = UpdatePickUpRequestMapper.mapToMergedPickUpRequest(
      this.command,
      this.pickUpRequest
    )

    this.validateContractLines()

    await this.pickUpRequestGeneralInfoValidator.validateUpdate(
      this.command,
      mergedPickUpRequest
    )

    this.pickUpRequestWasteDetailValidator.validateUpdate(
      this.command,
      mergedPickUpRequest
    )

    await this.pickUpRequestTransportValidator.validateUpdate (
      mergedPickUpRequest
    )

    await this.pickUpRequestPlanningValidator.validateUpdate(
      this.command,
      mergedPickUpRequest
    )

    this.pickUpRequestPackagingValidator.validateUpdate(
      this.command,
      mergedPickUpRequest
    )
  }

  private validateContractLines () {
    if (this.pickUpRequest.isWprPickUpRequest && this.command.materials !== undefined) {
      if (this.command.materials.length !== 1 || this.pickUpRequest.materials.length !== 1) {
        throw new ExactlyOneContractLineExpectedError()
      }

      const pickUpRequestContractLineId = this.pickUpRequest.materials.at(0)?.contractLineId
      const commandContractLineId = this.command.materials.at(0)?.contractLineId

      if (pickUpRequestContractLineId !== commandContractLineId) {
        throw new ContractLineIdMismatchError({ pointer: '$.materials.$.contractLineId' })
      }
    }
  }
}
