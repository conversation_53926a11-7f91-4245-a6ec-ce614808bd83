import { Injectable } from '@nestjs/common'
import { PgBossScheduler } from '@wisemen/pgboss-nestjs-job'
import { Subscribe } from '../../domain-events/subscribe.decorator.js'
import { SyncTypesenseJob } from '../../typesense/use-cases/sync-collection/sync-typesense-collection.job.js'
import { TypesenseCollectionName } from '../../typesense/collections/typesense-collection-name.enum.js'
import { PickUpRequestTemplateCreatedEvent } from '../use-cases/create-pick-up-request-template/pick-up-request-template-created.event.js'
import { PickUpRequestTemplateUpdatedEvent } from '../use-cases/update-pick-up-request-template/pick-up-request-template-updated.event.js'
import { PickUpRequestTemplateDeletedEvent, PickUpRequestTemplateDeletedEventContent } from '../use-cases/bulk-delete-pick-up-request-templates/pick-up-request-template-deleted.event.js'
import { RemoveFromTypesenseJob } from '../../typesense/jobs/remove-from-typesense/remove-from-typesense.job.js'

@Injectable()
export class PickUpRequestTemplateTypesenseSubscriber {
  constructor (
    private readonly jobScheduler: PgBossScheduler
  ) {}

  @Subscribe(PickUpRequestTemplateCreatedEvent)
  @Subscribe(PickUpRequestTemplateUpdatedEvent)
  async handle (): Promise<void> {
    const job = new SyncTypesenseJob(TypesenseCollectionName.PICK_UP_REQUEST_TEMPLATE)
    await this.jobScheduler.scheduleJob(job)
  }

  @Subscribe(PickUpRequestTemplateDeletedEvent)
  async handleDelete (event: PickUpRequestTemplateDeletedEventContent): Promise<void> {
    const job = new RemoveFromTypesenseJob({
      collection: TypesenseCollectionName.PICK_UP_REQUEST_TEMPLATE,
      uuid: event.pickUpRequestUuid
    })

    await this.jobScheduler.scheduleJob(job)
  }
}
