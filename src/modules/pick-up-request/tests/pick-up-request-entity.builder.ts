import { randomUUID } from 'crypto'
import { PickUpRequest } from '../entities/pick-up-request.entity.js'
import { PickUpTransportMode } from '../enums/pick-up-transport-mode.enum.js'
import { PickUpRequestMaterial } from '../types/pick-up-request-material.type.js'
import { PackagingRequestMaterial } from '../types/packaging-request-material.type.js'
import { Contact } from '../../contact/types/contact.type.js'
import { Customer } from '../../customer/types/customer.type.js'
import { WasteProducer } from '../../waste-producer/types/waste-producer.type.js'
import { PickUpAddress } from '../../pick-up-address/types/pick-up-address.type.js'

export class PickUpRequestEntityBuilder {
  protected pickUpRequest: PickUpRequest

  constructor () {
    this.reset()
  }

  reset (): this {
    this.pickUpRequest = new PickUpRequest()

    this.pickUpRequest.uuid = randomUUID()
    this.pickUpRequest.createdAt = new Date()
    this.pickUpRequest.updatedAt = new Date()
    this.pickUpRequest.customerId = null
    this.pickUpRequest.wasteProducerId = null
    this.pickUpRequest.deliveryAddressId = null
    this.pickUpRequest.pickUpAddressIds = []
    this.pickUpRequest.materials = []
    this.pickUpRequest.submittedOn = null
    this.pickUpRequest.transportMode = null
    this.pickUpRequest.sendCopyToContacts = []
    this.pickUpRequest.weeklyPlanningRequestUuid = null
    this.pickUpRequest.materialsWithContractLine = []
    this.pickUpRequest.packagingRequestMaterials = []
    this.pickUpRequest.isOnlyPackagingRequest = false
    this.pickUpRequest.additionalFiles = []
    this.pickUpRequest.templateUpdatedByUserUuid = null

    return this
  }

  withUuid (uuid: string): this {
    this.pickUpRequest.uuid = uuid

    return this
  }

  withTransportMode (transportMode: PickUpTransportMode | null): this {
    this.pickUpRequest.transportMode = transportMode

    return this
  }

  withCustomerId (customerId: string | null): this {
    this.pickUpRequest.customerId = customerId

    return this
  }

  withCustomer (customer: Customer): this {
    this.pickUpRequest.customerId = customer.id
    this.pickUpRequest.customer = customer

    return this
  }

  withWasteProducerId (wasteProducerId: string | null): this {
    this.pickUpRequest.wasteProducerId = wasteProducerId

    return this
  }

  withWasteProducer (wasteProducer: WasteProducer): this {
    this.pickUpRequest.wasteProducerId = wasteProducer.id
    this.pickUpRequest.wasteProducer = wasteProducer

    return this
  }

  addPickUpAddressId (pickUpAddressId: string): this {
    this.pickUpRequest.pickUpAddressIds.push(pickUpAddressId)

    return this
  }

  withPickUpAddressId (ids: string[]): this {
    this.pickUpRequest.pickUpAddressIds = ids

    return this
  }

  withPickUpAddresses (pickUpAddresses: PickUpAddress[]): this {
    const pickUpAddressIds = pickUpAddresses.map(
      pickUpAddress => pickUpAddress.id
    )

    this.pickUpRequest.pickUpAddressIds = pickUpAddressIds
    this.pickUpRequest.pickUpAddresses = pickUpAddresses

    return this
  }

  createdByUserUuid (userUuid: string): this {
    this.pickUpRequest.createdByUserUuid = userUuid

    return this
  }

  addMaterial (material: PickUpRequestMaterial): this {
    this.pickUpRequest.materials.push(material)

    return this
  }

  withTotalQuantityPallets (totalQuantityPallets: number | null): this {
    this.pickUpRequest.totalQuantityPallets = totalQuantityPallets

    return this
  }

  withIsReturnPackaging (isReturnPackaging: boolean | null): this {
    this.pickUpRequest.isReturnPackaging = isReturnPackaging

    return this
  }

  withPackagingRemark (packagingRemark: string | null): this {
    this.pickUpRequest.packagingRemark = packagingRemark

    return this
  }

  withWeeklyPlanningRequestUuid (uuid: string): this {
    this.pickUpRequest.weeklyPlanningRequestUuid = uuid

    return this
  }

  withIsTransportByIndaver (isTransportByIndaver: boolean | null): this {
    this.pickUpRequest.isTransportByIndaver = isTransportByIndaver

    return this
  }

  withStartDate (startDate: string | null): this {
    this.pickUpRequest.startDate = startDate

    return this
  }

  withEndDate (endDate: string | null): this {
    this.pickUpRequest.endDate = endDate

    return this
  }

  withDeliveryAddressId (deliveryAddressId: string | null): this {
    this.pickUpRequest.deliveryAddressId = deliveryAddressId

    return this
  }

  withMaterials (materials: PickUpRequestMaterial[]): this {
    this.pickUpRequest.materials = materials

    return this
  }

  withPackagingRequestMaterials (materials: PackagingRequestMaterial[]): this {
    this.pickUpRequest.packagingRequestMaterials = materials

    return this
  }

  withIsOnlyPackagingRequest (isOnlyPackagingRequest: boolean): this {
    this.pickUpRequest.isOnlyPackagingRequest = isOnlyPackagingRequest

    return this
  }

  withRemarks (remarks: string | null): this {
    this.pickUpRequest.remarks = remarks

    return this
  }

  withSendCopyToContracts (contacts: Contact[]): this {
    this.pickUpRequest.sendCopyToContacts = contacts

    return this
  }

  withSubmittedOn (submittedOn: Date | null): this {
    this.pickUpRequest.submittedOn = submittedOn

    return this
  }

  withRequestNumber (requestNumber: string | null): this {
    this.pickUpRequest.requestNumber = requestNumber

    return this
  }

  withStartTime (startTime: string | null): this {
    this.pickUpRequest.startTime = startTime

    return this
  }

  withCreatedByUserUuid (userUuid: string): this {
    this.pickUpRequest.createdByUserUuid = userUuid

    return this
  }

  witWeeklyPlanningRequestUuid (uuid: string | null): this {
    this.pickUpRequest.weeklyPlanningRequestUuid = uuid

    return this
  }

  withTemplateName (templateName: string | null): this {
    this.pickUpRequest.templateName = templateName

    return this
  }

  build (): PickUpRequest {
    const result = this.pickUpRequest

    this.reset()

    return result
  }
}
