import { Module } from '@nestjs/common'
import { DomainEventLogSubscriberModule } from '../domain-event-log/use-cases/log-event/domain-event-log-subscriber.module.js'
import { ClearRolePermissionsCacheSubscriberModule } from '../../app/roles/use-cases/clear-role-permissions-cache/clear-role-permissions-cache-subscriber.module.js'
import { UserTypesenseSubscriberModule } from '../../app/users/typesense/user-typesense.subscriber.module.js'
import { CreateUserNotificationsSubscriberModule } from '../notification/use-cases/create-user-notifications/create-user-notifications.subscriber-module.js'
import { SendAppNotificationSubscriberModule } from '../notification/use-cases/send-app-notification/send-app-notification.subscriber.module.js'
import { AssignDefaultNotificationPreferencesToUserSubscriberModule } from '../notification/use-cases/assign-default-notification-preferences-to-user/assign-default-notification-preferences-to-user.subscriber.module.js'
import { ContactTypesenseSubscriberModule } from '../contact/typesense/contact-typesense.subscriber.module.js'
import { PickUpRequestTemplateTypesenseSubscriberModule } from '../pick-up-request/typesense/pick-up-request-template-typesense.subscriber.module.js'
import { CreateContactsFromFormSubmissionSubscriberModule } from '../contact/use-cases/create-contacts-from-form-submission/create-contacts-from-form-submission.subscriber.module.js'

@Module({
  imports: [
    DomainEventLogSubscriberModule,
    ClearRolePermissionsCacheSubscriberModule,
    CreateContactsFromFormSubmissionSubscriberModule,
    UserTypesenseSubscriberModule,
    CreateUserNotificationsSubscriberModule,
    SendAppNotificationSubscriberModule,
    ContactTypesenseSubscriberModule,
    AssignDefaultNotificationPreferencesToUserSubscriberModule,
    PickUpRequestTemplateTypesenseSubscriberModule
  ]
})
export class DomainEventSubscribersModule {}
