export enum DomainEventType {
  ANNOUNCEMENT_CREATED = 'announcement.created',
  ANNOUNCEMENT_DELETED = 'announcement.deleted',
  ANNOUNCEMENT_UPDATED = 'announcement.updated',

  CONTACT_CREATED = 'contact.created',
  CONTACT_DELETED = 'contact.deleted',
  CONTACT_UPDATED = 'contact.updated',

  DYNAMIC_TABLE_VIEW_CREATED = 'dynamic-table-view.created',
  DYNAMIC_TABLE_VIEW_DELETED = 'dynamic-table-view.deleted',
  DYNAMIC_TABLE_VIEW_UPDATED = 'dynamic-table-view.updated',

  FILE_CREATED = 'file.created',
  FILE_DELETED = 'file.deleted',
  FILE_UPLOADED = 'file.uploaded',

  NEWS_ITEM_CREATED = 'news-item.created',
  NEWS_ITEM_DELETED = 'news-item.deleted',
  NEWS_ITEM_UPDATED = 'news-item.updated',

  NOTIFICATION_CREATED = 'notification.created',
  NOTIFICATION_PREFERENCE_PRESET_UPDATED = 'notification.preference.preset.updated',
  NOTIFICATION_READ = 'notification.read',
  NOTIFICATION_READ_ALL = 'notification.read.all',
  NOTIFICATION_TYPES_MIGRATED = 'notification.types.migrated',
  NOTIFICATION_UNREAD = 'notification.unread',

  PACKAGING_REQUEST_COPIED = 'packaging-request.copied',
  PACKAGING_REQUEST_CREATED = 'packaging-request.created',
  PACKAGING_REQUEST_DELETED = 'packaging-request.deleted',
  PACKAGING_REQUEST_SUBMITTED = 'packaging-request.submitted',
  PACKAGING_REQUEST_UPDATED = 'packaging-request.updated',

  PICK_UP_REQUEST_COPIED = 'pick-up-request.copied',
  PICK_UP_REQUEST_CREATED = 'pick-up-request.created',
  PICK_UP_REQUEST_DELETED = 'pick-up-request.deleted',
  PICK_UP_REQUEST_SUBMITTED = 'pick-up-request.submitted',
  PICK_UP_REQUEST_UPDATED = 'pick-up-request.updated',

  PICK_UP_REQUEST_TEMPLATE_CREATED = 'pick-up-request-template.created',
  PICK_UP_REQUEST_TEMPLATE_DELETED = 'pick-up-request-template.deleted',
  PICK_UP_REQUEST_TEMPLATE_UPDATED = 'pick-up-request-template.updated',

  ROLE_CREATED = 'role.created',
  ROLE_DELETED = 'role.deleted',
  ROLE_PERMISSIONS_CACHE_CLEARED = 'role.permissions.cache.cleared',
  ROLE_PERMISSIONS_UPDATED = 'role.permissions.updated',
  ROLE_RENAMED = 'role.renamed',

  TEST_NOTIFICATION_SENT = 'test-notification.sent',

  USER_CREATED = 'user.created',
  USER_DEFAULT_NOTIFICATION_PREFERENCES_ASSIGNED = 'user.default-notification-preferences.assigned',
  USER_NOTIFICATION_CREATED = 'user.notification.created',
  USER_ROLE_ASSIGNED = 'user.role-assigned',

  WASTE_INQUIRY_COPIED = 'waste-inquiry.copied',
  WASTE_INQUIRY_CREATED = 'waste-inquiry.created',
  WASTE_INQUIRY_DELETED = 'waste-inquiry.deleted',
  WASTE_INQUIRY_SUBMITTED = 'waste-inquiry.submitted',
  WASTE_INQUIRY_UPDATED = 'waste-inquiry.updated',

  WEEKLY_PLANNING_REQUEST_CREATED = 'weekly-planning-request.created',
  WEEKLY_PLANNING_REQUEST_SUBMITTED = 'weekly-planning-request.submitted',
  WEEKLY_PLANNING_REQUEST_UPDATED = 'weekly-planning-request.updated'
}
