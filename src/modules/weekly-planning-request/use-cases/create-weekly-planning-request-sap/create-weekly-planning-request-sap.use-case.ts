import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { WeeklyPlanningRequest } from '../../entities/weekly-planning-request.entity.js'
import { SapCreateWeeklyPlanningRequestUseCase } from '../../../sap/use-cases/create-weekly-planning-request/create-weekly-planning.use-case.js'
import { FileLinkService } from '../../../files/services/file-link.service.js'
import { EntityPart } from '../../../files/enums/entity-part.enum.js'
import { SubmitPickUpRequestService } from '../../../pick-up-request/services/submit-pick-up-request.service.js'

@Injectable()
export class CreateWeeklyPlanningRequestSapUseCase {
  constructor (
    @InjectRepository(WeeklyPlanningRequest)
    private readonly weeklyPlanningRequest: Repository<WeeklyPlanningRequest>,
    private readonly fileLinkService: FileLinkService,
    private readonly sapCreateWeeklyPlanningRequest: SapCreateWeeklyPlanningRequestUseCase,
    private readonly submitPickUpRequestService: SubmitPickUpRequestService
  ) {}

  async execute (weeklyPlanningRequestUuid: string): Promise<[string, string[]]> {
    const weeklyPlanningRequest = await this.weeklyPlanningRequest.findOneOrFail({
      where: {
        uuid: weeklyPlanningRequestUuid
      },
      relations: {
        createdByUser: true,
        pickUpRequests: {
          createdByUser: true
        }
      }
    })

    assert(weeklyPlanningRequest.pickUpRequests !== undefined)

    await this.loadFiles(weeklyPlanningRequest)
    this.loadPickUpRequestWithWprData(weeklyPlanningRequest)

    const wprSapResponse = await this.sapCreateWeeklyPlanningRequest.execute()
    const wprInquiryNumber = wprSapResponse.d.getWeekPLID.WeekPLID

    const pickUpRequestInquiryNumbers = await Promise.all(
      weeklyPlanningRequest.pickUpRequests.map(async (pickUpRequest) => {
        return await this.submitPickUpRequestService.execute(
          pickUpRequest,
          wprInquiryNumber
        )
      })
    )

    return [wprInquiryNumber, pickUpRequestInquiryNumbers]
  }

  private async loadFiles (weeklyPlanningRequest: WeeklyPlanningRequest): Promise<void> {
    weeklyPlanningRequest.additionalFiles = await this.fileLinkService.loadFileLinks(
      weeklyPlanningRequest.uuid,
      WeeklyPlanningRequest.name,
      EntityPart.ADDITIONAL
    )
  }

  private loadPickUpRequestWithWprData (
    weeklyPlannignRequest: WeeklyPlanningRequest
  ): void {
    weeklyPlannignRequest.pickUpRequests?.forEach((pickUpRequest) => {
      pickUpRequest.additionalFiles = weeklyPlannignRequest.additionalFiles
      pickUpRequest.sendCopyToContacts = weeklyPlannignRequest.sendCopyToContacts
      pickUpRequest.remarks = weeklyPlannignRequest.remarks
      pickUpRequest.createdByUserUuid = weeklyPlannignRequest.createdByUserUuid
      pickUpRequest.createdByUser = weeklyPlannignRequest.createdByUser
    })
  }
}
