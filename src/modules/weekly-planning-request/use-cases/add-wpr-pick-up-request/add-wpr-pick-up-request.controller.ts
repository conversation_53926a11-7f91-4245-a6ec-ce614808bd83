import { Body, Controller, Post } from '@nestjs/common'
import { ApiCreatedResponse, ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { AddWprPickUpRequestUseCase } from './add-wpr-pick-up-request.use-case.js'
import { AddWprPickUpRequestResponse } from './add-wpr-pick-up-request.response.js'
import { AddWprPickUpRequestCommand } from './add-wpr-pick-up-request.command.js'

@ApiTags('Weekly planning request')
@Controller('weekly-planning-requests/:uuid/add-pick-up-request')
@ApiOAuth2([])
export class AddWprPickUpRequestController {
  constructor (
    private readonly useCase: AddWprPickUpRequestUseCase
  ) {}

  @Post()
  @GlobalCustomerRequired()
  @Permissions(Permission.WEEKLY_PLANNING_REQUEST_MANAGE)
  @ApiCreatedResponse({ type: AddWprPickUpRequestResponse })
  async addWprPickUpRequest (
    @UuidParam('uuid') weeklyPlanningUuid: string,
    @Body() command: AddWprPickUpRequestCommand
  ): Promise<AddWprPickUpRequestResponse> {
    return await this.useCase.execute(
      weeklyPlanningUuid,
      command
    )
  }
}
