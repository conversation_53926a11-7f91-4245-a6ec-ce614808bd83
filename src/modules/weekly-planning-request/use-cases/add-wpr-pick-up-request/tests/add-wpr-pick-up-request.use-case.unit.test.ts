import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { WeeklyPlanningRequest } from '../../../entities/weekly-planning-request.entity.js'
import { WeeklyPlanningRequestEntityBuilder } from '../../../tests/weekly-planning-request-entity.builder.js'
import { AddWprPickUpRequestUseCase } from '../add-wpr-pick-up-request.use-case.js'
import { PickUpRequest } from '../../../../pick-up-request/entities/pick-up-request.entity.js'
import { AddWprPickUpRequestValidator } from '../add-wpr-pick-up-request.validator.js'
import { ValidPickUpRequestEntityBuilder } from '../../../../pick-up-request/tests/valid-pick-up-request-entity.builder.js'
import { WprPickUpRequestAddedCreatedEvent } from '../wpr-pick-up-added.event.js'
import { AddWprPickUpRequestCommandBuilder } from './add-wpr-pick-up-request-command.builder.js'

describe('Add pick-up request to weekly planning request use-case unit test', () => {
  let useCase: AddWprPickUpRequestUseCase

  let userUuid: string

  let pickUpRequest: PickUpRequest
  let weeklyPlanningRequest: WeeklyPlanningRequest

  let weeklyPlanningRequestRepository: SinonStubbedInstance<Repository<WeeklyPlanningRequest>>
  let pickUpRequestRepository: SinonStubbedInstance<Repository<PickUpRequest>>
  let validator: SinonStubbedInstance<AddWprPickUpRequestValidator>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()

    const authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: userUuid
    })

    validator = createStubInstance(AddWprPickUpRequestValidator)

    weeklyPlanningRequest = new WeeklyPlanningRequestEntityBuilder()
      .createdByUserUuid(userUuid)
      .build()

    weeklyPlanningRequestRepository = createStubInstance<Repository<WeeklyPlanningRequest>>(
      Repository<WeeklyPlanningRequest>, {
        findOneOrFail: Promise.resolve(
          weeklyPlanningRequest
        )
      }
    )

    pickUpRequest = new ValidPickUpRequestEntityBuilder()
      .createdByUserUuid(userUuid)
      .build()

    const pickUpRequestCopy = new PickUpRequest()

    Object.assign(pickUpRequestCopy, pickUpRequest, {
      submittedOn: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      startDate: null,
      endDate: null,
      remarks: null,
      sendCopyToContacts: [],
      weeklyPlanningRequestUuid: weeklyPlanningRequest.uuid
    })

    pickUpRequestRepository = createStubInstance<Repository<PickUpRequest>>(
      Repository<PickUpRequest>, {
        findOneByOrFail: Promise.resolve(
          pickUpRequest
        ),
        create: pickUpRequestCopy
      }
    )

    eventEmitter = createStubInstance(DomainEventEmitter)

    useCase = new AddWprPickUpRequestUseCase(
      stubDataSource(),
      authContext,
      pickUpRequestRepository,
      weeklyPlanningRequestRepository,
      validator,
      eventEmitter
    )
  })

  it('Calls all methods', async () => {
    const command = new AddWprPickUpRequestCommandBuilder()
      .withPickUpRequestUuid(pickUpRequest.uuid)
      .build()

    await useCase.execute(
      weeklyPlanningRequest.uuid,
      command
    )

    assert.calledOnce(weeklyPlanningRequestRepository.findOneOrFail)
    assert.calledOnce(pickUpRequestRepository.findOneByOrFail)
    assert.calledOnce(pickUpRequestRepository.create)
    assert.calledOnce(pickUpRequestRepository.insert)
    assert.calledOnce(validator.validate)
  })

  it('Emits a WprPickUpRequestAddedCreatedEvent event', async () => {
    const command = new AddWprPickUpRequestCommandBuilder()
      .withPickUpRequestUuid(pickUpRequest.uuid)
      .build()

    await useCase.execute(
      weeklyPlanningRequest.uuid,
      command
    )

    expect(eventEmitter).toHaveEmitted(
      new WprPickUpRequestAddedCreatedEvent(weeklyPlanningRequest, pickUpRequest.uuid)
    )
  })
})
