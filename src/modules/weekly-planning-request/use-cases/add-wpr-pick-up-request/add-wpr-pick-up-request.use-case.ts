import { randomUUID } from 'crypto'
import { Injectable } from '@nestjs/common'
import { DataSource, IsNull, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { AuthContext } from '../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { WeeklyPlanningRequest } from '../../entities/weekly-planning-request.entity.js'
import { PickUpRequest } from '../../../pick-up-request/entities/pick-up-request.entity.js'
import { AddWprPickUpRequestResponse } from './add-wpr-pick-up-request.response.js'
import { AddWprPickUpRequestCommand } from './add-wpr-pick-up-request.command.js'
import { WprPickUpRequestAddedCreatedEvent } from './wpr-pick-up-added.event.js'
import { AddWprPickUpRequestValidator } from './add-wpr-pick-up-request.validator.js'

@Injectable()
export class AddWprPickUpRequestUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly authContext: AuthContext,
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    @InjectRepository(WeeklyPlanningRequest)
    private readonly weeklyPlanningRequestRepository: Repository<WeeklyPlanningRequest>,
    private readonly validator: AddWprPickUpRequestValidator,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (
    weeklyPlanningRequestUuid: string,
    command: AddWprPickUpRequestCommand
  ): Promise<AddWprPickUpRequestResponse> {
    const userUuid = this.authContext.getUserUuidOrFail()

    const [pickUpRequest, weeklyPlanningRequest] = await Promise.all([
      this.pickUpRequestRepository.findOneByOrFail({
        uuid: command.pickUpRequestUuid,
        createdByUserUuid: userUuid
      }),
      this.weeklyPlanningRequestRepository.findOneOrFail({
        where: [
          {
            uuid: weeklyPlanningRequestUuid,
            createdByUserUuid: userUuid,
            customerId: this.authContext.getSelectedCustomerId() ?? undefined
          },
          {
            uuid: weeklyPlanningRequestUuid,
            createdByUserUuid: userUuid,
            customerId: IsNull()
          }
        ]
      })
    ])

    this.validator.validate(pickUpRequest, weeklyPlanningRequest)

    const pickUpRequestCopy = this.pickUpRequestRepository.create({
      ...pickUpRequest,
      uuid: randomUUID(),
      submittedOn: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      startDate: null,
      endDate: null,
      remarks: null,
      sendCopyToContacts: [],
      weeklyPlanningRequestUuid: weeklyPlanningRequest.uuid
    })

    await transaction(this.dataSource, async () => {
      await this.pickUpRequestRepository.insert(pickUpRequestCopy)
      await this.eventEmitter.emit([
        new WprPickUpRequestAddedCreatedEvent(
          weeklyPlanningRequest,
          pickUpRequestCopy.uuid
        )
      ])
    })

    return new AddWprPickUpRequestResponse(pickUpRequestCopy.uuid)
  }
}
