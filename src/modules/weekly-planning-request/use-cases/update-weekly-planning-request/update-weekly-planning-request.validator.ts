import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { IsNull, Repository } from 'typeorm'
import { AuthContext } from '../../../auth/auth.context.js'
import { WeeklyPlanningRequest } from '../../entities/weekly-planning-request.entity.js'
import { WeeklyPlanningRequestGeneralInfoValidator } from '../../validators/weekly-planning-request-general-info.validator.js'
import { WeeklyPlanningRequestExtraInfoValidator } from '../../validators/weekly-planning-request-extra-info.validator.js'
import { WeeklyPlanningRequestAlreadySubmittedError } from '../../errors/weekly-planning-request-already-submitted.error.js'
import { PickUpRequestIdMismatchError } from '../../errors/pick-up-request-id-mismatch.error.js'
import { UpdateWeeklyPlanningRequestCommand } from './update-weekly-planning-request.command.js'
import { UpdateWeeklyPlanningRequestMapper } from './mappers/update-weekly-planning-request.mapper.js'

@Injectable()
export class UpdateWeeklyPlanningRequestValidator {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(WeeklyPlanningRequest)
    private readonly weeklyPlanningRequestRepository: Repository<WeeklyPlanningRequest>,
    private readonly generalInfoValidator: WeeklyPlanningRequestGeneralInfoValidator,
    private readonly extraInfoValidator: WeeklyPlanningRequestExtraInfoValidator
  ) {}

  async validate (
    uuid: string,
    command: UpdateWeeklyPlanningRequestCommand
  ): Promise<void> {
    const userUuid = this.authContext.getUserUuidOrFail()

    const weeklyPlanningRequest = await this.weeklyPlanningRequestRepository.findOneOrFail({
      where: [
        {
          uuid: uuid,
          createdByUserUuid: userUuid,
          customerId: this.authContext.getSelectedCustomerId() ?? undefined
        },
        {
          uuid: uuid,
          createdByUserUuid: userUuid,
          customerId: IsNull()
        }
      ],
      relations: {
        pickUpRequests: true
      }
    })

    if (weeklyPlanningRequest.submittedOn !== null) {
      throw new WeeklyPlanningRequestAlreadySubmittedError()
    }

    const mergedWeeklyPlanningRequest = UpdateWeeklyPlanningRequestMapper
      .mapToMergedWeeklyPlanningRequest(
        command,
        weeklyPlanningRequest
      )

    await this.generalInfoValidator.validateUpdate(
      command,
      mergedWeeklyPlanningRequest
    )

    await this.extraInfoValidator.validateUpdate(
      command,
      mergedWeeklyPlanningRequest
    )

    this.validateGeneralInfoIds(mergedWeeklyPlanningRequest)
  }

  private validateGeneralInfoIds (
    weeklyPlanningRequest: WeeklyPlanningRequest
  ): void {
    assert(weeklyPlanningRequest.pickUpRequests !== undefined)

    for (const pickUpRequest of weeklyPlanningRequest.pickUpRequests) {
      if (
        weeklyPlanningRequest.customerId !== null
        && weeklyPlanningRequest.customerId !== pickUpRequest.customerId
      ) {
        throw new PickUpRequestIdMismatchError(
          { pointer: `$.pickUpRequests.${pickUpRequest.uuid}.$.customerId` }
        )
      }

      if (
        weeklyPlanningRequest.wasteProducerId !== null
        && weeklyPlanningRequest.wasteProducerId !== pickUpRequest.wasteProducerId
      ) {
        throw new PickUpRequestIdMismatchError(
          { pointer: `$.pickUpRequests.${pickUpRequest.uuid}.$.wasteProducerId` }
        )
      }

      if (
        weeklyPlanningRequest.pickUpAddressIds.length > 0
      ) {
        weeklyPlanningRequest.pickUpAddressIds.forEach((id) => {
          if (!pickUpRequest.pickUpAddressIds.includes(id)) {
            throw new PickUpRequestIdMismatchError(
              { pointer: `$.pickUpRequests.${pickUpRequest.uuid}.$.pickUpAddressIds` }
            )
          }
        })
      }
    }
  }
}
