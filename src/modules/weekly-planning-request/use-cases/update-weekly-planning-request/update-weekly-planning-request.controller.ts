import { <PERSON>, Controller, Patch } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { ApiBadRequestErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { CustomerNotAccessibleError } from '../../../customer/errors/customer-not-accessible.error.js'
import { FieldMustBeNullError } from '../../../exceptions/generic/field-must-be-null.error.js'
import { FileNotAccessibleError } from '../../../files/errors/file-not-accessible.error.js'
import { CustomerNotProvidedError } from '../../../customer/errors/customer-not-provided.error.js'
import { WasteProducerNotAccessibleError } from '../../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { PickUpAddressNotAccessibleError } from '../../../pick-up-address/errors/pick-up-address-not-accessible.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { UpdateWeeklyPlanningRequestCommand } from './update-weekly-planning-request.command.js'
import { UpdateWeeklyPlanningRequestUseCase } from './update-weekly-planning-request.use-case.js'
import { UpdateWeeklyPlanningRequestResponse } from './update-weekly-planning-request.response.js'

@ApiTags('Weekly planning request')
@Controller('weekly-planning-requests/:uuid')
@ApiOAuth2([])
export class UpdateWeeklyPlanningRequestController {
  constructor (
    private readonly useCase: UpdateWeeklyPlanningRequestUseCase
  ) {}

  @Patch()
  @GlobalCustomerRequired()
  @Permissions(Permission.WEEKLY_PLANNING_REQUEST_MANAGE)
  @ApiOkResponse({ type: UpdateWeeklyPlanningRequestResponse })
  @ApiNotFoundErrorResponse(NotFoundError)
  @ApiBadRequestErrorResponse(
    CustomerNotAccessibleError,
    WasteProducerNotAccessibleError,
    PickUpAddressNotAccessibleError,
    FieldMustBeNullError,
    FileNotAccessibleError,
    CustomerNotProvidedError
  )
  async updateWeeklyPlanningRequest (
    @UuidParam('uuid') weeklyPlanningRequestUuid: string,
    @Body() command: UpdateWeeklyPlanningRequestCommand
  ): Promise<UpdateWeeklyPlanningRequestResponse> {
    return await this.useCase.execute(weeklyPlanningRequestUuid, command)
  }
}
