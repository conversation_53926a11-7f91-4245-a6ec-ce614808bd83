import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { AuthContext } from '../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { WeeklyPlanningRequest } from '../../entities/weekly-planning-request.entity.js'
import { CreateWeeklyPlanningRequestCommand } from './create-weekly-planning-request.command.js'
import { WeeklyPlanningRequestCreatedEvent } from './weekly-planning-request-created.event.js'
import { CreateWeeklyPlanningRequestResponse } from './create-weekly-planning-request.response.js'

@Injectable()
export class CreateWeeklyPlanningRequestUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly authContext: AuthContext,
    @InjectRepository(WeeklyPlanningRequest)
    private readonly weeklyPlanningRequestRepository: Repository<WeeklyPlanningRequest>,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (
    _command: CreateWeeklyPlanningRequestCommand
  ): Promise<CreateWeeklyPlanningRequestResponse> {
    const userUuid = this.authContext.getUserUuidOrFail()

    const weeklyPlanningRequest = this.weeklyPlanningRequestRepository.create({
      createdByUserUuid: userUuid
    })

    await transaction(this.dataSource, async () => {
      await this.weeklyPlanningRequestRepository.save(weeklyPlanningRequest)
      await this.eventEmitter.emit([new WeeklyPlanningRequestCreatedEvent(weeklyPlanningRequest)])
    })

    return new CreateWeeklyPlanningRequestResponse(weeklyPlanningRequest)
  }
}
