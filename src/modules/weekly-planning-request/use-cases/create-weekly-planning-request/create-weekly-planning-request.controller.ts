import { Body, Controller, Post } from '@nestjs/common'
import { ApiCreatedResponse, ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { CreateWeeklyPlanningRequestUseCase } from './create-weekly-planning-request.use-case.js'
import { CreateWeeklyPlanningRequestResponse } from './create-weekly-planning-request.response.js'
import { CreateWeeklyPlanningRequestCommand } from './create-weekly-planning-request.command.js'

@ApiTags('Weekly planning request')
@Controller('weekly-planning-requests')
@ApiOAuth2([])
export class CreateWeeklyPlanningRequestController {
  constructor (
    private readonly useCase: CreateWeeklyPlanningRequestUseCase
  ) {}

  @Post()
  @Permissions(Permission.WEEKLY_PLANNING_REQUEST_MANAGE)
  @ApiCreatedResponse({ type: CreateWeeklyPlanningRequestResponse })
  async createWeeklyPlanningRequest (
    @Body() command: CreateWeeklyPlanningRequestCommand
  ): Promise<CreateWeeklyPlanningRequestResponse> {
    return await this.useCase.execute(command)
  }
}
