import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { IsNull, Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { WeeklyPlanningRequest } from '../../entities/weekly-planning-request.entity.js'
import { PickUpRequest } from '../../../pick-up-request/entities/pick-up-request.entity.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { FileLinkService } from '../../../files/services/file-link.service.js'
import { ContractLine } from '../../../contract-line/types/contract-line.type.js'
import { SapGetContractLineIndexUseCase } from '../../../sap/use-cases/get-contract-line-index/get-contract-line-index.use-case.js'
import { SapGetContractLineIndexResponse } from '../../../sap/use-cases/get-contract-line-index/get-contract-line-index.response.js'
import { MapContractLineSapService } from '../../../contract-line/services/map-contract-line.service.js'
import { EntityPart } from '../../../files/enums/entity-part.enum.js'
import { SapGetCustomerIndexUseCase } from '../../../sap/use-cases/get-customer-index/get-customer-index.use-case.js'
import { SapGetCustomerIndexResponse } from '../../../sap/use-cases/get-customer-index/get-customer-index.response.js'
import { MapCustomerSapService } from '../../../customer/services/map-customer-sap.service.js'
import { SapGetPickUpAddressIndexUseCase } from '../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.use-case.js'
import { SapGetWasteProducerIndexResponse } from '../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.response.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetWasteProducerIndexUseCase } from '../../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.use-case.js'
import { MapWasteProducerSapService } from '../../../waste-producer/services/map-waste-producer.service.js'
import { SapGetPickUpAddressIndexResponse } from '../../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.response.js'
import { MapPickUpAddressSapService } from '../../../pick-up-address/services/map-pick-up-address.service.js'
import { SapFilterGroup } from '../../../sap/query/types/sap-filter-group.js'
import { PickUpRequestMaterial } from '../../../pick-up-request/types/pick-up-request-material.type.js'
import { CustomerNotFoundError } from '../../../customer/errors/customer-not-found.error.js'
import { WasteProducerNotFoundError } from '../../../waste-producer/errors/waste-producer-not-found.error.js'
import { PickUpAddressNotFoundError } from '../../../pick-up-address/errors/pick-up-address-not-found.error.js'
import { ViewWeeklyPlanningRequestResponse } from './view-weekly-planning-request.response.js'
import { ViewWprPickUpRequestMapper } from './mappers/view-wpr-pick-up-request.mapper.js'

@Injectable()
export class ViewWeeklyPlanningRequestUseCase {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(WeeklyPlanningRequest)
    private readonly weeklyPlanningRequestRepository: Repository<WeeklyPlanningRequest>,
    private readonly sapGetContractLineIndex: SapGetContractLineIndexUseCase,
    private readonly fileLinkService: FileLinkService,
    private readonly sapGetCustomerIndex: SapGetCustomerIndexUseCase,
    private readonly sapGetWasteProducers: SapGetWasteProducerIndexUseCase,
    private readonly sapGetPickUpAddresses: SapGetPickUpAddressIndexUseCase
  ) {}

  async execute (uuid: string): Promise<ViewWeeklyPlanningRequestResponse> {
    const authUserUuid = this.authContext.getUserUuidOrFail()

    const weeklyPlanningRequest = await this.weeklyPlanningRequestRepository.findOneOrFail({
      where: [
        {
          uuid: uuid,
          createdByUserUuid: authUserUuid,
          customerId: this.authContext.getSelectedCustomerId() ?? undefined
        },
        {
          uuid: uuid,
          createdByUserUuid: authUserUuid,
          customerId: IsNull()
        }
      ],
      relations: {
        pickUpRequests: true
      }
    })

    assert(weeklyPlanningRequest.pickUpRequests !== undefined)

    const contractLines = await this.getContractLines(weeklyPlanningRequest.pickUpRequests)

    const pickUpRequestContractLines = ViewWprPickUpRequestMapper
      .mapPickUpRequests(
        weeklyPlanningRequest.pickUpRequests,
        contractLines
      )

    await Promise.all([
      this.loadCustomer(weeklyPlanningRequest),
      this.loadWasteProducer(weeklyPlanningRequest),
      await this.loadPickUpAddresses(weeklyPlanningRequest),
      this.loadFiles(weeklyPlanningRequest)
    ])

    return new ViewWeeklyPlanningRequestResponse(
      weeklyPlanningRequest,
      pickUpRequestContractLines
    )
  }

  private async getContractLines (pickUpRequests: PickUpRequest[]): Promise<ContractLine[]> {
    if (pickUpRequests.length === 0) return []

    const sapQuery = new SapQuery<SapGetContractLineIndexResponse>()
      .where((qb) => {
        for (let i = 0; i < pickUpRequests.length; i++) {
          const material = pickUpRequests[i].materials.at(0)!

          if (i === 0) {
            qb = qb.where((qb1) => {
              return this.getMaterialCondition(qb1, material)
            })
          } else {
            qb = qb.orWhere((qb1) => {
              return this.getMaterialCondition(qb1, material)
            })
          }
        }
        return qb
      })

    const sapResult = await this.sapGetContractLineIndex.execute(sapQuery)

    return MapContractLineSapService.mapResultsToContractLines(sapResult)
  }

  private getMaterialCondition (
    sapFilterGroup: SapFilterGroup<SapGetContractLineIndexResponse>,
    material: PickUpRequestMaterial
  ): SapFilterGroup<SapGetContractLineIndexResponse> {
    sapFilterGroup.where('Vbeln', material.contractNumber)
      .andWhere('Posnr', material.contractItem)

    if (material.tcNumber != null) {
      sapFilterGroup.andWhere('ActionNr', material.tcNumber)
    }

    return sapFilterGroup
  }

  async loadFiles (weeklyPlanningRequest: WeeklyPlanningRequest): Promise<void> {
    weeklyPlanningRequest.additionalFiles = await this.fileLinkService.loadFileLinks(
      weeklyPlanningRequest.uuid,
      WeeklyPlanningRequest.name,
      EntityPart.ADDITIONAL
    )
  }

  private async loadCustomer (weeklyPlanningRequest: WeeklyPlanningRequest): Promise<void> {
    if (weeklyPlanningRequest.customerId === null) {
      weeklyPlanningRequest.customer = null
      return
    }

    const sapQuery = new SapQuery<SapGetCustomerIndexResponse>()
      .where('Customer', weeklyPlanningRequest.customerId)

    const sapResponse = await this.sapGetCustomerIndex.execute(sapQuery)

    const customers = MapCustomerSapService.mapResultsToCustomers(sapResponse.items)

    if (customers.length === 0) {
      throw new CustomerNotFoundError(weeklyPlanningRequest.customerId)
    }

    weeklyPlanningRequest.customer = customers.at(0)
  }

  private async loadWasteProducer (
    weeklyPlanningRequest: WeeklyPlanningRequest
  ): Promise<void> {
    if (
      weeklyPlanningRequest.wasteProducerId === null
      || weeklyPlanningRequest.customerId === null
    ) {
      weeklyPlanningRequest.wasteProducer = null
      return
    }

    const sapQuery = new SapQuery<SapGetWasteProducerIndexResponse>()
      .where('Customer', weeklyPlanningRequest.customerId)
      .andWhere('WasteProducer', weeklyPlanningRequest.wasteProducerId)

    const sapResponse = await this.sapGetWasteProducers.execute(sapQuery)

    const wasteProducers = MapWasteProducerSapService.mapResultsToWasteProducers(sapResponse.items)

    if (wasteProducers.length === 0) {
      throw new WasteProducerNotFoundError(weeklyPlanningRequest.wasteProducerId)
    }

    weeklyPlanningRequest.wasteProducer = wasteProducers.at(0)
  }

  private async loadPickUpAddresses (
    weeklyPlanningRequest: WeeklyPlanningRequest
  ): Promise<void> {
    const numberOfAddresses = weeklyPlanningRequest.pickUpAddressIds.length

    if (
      numberOfAddresses === 0
      || weeklyPlanningRequest.customerId === null
    ) {
      weeklyPlanningRequest.pickUpAddresses = []

      return
    }

    const sapQuery = new SapQuery<SapGetPickUpAddressIndexResponse>()
      .where('Customer', weeklyPlanningRequest.customerId)
      .andWhere((qb) => {
        qb.where('PickUpAddress', weeklyPlanningRequest.pickUpAddressIds[0])
        for (let i = 1; i < weeklyPlanningRequest.pickUpAddressIds.length; i++) {
          qb.orWhere('PickUpAddress', weeklyPlanningRequest.pickUpAddressIds[i])
        }
        return qb
      })

    const sapResponse = await this.sapGetPickUpAddresses.execute(sapQuery)

    const pickUpAddresses = MapPickUpAddressSapService.mapResultsToPickUpAddresses(
      sapResponse.items
    )

    const foundPickUpAddressIds = pickUpAddresses.map(pickUpAddress => pickUpAddress.id)

    weeklyPlanningRequest.pickUpAddressIds.forEach((id) => {
      if (!foundPickUpAddressIds.includes(id)) {
        throw new PickUpAddressNotFoundError(id)
      }
    })

    weeklyPlanningRequest.pickUpAddresses = pickUpAddresses
  }
}
