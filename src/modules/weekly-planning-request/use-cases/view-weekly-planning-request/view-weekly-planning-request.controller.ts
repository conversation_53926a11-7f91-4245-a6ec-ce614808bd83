import { Controller, Get } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { CustomerNotFoundError } from '../../../customer/errors/customer-not-found.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewWeeklyPlanningRequestUseCase } from './view-weekly-planning-request.use-case.js'
import { ViewWeeklyPlanningRequestResponse } from './view-weekly-planning-request.response.js'

@ApiTags('Weekly planning request')
@Controller('weekly-planning-requests/:uuid')
@ApiOAuth2([])
export class ViewWeeklyPlanningRequestController {
  constructor (
    private readonly useCase: ViewWeeklyPlanningRequestUseCase
  ) {}

  @Get()
  @GlobalCustomerRequired()
  @Permissions(
    Permission.WEEKLY_PLANNING_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE
  )
  @ApiOkResponse({ type: ViewWeeklyPlanningRequestResponse })
  @ApiNotFoundErrorResponse(CustomerNotFoundError)
  async viewWeeklyPlanningRequest (
    @UuidParam('uuid') weeklyPlanningRequestUuid: string
  ): Promise<ViewWeeklyPlanningRequestResponse> {
    return await this.useCase.execute(weeklyPlanningRequestUuid)
  }
}
