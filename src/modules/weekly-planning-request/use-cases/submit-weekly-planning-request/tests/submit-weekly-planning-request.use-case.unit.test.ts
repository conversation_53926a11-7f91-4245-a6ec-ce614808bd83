import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { randNumber } from '@ngneat/falso'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { CreateWeeklyPlanningRequestSapUseCase } from '../../create-weekly-planning-request-sap/create-weekly-planning-request-sap.use-case.js'
import { SubmitWeeklyPlanningRequestValidator } from '../submit-weekly-planning-request.validator.js'
import { SubmitWeeklyPlanningRequestUseCase } from '../submit-weekly-planning-request.use-case.js'
import { WeeklyPlanningRequest } from '../../../entities/weekly-planning-request.entity.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { ValidWeeklyPlanningRequestEntityBuilder } from '../../../tests/valid-weekly-planning-request-entity.builder.js'
import { WeeklyPlanningRequestSubmittedEvent } from '../weekly-planning-request-submitted.event.js'
import { PickUpRequest } from '../../../../pick-up-request/entities/pick-up-request.entity.js'

describe('Submit weekly planning request use-case unit test', () => {
  let useCase: SubmitWeeklyPlanningRequestUseCase

  let userUuid: string
  let weeklyPlanningRequest: WeeklyPlanningRequest

  let authContext: SinonStubbedInstance<AuthContext>
  let validator: SinonStubbedInstance<SubmitWeeklyPlanningRequestValidator>
  let createWprSapUseCase: SinonStubbedInstance<CreateWeeklyPlanningRequestSapUseCase>
  let wprRepository: SinonStubbedInstance<Repository<WeeklyPlanningRequest>>
  let pickUpRequestRepository: SinonStubbedInstance<Repository<PickUpRequest>>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()
    weeklyPlanningRequest = new ValidWeeklyPlanningRequestEntityBuilder()
      .createdByUserUuid(userUuid)
      .build()

    authContext = createStubInstance(AuthContext)
    validator = createStubInstance(SubmitWeeklyPlanningRequestValidator)
    createWprSapUseCase = createStubInstance(CreateWeeklyPlanningRequestSapUseCase)
    wprRepository = createStubInstance<Repository<WeeklyPlanningRequest>>(
      Repository<WeeklyPlanningRequest>
    )
    pickUpRequestRepository = createStubInstance<Repository<PickUpRequest>>(
      Repository<PickUpRequest>
    )

    eventEmitter = createStubInstance(DomainEventEmitter)

    useCase = new SubmitWeeklyPlanningRequestUseCase(
      stubDataSource(),
      authContext,
      wprRepository,
      pickUpRequestRepository,
      validator,
      eventEmitter,
      createWprSapUseCase
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getUserUuidOrFail.returns(userUuid)
    createWprSapUseCase.execute.resolves([
      randNumber({ min: 10000000, max: 99999999 }).toString(),
      weeklyPlanningRequest.pickUpRequests?.map(
        () => randNumber({ min: 10000000, max: 99999999 }).toString()
      ) || []
    ])
    wprRepository.findOneOrFail.resolves(weeklyPlanningRequest)
  }

  it('Calls all methods once', async () => {
    await useCase.execute(randomUUID())

    assert.calledOnce(validator.validate)
    assert.calledOnce(wprRepository.findOneOrFail)
    assert.calledOnce(createWprSapUseCase.execute)
    assert.calledOnce(wprRepository.save)
  })

  it('Emits a WeeklyPlanningRequestSubmittedEvent event', async () => {
    const weeklyPlanningRequestUuid = randomUUID()

    await useCase.execute(weeklyPlanningRequestUuid)

    expect(eventEmitter).toHaveEmitted(
      new WeeklyPlanningRequestSubmittedEvent(weeklyPlanningRequestUuid)
    )
  })
})
