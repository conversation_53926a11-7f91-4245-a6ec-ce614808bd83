import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Module } from '@nestjs/common'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { SapModule } from '../../../sap/sap.module.js'
import { WeeklyPlanningRequest } from '../../entities/weekly-planning-request.entity.js'
import { CreateWeeklyPlanningRequestSapUseCase } from '../create-weekly-planning-request-sap/create-weekly-planning-request-sap.use-case.js'
import { PickUpRequestModule } from '../../../pick-up-request/pick-up-request.module.js'
import { WeeklyPlanningRequestGeneralInfoValidator } from '../../validators/weekly-planning-request-general-info.validator.js'
import { WeeklyPlanningRequestExtraInfoValidator } from '../../validators/weekly-planning-request-extra-info.validator.js'
import { PermissionModule } from '../../../permission/permission.module.js'
import { FileModule } from '../../../files/file.module.js'
import { PickUpRequest } from '../../../pick-up-request/entities/pick-up-request.entity.js'
import { CustomerModule } from '../../../customer/customer.module.js'
import { PickUpRequestServicesModule } from '../../../pick-up-request/services/services.module.js'
import { SubmitWeeklyPlanningRequestUseCase } from './submit-weekly-planning-request.use-case.js'
import { SubmitWeeklyPlanningRequestValidator } from './submit-weekly-planning-request.validator.js'
import { SubmitWeeklyPlanningRequestController } from './submit-weekly-planning-request.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      WeeklyPlanningRequest,
      PickUpRequest,
      File
    ]),
    FileModule,
    DomainEventEmitterModule,
    PickUpRequestModule,
    SapModule,
    PermissionModule,
    CustomerModule,
    PickUpRequestServicesModule
  ],
  controllers: [
    SubmitWeeklyPlanningRequestController
  ],
  providers: [
    SubmitWeeklyPlanningRequestUseCase,
    CreateWeeklyPlanningRequestSapUseCase,

    SubmitWeeklyPlanningRequestValidator,
    WeeklyPlanningRequestGeneralInfoValidator,
    WeeklyPlanningRequestExtraInfoValidator
  ]
})
export class SubmitWeeklyPlanningRequestModule {}
