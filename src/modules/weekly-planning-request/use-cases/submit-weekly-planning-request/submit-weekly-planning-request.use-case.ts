import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { DataSource, IsNull, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { WeeklyPlanningRequest } from '../../entities/weekly-planning-request.entity.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { CreateWeeklyPlanningRequestSapUseCase } from '../create-weekly-planning-request-sap/create-weekly-planning-request-sap.use-case.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { PickUpRequest } from '../../../pick-up-request/entities/pick-up-request.entity.js'
import { SubmitWeeklyPlanningRequestValidator } from './submit-weekly-planning-request.validator.js'
import { SubmitWeeklyPlanningRequestResponse } from './submit-weekly-planning-request.response.js'
import { WeeklyPlanningRequestSubmittedEvent } from './weekly-planning-request-submitted.event.js'

@Injectable()
export class SubmitWeeklyPlanningRequestUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly authContext: AuthContext,
    @InjectRepository(WeeklyPlanningRequest)
    private readonly weeklyPlanningRequestRepository: Repository<WeeklyPlanningRequest>,
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    private readonly validator: SubmitWeeklyPlanningRequestValidator,
    private readonly eventEmitter: DomainEventEmitter,
    private readonly createWprSapUseCase: CreateWeeklyPlanningRequestSapUseCase
  ) {}

  async execute (
    weeklyPlanningRequestUuid: string
  ): Promise<SubmitWeeklyPlanningRequestResponse> {
    const userUuid = this.authContext.getUserUuidOrFail()

    const weeklyPlanningRequest = await this.weeklyPlanningRequestRepository.findOneOrFail({
      where: [
        {
          uuid: weeklyPlanningRequestUuid,
          createdByUserUuid: userUuid,
          customerId: this.authContext.getSelectedCustomerId() ?? undefined
        },
        {
          uuid: weeklyPlanningRequestUuid,
          createdByUserUuid: userUuid,
          customerId: IsNull()
        }
      ],
      relations: {
        pickUpRequests: true
      }
    })

    assert(weeklyPlanningRequest.pickUpRequests !== undefined)

    await this.validator.validate(weeklyPlanningRequest)

    const [wprInquiryNumber, pickUpRequestInquiryNumber] = await this.createWprSapUseCase.execute(
      weeklyPlanningRequestUuid
    )
    const submittedOn = new Date()

    weeklyPlanningRequest.inquiryNumber = wprInquiryNumber
    weeklyPlanningRequest.submittedOn = submittedOn

    weeklyPlanningRequest.pickUpRequests.forEach((pickUpRequest, index) => {
      pickUpRequest.requestNumber = pickUpRequestInquiryNumber[index]
      pickUpRequest.submittedOn = submittedOn
    })

    await transaction(this.dataSource, async () => {
      await this.weeklyPlanningRequestRepository.save(weeklyPlanningRequest)
      await this.pickUpRequestRepository.save(weeklyPlanningRequest.pickUpRequests!)

      await this.eventEmitter.emitOne(
        new WeeklyPlanningRequestSubmittedEvent(weeklyPlanningRequestUuid)
      )
    })

    return new SubmitWeeklyPlanningRequestResponse(weeklyPlanningRequest)
  }
}
