import { Controller, Post, HttpCode } from '@nestjs/common'
import { Api<PERSON><PERSON>s, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { ApiNotFoundErrorResponse, ApiBadRequestErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { WeeklyPlanningRequestAlreadySubmittedError } from '../../errors/weekly-planning-request-already-submitted.error.js'
import { ContractLineNotAccessibleError } from '../../../contract-line/errors/contract-line-not-accessible.error.js'
import { ContractLineNotOfCustomerError } from '../../../contract-line/errors/contract-line-not-of-customer.error.js'
import { ContractLineNotOfPickUpAddressesError } from '../../../contract-line/errors/contract-line-not-of-pick-up-addresses.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { SubmitWeeklyPlanningRequestResponse } from './submit-weekly-planning-request.response.js'
import { SubmitWeeklyPlanningRequestUseCase } from './submit-weekly-planning-request.use-case.js'

@ApiTags('Weekly planning request')
@Controller('weekly-planning-requests/:uuid/submit')
@ApiOAuth2([])
export class SubmitWeeklyPlanningRequestController {
  constructor (
    private readonly useCase: SubmitWeeklyPlanningRequestUseCase
  ) {}

  @Post()
  @GlobalCustomerRequired()
  @HttpCode(200)
  @Permissions(Permission.WEEKLY_PLANNING_REQUEST_MANAGE)
  @ApiOkResponse({
    description: 'Weekly planning request submitted',
    type: SubmitWeeklyPlanningRequestResponse
  })
  @ApiNotFoundErrorResponse(NotFoundError)
  @ApiBadRequestErrorResponse(
    WeeklyPlanningRequestAlreadySubmittedError,
    ContractLineNotAccessibleError,
    ContractLineNotOfCustomerError,
    ContractLineNotOfPickUpAddressesError
  )
  async submitWeeklyPlanningRequest (
    @UuidParam('uuid') weeklyPlanningRequestUuid: string
  ): Promise<SubmitWeeklyPlanningRequestResponse> {
    return await this.useCase.execute(weeklyPlanningRequestUuid)
  }
}
