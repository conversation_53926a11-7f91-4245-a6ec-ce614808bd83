import { AsyncLocalStorage } from 'async_hooks'
import { Injectable } from '@nestjs/common'
import { UnauthorizedError } from '../exceptions/generic/unauthorized.error.js'

export interface AuthContent {
  zitadelSub: string | null
  uuid: string
  impersonateUserUuid: string | null
  azureEntraId: string
  azureEntraUpn: string
  selectedCustomerId: string | null
}

@Injectable()
export class AuthContext {
  private readonly authStorage = new AsyncLocalStorage<AuthContent>()

  public getAuthOrFail (): AuthContent {
    const token = this.authStorage.getStore()

    if (token == null) {
      throw new UnauthorizedError()
    }

    return token
  }

  public getAuth (): AuthContent | null {
    const token = this.authStorage.getStore()

    return token ?? null
  }

  public getUserUuidOrFail (): string {
    return this.getAuthOrFail().impersonateUserUuid ?? this.getAuthOrFail().uuid
  }

  public getUserUuid (): string | null {
    return this.getAuth()?.impersonateUserUuid ?? this.getAuth()?.uuid ?? null
  }

  public getAzureEntraIdOrFail (): string {
    const auth = this.getAuthOrFail()

    // TODO get azureEntraId from impersonated user if available

    if (!auth.azureEntraId) {
      throw new UnauthorizedError('error.auth.azure_unauthorized_id_auth_context')
    }

    return auth.azureEntraId
  }

  public getAzureEntraUpnOrFail (): string {
    const auth = this.getAuthOrFail()

    // TODO get azureEntraUpn from impersonated user if available

    if (!auth.azureEntraUpn) {
      throw new UnauthorizedError('error.auth.azure_unauthorized_upn_auth_context')
    }

    return auth.azureEntraUpn
  }

  public isInternalUser (): boolean {
    const azureEntraUpn = this.getAzureEntraUpnOrFail()

    const domain = azureEntraUpn.split('@').at(-1)

    return domain?.toLocaleLowerCase() === 'indaver.com'
  }

  public getSelectedCustomerId (): string | null {
    return this.getAuthOrFail().selectedCustomerId
  }

  public run (content: AuthContent, callback: () => void): void {
    this.authStorage.run(content, callback)
  }
}
