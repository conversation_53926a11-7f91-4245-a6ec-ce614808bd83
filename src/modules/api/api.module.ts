import { MiddlewareConsumer, Module } from '@nestjs/common'
import { AppModule } from '../../app.module.js'
import { AuthModule } from '../auth/auth.module.js'
import { SwaggerModule } from '../swagger/swagger.module.js'
import { StatusModule } from '../status/status.module.js'
import { UserModule } from '../../app/users/user.module.js'
import { RoleModule } from '../../app/roles/role.module.js'
import { PermissionModule } from '../permission/permission.module.js'
import { FileModule } from '../files/file.module.js'
import { LocalizationModule } from '../localization/modules/localization.module.js'
import { CustomerModule } from '../customer/customer.module.js'
import { WasteProducerModule } from '../waste-producer/waste-producer.module.js'
import { PickUpAddressModule } from '../pick-up-address/pick-up-address.module.js'
import { WasteInquiryModule } from '../waste-inquiry/waste-inquiry.module.js'
import { ImpersonationMiddleware } from '../auth/middleware/impersonation.middleware.js'
import { EwcCodeModule } from '../ewc-code/ewc-code.module.js'
import { NewsModule } from '../news/news.module.js'
import { AnnouncementModule } from '../announcement/announcement.module.js'
import { PickUpRequestModule } from '../pick-up-request/pick-up-request.module.js'
import { NewsletterModule } from '../newsletter/newsletter.module.js'
import { PackagingTypeModule } from '../packaging-type/packaging-type.module.js'
import { UnNumberModule } from '../un-number/un-number.module.js'
import { ContainerTypeModule } from '../container-type/container-type.module.js'
import { DynamicTableModule } from '../dynamic-tables/dynamic-tables.module.js'
import { AuthMiddleware } from '../auth/middleware/auth.middleware.js'
import { DomainEventLogModule } from '../domain-event-log/domain-event-log.module.js'
import { GlobalSearchModule } from '../global-search/global-search.module.js'
import { NotificationModule } from '../notification/notification.module.js'
import { UiPreferencesModule } from '../../app/ui-preferences/ui-preferences.module.js'
import { ContactModule } from '../contact/contact.module.js'
import { ContractLineModule } from '../contract-line/contract-line.module.js'
import { JobsApiModule } from '../jobs/jobs.api-module.js'
import { WeeklyPlanningRequestModule } from '../weekly-planning-request/weekly-planning-request.module.js'
import { PackagingRequestModule } from '../packaging-request/packaging-request.module.js'
import { InvoiceModule } from '../invoice/invoice.module.js'
import { TransportTypeModule } from '../transport-type/transport-type.module.js'
import { TankerTypeModule } from '../tanker-type/tanker-type.module.js'
import { DocumentModule } from '../document/document.module.js'
import { GuidanceLetterModule } from '../guidance-letter/guidance-letter.module.js'

@Module({
  imports: [
    AppModule.forRoot(),
    AuthModule,
    DomainEventLogModule,
    FileModule,
    GlobalSearchModule,
    JobsApiModule,
    LocalizationModule,
    NotificationModule,
    PermissionModule,
    RoleModule,
    StatusModule,
    SwaggerModule,
    UiPreferencesModule,
    UserModule,

    AnnouncementModule,
    ContactModule,
    ContainerTypeModule,
    ContractLineModule,
    CustomerModule,
    DocumentModule,
    DynamicTableModule,
    EwcCodeModule,
    GuidanceLetterModule,
    InvoiceModule,
    NewsletterModule,
    NewsModule,
    PackagingRequestModule,
    PackagingTypeModule,
    PickUpAddressModule,
    PickUpRequestModule,
    TankerTypeModule,
    TransportTypeModule,
    UnNumberModule,
    WasteInquiryModule,
    WasteProducerModule,
    WeeklyPlanningRequestModule
  ]
})
export class ApiModule {
  configure (consumer: MiddlewareConsumer): void {
    consumer
      .apply(AuthMiddleware, ImpersonationMiddleware)
      .exclude('auth/token')
      .forRoutes('{*all}')
  }
}
