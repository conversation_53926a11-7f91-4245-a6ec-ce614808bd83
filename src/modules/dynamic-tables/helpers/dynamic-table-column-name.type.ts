import { ContractLineColumnName } from '../enums/contract-line-column-name.enum.js'
import { DraftInvoiceColumnName } from '../enums/draft-invoice-column-name.enum.js'
import { FormPickUpRequestContractLineColumnName } from '../enums/form-pick-up-request-contract-line-column-name.enum.js'
import { GuidanceLetterColumnName } from '../enums/guidance-letter-column-name.enum.js'
import { InvoiceColumnName } from '../enums/invoice-column-name.enum.js'
import { PickUpRequestColumnName } from '../enums/pick-up-request-column-name.enum.js'
import { WasteInquiryColumnName } from '../enums/waste-inquiry-column-name.enum.js'

export type DynamicColumnName = ContractLineColumnName
  | DraftInvoiceColumnName
  | FormPickUpRequestContractLineColumnName
  | InvoiceColumnName
  | PickUpRequestColumnName
  | WasteInquiryColumnName
  | GuidanceLetterColumnName

export const DynamicColumnNames = {
  ...ContractLineColumnName,
  ...DraftInvoiceColumnName,
  ...FormPickUpRequestContractLineColumnName,
  ...InvoiceColumnName,
  ...PickUpRequestColumnName,
  ...WasteInquiryColumnName,
  ...GuidanceLetterColumnName
}
