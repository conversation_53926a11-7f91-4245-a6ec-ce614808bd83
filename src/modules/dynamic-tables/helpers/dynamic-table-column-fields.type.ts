import { ContractLineDynamicTableFields } from '../../contract-line/types/contract-line.dynamic-table-fields.type.js'
import { GuidanceLetterDynamicTableFields } from '../../guidance-letter/types/guidance-letter.dynamic-table-fields.type.js'
import { InvoiceDynamicTableFields } from '../../invoice/types/invoice.dynamic-table-fields.type.js'
import { FormPickUpRequestContractLineDynamicTableFields } from '../../pick-up-request/types/form-pick-up-request-contract-line.dynamic-table-fields.type.js'
import { PickUpRequestDynamicTableFields } from '../../pick-up-request/types/pick-up-request.dynamic-table-fields.type.js'
import { WasteInquiryDynamicTableFields } from '../../waste-inquiry/types/waste-inquiry.dynamic-table-fields.type.js'

export type DynamicTableColumnField = keyof WasteInquiryDynamicTableFields
  | keyof PickUpRequestDynamicTableFields
  | keyof ContractLineDynamicTableFields
  | keyof InvoiceDynamicTableFields
  | keyof FormPickUpRequestContractLineDynamicTableFields
  | keyof GuidanceLetterDynamicTableFields
