import { ViewGuidanceLetterIndexQuery } from '../query/view-guidance-letter-index.query.js'

export class ViewGuidanceLetterIndexQueryBuilder {
  private query: ViewGuidanceLetterIndexQuery

  constructor () {
    this.reset()
  }

  reset (): this {
    this.query = new ViewGuidanceLetterIndexQuery()

    return this
  }

  build (): ViewGuidanceLetterIndexQuery {
    const result = this.query

    this.reset()

    return result
  }
}
