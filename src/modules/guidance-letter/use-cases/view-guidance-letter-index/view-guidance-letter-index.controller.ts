import { Controller, Get, Query } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewGuidanceLetterIndexQuery } from './query/view-guidance-letter-index.query.js'
import { ViewGuidanceLetterIndexResponse } from './view-guidance-letter-index.response.js'
import { ViewGuidanceLetterIndexUseCase } from './view-guidance-letter-index.use-case.js'

@ApiTags('Guidance Letter')
@ApiOAuth2([])
@Controller('guidance-letters')
export class ViewGuidanceLetterIndexController {
  constructor (
    private readonly useCase: ViewGuidanceLetterIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.GUIDANCE_LETTER_READ)
  @ApiOkResponse({ type: ViewGuidanceLetterIndexResponse })
  public async viewGuidanceLetterIndex (
    @Query() query: ViewGuidanceLetterIndexQuery
  ): Promise<ViewGuidanceLetterIndexResponse> {
    return await this.useCase.execute(query)
  }
}
