import { ArrayUnique, Equals, <PERSON><PERSON><PERSON>y, IsObject, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { PaginatedKeysetSearchQuery } from '@wisemen/pagination'
import { IsUndefinable } from '@wisemen/validators'
import { ViewGuidanceLetterIndexPaginationQuery } from './view-guidance-letter-index.pagination-query.js'
import { ViewGuidanceLetterIndexSortQuery } from './view-guidance-letter-index.sort-query.js'

export class ViewGuidanceLetterIndexQuery extends PaginatedKeysetSearchQuery {
  @ApiProperty({ type: ViewGuidanceLetterIndexSortQuery, required: false, isArray: true })
  @Type(() => ViewGuidanceLetterIndexSortQuery)
  @ValidateNested({ each: true })
  @IsUndefinable()
  @IsArray()
  @ArrayUnique()
  sort?: ViewGuidanceLetterIndexSortQuery[]

  @Equals(undefined)
  filter?: never

  @Equals(undefined)
  search?: never

  @ApiProperty({ type: ViewGuidanceLetterIndexPaginationQuery, required: false })
  @IsUndefinable()
  @Type(() => ViewGuidanceLetterIndexPaginationQuery)
  @ValidateNested()
  @IsObject()
  pagination?: ViewGuidanceLetterIndexPaginationQuery
}
