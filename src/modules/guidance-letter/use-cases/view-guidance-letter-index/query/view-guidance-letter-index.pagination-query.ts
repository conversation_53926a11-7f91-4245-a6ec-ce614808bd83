import { IsObject, IsOptional, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { PaginatedKeysetQuery } from '@wisemen/pagination'
import { Type } from 'class-transformer'
import { ViewGuidanceLetterIndexQueryKey } from './view-guidance-letter-index.query-key.js'

export class ViewGuidanceLetterIndexPaginationQuery extends PaginatedKeysetQuery {
  @ApiProperty({ type: ViewGuidanceLetterIndexQueryKey, required: false, nullable: true })
  @Type(() => ViewGuidanceLetterIndexQueryKey)
  @ValidateNested()
  @IsObject()
  @IsOptional()
  key?: ViewGuidanceLetterIndexQueryKey | null
}
