import { ApiProperty } from '@nestjs/swagger'
import { SortQuery, SortDirectionApiProperty, SortDirection } from '@wisemen/pagination'
import { IsEnum } from 'class-validator'

export enum ViewGuidanceLetterIndexSortQueryKey {
  SHIPMENT_ID = 'shipmentId',
  CONTRACT_NUMBER = 'contractNumber',
  REQUEST_NUMBER = 'requestNumber',
  WASTE_MATERIAL = 'wasteMaterial',
  TRANSPORT_DATE = 'transportDate',
  CUSTOMER_ID = 'customerId',
  WASTE_PRODUCER_ID = 'wasteProducerId',
  PICK_UP_ADDRESS_ID = 'pickUpAddressId',
  CUSTOMER_NAME = 'customerName',
  WASTE_PRODUCER_NAME = 'wasteProducerName',
  PICK_UP_ADDRESS_NAME = 'pickUpAddressName'
}

export class ViewGuidanceLetterIndexSortQuery extends SortQuery {
  @ApiProperty({ enum: ViewGuidanceLetterIndexSortQueryKey, enumName: 'ViewGuidanceLetterIndexSortQueryKey' })
  @IsEnum(ViewGuidanceLetterIndexSortQueryKey)
  key: ViewGuidanceLetterIndexSortQueryKey

  @SortDirectionApiProperty()
  @IsEnum(SortDirection)
  order: SortDirection
}
