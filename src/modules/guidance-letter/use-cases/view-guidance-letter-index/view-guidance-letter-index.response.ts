import { ApiProperty } from '@nestjs/swagger'
import { PaginatedKeysetResponse, PaginatedKeysetResponseMeta } from '@wisemen/pagination'
import { GuidanceLetter } from '../../types/guidance-letter.type.js'
import { ViewContractLineIndexQueryKey } from '../../../contract-line/use-cases/view-contract-line-index/query/view-contract-line-index.query-key.js'
import { ViewGuidanceLetterIndexQueryKey } from './query/view-guidance-letter-index.query-key.js'

class GuidanceLetterResponse {
  @ApiProperty({ type: String })
  shipmentId: string

  @ApiProperty({ type: String })
  contractNumber: string

  @ApiProperty({ type: Boolean })
  guidanceLetter: boolean

  @ApiProperty({ type: Boolean })
  attachement: boolean

  @ApiProperty({ type: String, nullable: true })
  requestNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteMaterial: string | null

  @ApiProperty({ type: Number, nullable: true })
  weightOrVolume: number | null

  @ApiProperty({ type: String, nullable: true })
  unit: string | null

  @ApiProperty({ type: String, nullable: true })
  transportDate: string | null

  @ApiProperty({ type: String, nullable: true })
  customerId: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerId: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressId: string | null

  @ApiProperty({ type: String, nullable: true })
  customerName: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerName: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressName: string | null

  constructor (guidanceLetter: GuidanceLetter) {
    this.shipmentId = guidanceLetter.shipmentId
    this.contractNumber = guidanceLetter.contractNumber
    this.guidanceLetter = guidanceLetter.guidanceLetter
    this.attachement = guidanceLetter.attachement
    this.requestNumber = guidanceLetter.requestNumber
    this.wasteMaterial = guidanceLetter.wasteMaterial
    this.weightOrVolume = guidanceLetter.weightOrVolume
    this.unit = guidanceLetter.unit
    this.transportDate = guidanceLetter.transportDate
    this.customerId = guidanceLetter.customerId
    this.wasteProducerId = guidanceLetter.wasteProducerId
    this.pickUpAddressId = guidanceLetter.pickUpAddressId
    this.customerName = guidanceLetter.customerName
    this.wasteProducerName = guidanceLetter.wasteProducerName
    this.pickUpAddressName = guidanceLetter.pickUpAddressName
  }
}

class ViewGuidanceLetterIndexResponseMeta implements PaginatedKeysetResponseMeta {
  @ApiProperty({ type: ViewContractLineIndexQueryKey, nullable: true })
  next: ViewContractLineIndexQueryKey | null

  constructor (skipToken: string | null) {
    this.next = skipToken !== null
      ? new ViewGuidanceLetterIndexQueryKey(skipToken)
      : null
  }
}

export class ViewGuidanceLetterIndexResponse implements PaginatedKeysetResponse {
  @ApiProperty({ type: GuidanceLetterResponse, isArray: true })
  declare items: GuidanceLetterResponse[]

  @ApiProperty({ type: ViewGuidanceLetterIndexResponseMeta })
  declare meta: ViewGuidanceLetterIndexResponseMeta

  constructor (items: GuidanceLetter[], skipToken: string | null) {
    this.items = items.map(item => new GuidanceLetterResponse(item))
    this.meta = new ViewGuidanceLetterIndexResponseMeta(skipToken)
  }
}
