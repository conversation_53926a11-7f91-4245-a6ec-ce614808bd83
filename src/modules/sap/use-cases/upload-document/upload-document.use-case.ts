import { Inject, Injectable } from '@nestjs/common'
import { AxiosInstance } from 'axios'
import { SAP_CLIENT } from '../../sap-client.provider.js'
import { SAP_ENDPOINTS } from '../../constants/endpoint.constants.js'
import { MimeType } from '../../../files/enums/mime-type.enum.js'
import { SapUploadDocumentResponse } from './upload-document.response.js'
import { SapDocumentType } from './sap-document-type.type.js'

@Injectable()
export class SapUploadDocumentUseCase {
  constructor (
      @Inject(SAP_CLIENT) private client: AxiosInstance
  ) {}

  async execute (
    fileBuffer: Buffer,
    mimeType: MimeType,
    requestNumber: string,
    documentType: SapDocumentType
  ): Promise<SapUploadDocumentResponse> {
    const url = SAP_ENDPOINTS.DOCUMENT.UPLOAD

    const slugHeader = `SapObject=BUS2030;ObjectId=${requestNumber};ArObject=${documentType};Mimetype=${mimeType}`

    const response = await this.client.post<SapUploadDocumentResponse>(
      url,
      fileBuffer,
      {
        headers: {
          'Content-Type': mimeType,
          'Accept': 'application/json',
          'Slug': slugHeader
        }
      }
    )

    return response.data
  }
}
