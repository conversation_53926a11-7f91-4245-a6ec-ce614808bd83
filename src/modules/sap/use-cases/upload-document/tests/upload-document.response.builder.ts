import { randNumber, randText, randUrl } from '@ngneat/falso'
import { SAP_ENDPOINTS } from '../../../constants/endpoint.constants.js'
import { SapUploadDocumentResponse } from '../upload-document.response.js'

export class SapUploadDocumentResponseBuilder {
  private response: SapUploadDocumentResponse

  constructor () {
    this.reset()
  }

  reset (): this {
    const objectId = randNumber({ min: 1000000, max: 9999999 }).toString()
    const archiveId = randNumber({ min: 100000, max: 999999 }).toString()
    const arcDocId = randNumber({ min: 1000000, max: 9999999 }).toString()
    const baseUrl = `https://services.odata.org/OData${SAP_ENDPOINTS.DOCUMENT.UPLOAD}`

    this.response = {
      d: {
        __metadata: {
          id: `${baseUrl}(SapObject='BUS2000108',ObjectId='${objectId}')`,
          uri: `${baseUrl}(SapObject='BUS2000108',ObjectId='${objectId}')`,
          type: 'ALDS_ODATA_SRV.ArchiveLinkDocument',
          content_type: 'application/pdf',
          media_src: `${baseUrl}(SapObject='BUS2000108',ObjectId='${objectId}')/$value`,
          edit_media: `${baseUrl}(SapObject='BUS2000108',ObjectId='${objectId}')/$value`
        },
        SapObject: 'BUS2000108',
        ObjectId: objectId,
        ArchivId: archiveId,
        ArcDocId: arcDocId,
        ArObject: 'DRAW',
        DocContent: randText({ charCount: 20 }),
        Mimetype: 'application/pdf',
        ArUrl: randUrl()
      }
    }

    return this
  }

  build (): SapUploadDocumentResponse {
    const result = this.response

    this.reset()

    return result
  }
}
