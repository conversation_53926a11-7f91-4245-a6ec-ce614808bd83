import { randNumber, randProductName, randUrl, randWord } from '@ngneat/falso'
import dayjs from 'dayjs'
import { SAP_ENDPOINTS } from '../../../constants/endpoint.constants.js'
import { ArchiveLinkAttribute, SapGetDocumentResponse } from '../get-documents-index.response.js'

export class GetDocumentIndexResponseBuilder {
  private response: SapGetDocumentResponse

  constructor () {
    this.reset()
  }

  reset (): this {
    const documentId = randNumber({ min: 1000000, max: 9999999 }).toString()
    const archiveId = randNumber({ min: 100000, max: 999999 }).toString()
    const objectId = randNumber({ min: 1000000, max: 9999999 }).toString()

    this.response = {
      __metadata: {
        id: `https://services.odata.org/OData${SAP_ENDPOINTS.DOCUMENT.INDEX}`,
        uri: `https://services.odata.org/OData${SAP_ENDPOINTS.DOCUMENT.INDEX}`,
        type: 'ALDS_ODATA_SRV.ArchiveLinkDocument'
      },
      ArUrl: randUrl(),
      SapObject: 'BUS2000108',
      ObjectId: objectId,
      ArchivId: archiveId,
      ArcDocId: documentId,
      ArObject: 'DRAW',
      ArDate: dayjs().toISOString(),
      DocIcon: 'PDF',
      Description: randProductName(),
      RmStatusIcon: 'S',
      RmStatus: 'Stored',
      Reserve: '',
      DocPermission: 'RW',
      RmPermission: 'RW',
      DocToDocCont: {
        __deferred: {
          uri: `https://services.odata.org/OData${SAP_ENDPOINTS.DOCUMENT.INDEX}(ObjectId='${objectId}',ArchivId='${archiveId}',ArcDocId='${documentId}')/DocToDocCont`
        }
      },
      DocToAttributeNav: {
        results: [this.createAttribute(objectId, archiveId, documentId)]
      }
    }

    return this
  }

  private createAttribute (objectId: string, archiveId: string,
    arcDocId: string): ArchiveLinkAttribute {
    return {
      __metadata: {
        id: `https://services.odata.org/OData${SAP_ENDPOINTS.DOCUMENT.INDEX}(ObjectId='${objectId}',ArchivId='${archiveId}',ArcDocId='${arcDocId}')/DocToAttributeNav`,
        uri: `https://services.odata.org/OData${SAP_ENDPOINTS.DOCUMENT.INDEX}(ObjectId='${objectId}',ArchivId='${archiveId}',ArcDocId='${arcDocId}')/DocToAttributeNav`,
        type: 'ALDS_ODATA_SRV.ArchiveLinkAttribute'
      },
      ExtAttrType: 'ZSD_SW',
      FieldScale: 0,
      FieldType: 'CHAR',
      SapObject: 'BUS2000108',
      ExtAttribute: 'ZDOC_TYPE',
      FieldLength: 10,
      ObjectId: objectId,
      ArchivId: archiveId,
      FieldLabel: 'Document Type',
      ArcDocId: arcDocId,
      ArObject: 'DRAW',
      FieldName: 'DOC_TYPE',
      FieldValue: randWord(),
      CheckTable: false,
      CheckTableError: ''
    }
  }

  build (): SapGetDocumentResponse {
    const result = this.response

    this.reset()

    return result
  }
}
