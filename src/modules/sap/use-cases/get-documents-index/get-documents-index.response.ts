export interface SapGetDocumentResponse {
  __metadata: DocumentMetadata
  ArUrl: string
  SapObject: string
  ObjectId: string
  ArchivId: string
  ArcDocId: string
  ArObject: string
  ArDate: string
  DocIcon: string
  Description: string
  RmStatusIcon: string
  RmStatus: string
  Reserve: string
  DocPermission: string
  RmPermission: string
  DocToDocCont: DocToDocCont
  DocToAttributeNav: DocToAttributeNav
}

export interface DocToDocCont {
  __deferred: {
    uri: string
  }
}

export interface ArchiveLinkAttribute {
  __metadata: DocumentMetadata
  ExtAttrType: string
  FieldScale: number
  FieldType: string
  SapObject: string
  ExtAttribute: string
  FieldLength: number
  ObjectId: string
  ArchivId: string
  FieldLabel: string
  ArcDocId: string
  ArObject: string
  FieldName: string
  FieldValue: string
  CheckTable: boolean
  CheckTableError: string
}

export interface DocToAttributeNav {
  results: ArchiveLinkAttribute[]
}

export interface DocumentMetadata {
  id: string
  uri: string
  type: string
}
