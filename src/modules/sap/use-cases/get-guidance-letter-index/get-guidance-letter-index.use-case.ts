import { Inject, Injectable } from '@nestjs/common'
import { AxiosInstance } from 'axios'
import { SAP_ENDPOINTS } from '../../constants/endpoint.constants.js'
import { SapQuery } from '../../query/sap-query.js'
import { SAP_CLIENT } from '../../sap-client.provider.js'
import { SapDResultResponse } from '../../shared/sap-d-result.response.js'
import { SapGetGuidanceLetterIndexResponse } from './get-guidance-letter-index.response.js'

@Injectable()
export class SapGetGuidanceLetterIndexUseCase {
  constructor (
    @Inject(SAP_CLIENT) private client: AxiosInstance
  ) {}

  async execute (
    query: SapQuery<SapGetGuidanceLetterIndexResponse>
  ): Promise<SapGetGuidanceLetterIndexResponse[]> {
    const url = query.buildUrl(SAP_ENDPOINTS.GUIDANCE_LETTER.INDEX)

    const response = await this.client.get<SapDResultResponse<SapGetGuidanceLetterIndexResponse[]>>(
      url
    )

    return response.data.d.results
  }
}
