import { SapDraftInvoiceCategory, SapDraftInvoiceStatus } from '../../types/invoice.type.js'

export interface SapGetDraftInvoiceIndexResponse {
  __metadata: {
    id: string
    uri: string
    type: 'ZCZ_SRV.listDraftInvoice'
  }
  Vbeln?: string
  Custtoberel?: boolean
  Fkart?: string
  Custapprove?: boolean
  Fktyp?: string
  Custreject?: boolean
  Vbtyp?: string
  Salestoberel?: boolean
  Vkorg?: string
  Salesapprove?: boolean
  Vtweg?: string
  Salesreject?: boolean
  Fkdat?: string
  Zterm?: string
  Kunag?: string
  Kunrg?: string
  Stceg?: string
  Mwsbk?: string
  Netwr?: string
  Waerk?: string
  Katr6?: SapDraftInvoiceCategory
  Status1?: string
  St1Ernam?: string
  St1Erdat?: string
  Status2?: SapDraftInvoiceStatus
  St2Ernam?: string | null
  St2Erdat?: string | null
  Status3?: SapDraftInvoiceStatus
  St3Ernam?: string
  St3Erdat?: string
  Status4?: string | null
  St4Ernam?: string | null
  St4Erdat?: string | null
  St5Mail?: string
  St5Date?: string | null
  St6Mail?: string
  St6Date?: string | null
  St7Mail?: string
  St7Date?: string | null
  AdrnrRg?: string
  Name1Rg?: string
  StreetRg?: string
  PostCode1Rg?: string
  City1Rg?: string
}
