import { rand<PERSON>ity, randCompanyName, randFullAddress, randNumber, randProductMaterial, randSentence, randStreetName, randZipCode } from '@ngneat/falso'
import { SAP_ENDPOINTS } from '../../../constants/endpoint.constants.js'
import { SapGetContractLineIndexResponse } from '../../../use-cases/get-contract-line-index/get-contract-line-index.response.js'

export class GetContractLineIndexResponseBuilder {
  private response: SapGetContractLineIndexResponse

  constructor () {
    this.reset()
  }

  reset (): this {
    this.response = {
      __metadata: {
        id: `https://services.odata.org/OData${SAP_ENDPOINTS.CONTRACT_LINE.INDEX}`,
        uri: `https://services.odata.org/OData${SAP_ENDPOINTS.CONTRACT_LINE.INDEX}`,
        type: 'ZCZ_SRV.containerTypes'
      },
      MaterialDescription: randProductMaterial(),
      Vbeln: randNumber({ min: 10000000, max: 99999999 }).toString(),
      MaterialType: 'YWAS',
      Posnr: randNumber({ min: 1, max: 100 }).toString().padStart(6, '0'),
      PackagingIndicator: '',
      Kunnr: randNumber({ min: 10000000, max: 99999999 }).toString(),
      KunnrName: randCompanyName(),
      Matnr: randNumber({ min: 10000000, max: 99999999 }).toString(),
      Arktx: randSentence(),
      Kdmat: randProductMaterial(),
      Yyklantmat: randNumber({ min: 10000000, max: 99999999 }).toString(),
      Adrnr: '',
      City1: randCity(),
      PostCode1: randZipCode(),
      Street: randStreetName(),
      HouseNum1: randNumber({ min: 1, max: 1000 }).toString(),
      AdrnrName: randFullAddress(),
      KunnrY2: randNumber({ min: 10000000, max: 99999999 }).toString(),
      KunnrY2Name: randCompanyName(),
      LifnrY0: randNumber({ min: 10000000, max: 99999999 }).toString(),
      LifnrY0Name: randCompanyName(),
      LifnrY3: randNumber({ min: 10000000, max: 99999999 }).toString(),
      LifnrY3Name: randCompanyName(),
      LifnrYe: '',
      LifnrYeName: '',
      LifnrY3End: '',
      LifnrY3EndName: '',
      KunnrWe: randNumber({ min: 10000000, max: 99999999 }).toString(),
      KunnrWeName: randCompanyName(),
      Name: '',
      Flag: '',
      Treatment: '',
      Langu: 'NL',
      Header: '',
      OrderNumber: randNumber({ min: 1000, max: 9999 }).toString(),
      Yyasn: '',
      Pstyv: ['YCPO', 'YCWA', 'YCWF'][Math.floor(Math.random() * 3)],
      Yvtfsflag: '',
      KunnrY2DisplayName: `${randCompanyName()} - ${randStreetName()} ${randNumber({ min: 1, max: 100 })} - ${randZipCode()} ${randCity()}`,
      KunnrWeDisplayName: `${randCompanyName()} - ${randStreetName()} ${randNumber({ min: 1, max: 100 })} - ${randZipCode()} ${randCity()}`,
      Cuobj: randNumber({ min: 0, max: 1000 }).toString().padStart(20, '0'),
      MtposMara: '',
      KunnrDisplayName: `${randCompanyName()} - ${randStreetName()} ${randNumber({ min: 1, max: 100 })} - ${randZipCode()} ${randCity()}`,
      Repetition: '00',
      Dangerous: '',
      GeneralCrit: '',
      GenCritPresent: '',
      Ybpflow: '',
      ActionNr: undefined,
      Y004: '',
      Vkorg: randNumber({ min: 1000, max: 9999 }).toString(),
      Eural: '',
      YeDisplayName: '',
      Dgkey: '',
      YeLifnr: '',
      Remarks: '',
      DeliveryInfo: '',
      Installation: randNumber({ min: 1000, max: 9999 }).toString(),
      Wtx: '',
      Erdat: '/Date(1738281600000)/'
    }

    return this
  }

  withKunnr (kunnr: string): this {
    this.response.Kunnr = kunnr

    return this
  }

  withKunnrWe (kunnrWe: string): this {
    this.response.KunnrWe = kunnrWe

    return this
  }

  withVbeln (vbeln: string): this {
    this.response.Vbeln = vbeln

    return this
  }

  withPosnr (posnr: string): this {
    this.response.Posnr = posnr

    return this
  }

  withActionNr (actionNr: string): this {
    this.response.ActionNr = actionNr

    return this
  }

  build (): SapGetContractLineIndexResponse {
    const result = this.response

    this.reset()

    return result
  }
}
