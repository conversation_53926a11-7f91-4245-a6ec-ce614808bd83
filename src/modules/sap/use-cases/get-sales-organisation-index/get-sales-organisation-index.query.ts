import { ApiProperty } from '@nestjs/swagger'
import { SearchQuery } from '@wisemen/pagination'
import { IsOptional, IsString, IsNotEmpty } from 'class-validator'

export class GetSalesOrganisationIndexQuery extends SearchQuery {
  @ApiProperty({ type: String, required: false, description: 'Search sales organisations by name or country' })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  search?: string
}
