import { Module } from '@nestjs/common'
import { SapClientProvider } from '../../../sap/sap-client.provider.js'
import { RedisModule } from '../../../redis/redis.module.js'
import { SapCacheService } from '../../../sap/services/sap-cache.service.js'
import { GetSalesOrganisationIndexController } from './get-sales-organisation-index.controller.js'
import { SapGetSalesOrganisationIndexUseCase } from './get-sales-organisation-index.use-case.js'

@Module({
  imports: [
    RedisModule
  ],
  controllers: [
    GetSalesOrganisationIndexController
  ],
  providers: [
    SapClientProvider,
    SapGetSalesOrganisationIndexUseCase,
    SapCacheService
  ],
  exports: [
    SapGetSalesOrganisationIndexUseCase
  ]
})
export class SapGetSalesOrganisationIndexModule { }
