import { Modu<PERSON> } from '@nestjs/common'
import { SapClientProvider } from '../../sap-client.provider.js'
import { RedisModule } from '../../../redis/redis.module.js'
import { SapCacheService } from '../../services/sap-cache.service.js'
import { GetSalesOrganisationIndexController } from './get-sales-organisation-index.controller.js'
import { SapGetSalesOrganisationIndexUseCase } from './get-sales-organisation-index.use-case.js'

@Module({
  imports: [
    RedisModule
  ],
  controllers: [
    GetSalesOrganisationIndexController
  ],
  providers: [
    SapClientProvider,
    SapGetSalesOrganisationIndexUseCase,
    SapCacheService
  ],
  exports: [
    SapGetSalesOrganisationIndexUseCase
  ]
})
export class SapGetSalesOrganisationIndexModule { }
