import { Controller, Get } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { SapQuery } from '../../query/sap-query.js'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { SapGetSalesOrganisationIndexUseCase } from './get-sales-organisation-index.use-case.js'
import { SapGetSalesOrganisationIndexResponse } from './get-sales-organisation-index.response.js'

@ApiTags('Sales Organisation')
@ApiOAuth2([])
@Controller('sales-organisations')
export class SalesOrganisationController {
  constructor (
    private readonly sapGetSalesOrganisationIndexUseCase: SapGetSalesOrganisationIndexUseCase
  ) { }

  @Get()
  @Permissions(
    Permission.ALL_PERMISSIONS
  )
  @Query('search') search?: string

  @ApiOkResponse({ description: 'Sales organisations retrieved successfully' })
  async getSalesOrganisations (): Promise<SapGetSalesOrganisationIndexResponse[]> {
    const query = new SapQuery<SapGetSalesOrganisationIndexResponse>()
    if (search) {
      query.where('SalesOrganization', search)
    }
    return await this.sapGetSalesOrganisationIndexUseCase.execute(query)
  }
}
