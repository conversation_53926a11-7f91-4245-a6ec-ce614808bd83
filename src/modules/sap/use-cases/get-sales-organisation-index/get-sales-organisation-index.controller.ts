import { Controller, Get, Query } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { SapGetSalesOrganisationIndexUseCase } from './get-sales-organisation-index.use-case.js'
import { SapGetSalesOrganisationIndexResponse } from './get-sales-organisation-index.response.js'
import { GetSalesOrganisationIndexQuery } from './get-sales-organisation-index.query.js'

@ApiTags('Sales Organisation')
@ApiOAuth2([])
@Controller('sales-organisations')
export class GetSalesOrganisationIndexController {
  constructor(
    private readonly sapGetSalesOrganisationIndexUseCase: SapGetSalesOrganisationIndexUseCase
  ) { }

  @Get()
  @Permissions(
    Permission.NEWS_ITEM_MANAGE
  )
  @ApiOkResponse({
    description: 'Sales organisations retrieved successfully'
  })
  async getSalesOrganisations(
    @Query() query: GetSalesOrganisationIndexQuery
  ): Promise<SapGetSalesOrganisationIndexResponse[]> {
    const sapQuery = new SapQuery<SapGetSalesOrganisationIndexResponse>(query)
    return await this.sapGetSalesOrganisationIndexUseCase.execute(sapQuery)
  }
}
