import { Inject, Injectable } from '@nestjs/common'
import { AxiosInstance } from 'axios'
import { SAP_ENDPOINTS } from '../../constants/endpoint.constants.js'
import { SapQuery } from '../../query/sap-query.js'
import { SAP_CLIENT } from '../../sap-client.provider.js'
import { SapValueResponse } from '../../shared/sap-value.response.js'
import { SapCall } from '../../enums/sap-call.enum.js'
import { SapCacheService } from '../../services/sap-cache.service.js'
import { SapGetSalesOrganisationIndexResponse } from './get-sales-organisation-index.response.js'

@Injectable()
export class SapGetSalesOrganisationIndexUseCase {
  constructor (
    @Inject(SAP_CLIENT) private client: AxiosInstance,
    private readonly sapCacheService: SapCacheService
  ) {}

  async execute (
    query: Sap<PERSON>uery<SapGetSalesOrganisationIndexResponse>
  ): Promise<SapGetSalesOrganisationIndexResponse[]> {
    const cachedResponse = await this.sapCacheService.getResponse<
      SapGetSalesOrganisationIndexResponse[]
    >(
      SapCall.VIEW_SALES_ORGANISATION_INDEX,
      query.getUniqueKey()
    )

    if (cachedResponse !== null) return cachedResponse

    const url = query.buildUrl(SAP_ENDPOINTS.SALES_ORGANISATION.INDEX)

    const response = await this.client.get<
      SapValueResponse<SapGetSalesOrganisationIndexResponse[]>
    >(
      url
    )

    await this.sapCacheService.putResponse(
      response.data.value,
      SapCall.VIEW_SALES_ORGANISATION_INDEX,
      query.getUniqueKey()
    )

    return response.data.value
  }
}
