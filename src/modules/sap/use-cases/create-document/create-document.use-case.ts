import { Inject, Injectable } from '@nestjs/common'
import { AxiosInstance } from 'axios'
import { SAP_CLIENT } from '../../sap-client.provider.js'
import { SAP_ENDPOINTS } from '../../constants/endpoint.constants.js'
import { CreateDocumentOptions } from './create-document-options.type.js'

@Injectable()
export class SapCreateDocumentUseCase {
  constructor (
    @Inject(SAP_CLIENT) private client: AxiosInstance
  ) {}

  async execute (
    options: CreateDocumentOptions
  ): Promise<void> {
    const url = SAP_ENDPOINTS.DOCUMENT.CREATE

    const body = this.createBody(
      options
    )

    await this.client.post(
      url,
      body,
      {
        headers: {
          'content-type': `multipart/mixed; boundary=batch_${options.fileUuid}`
        }
      }
    )
  }

  private createBody (
    options: CreateDocumentOptions
  ): string {
    const {
      fileUuid,
      requestNumber,
      arObject,
      arcDocId,
      archivId,
      fileName,
      timeStamp,
      mimeType
    } = options

    return `--batch_${fileUuid}
        Content-Type: multipart/mixed; boundary=changeset_${fileUuid}

        --changeset_${fileUuid}
        Content-Type: application/http
        Content-Transfer-Encoding: binary

        POST ArchiveLinkAttributeSet HTTP/1.1
        Content-Type: application/json

        {"SapObject":"BUS2030","ObjectId":"${requestNumber}","ArObject":"${arObject}","ArcDocId":"${arcDocId}","ArchivId":"${archivId}","FieldName":"DESCRIPTION","FieldLabel":"Description","FieldValue":"Upload from Customer Zone"}

        --changeset_${fileUuid}
        Content-Type: application/http
        Content-Transfer-Encoding: binary

        POST ArchiveLinkAttributeSet HTTP/1.1
        Content-Type: application/json

        {"SapObject":"BUS2030","ObjectId":"${requestNumber}","ArObject":"${arObject}","ArcDocId":"${arcDocId}","ArchivId":"${archivId}","FieldName":"FILENAME","FieldLabel":"Filename","FieldValue":"${fileName}"}

        --changeset_${fileUuid}
        Content-Type: application/http
        Content-Transfer-Encoding: binary

        POST ArchiveLinkAttributeSet HTTP/1.1
        Content-Type: application/json

        {"SapObject":"BUS2030","ObjectId":"${requestNumber}","ArObject":"${arObject}","ArcDocId":"${arcDocId}","ArchivId":"${archivId}","FieldName":"TIMESTAMP","FieldLabel":"Time","FieldValue":"${timeStamp}"}

        --changeset_${fileUuid}
        Content-Type: application/http
        Content-Transfer-Encoding: binary

        POST ArchiveLinkAttributeSet HTTP/1.1
        Content-Type: application/json

        {"SapObject":"BUS2030","ObjectId":"${requestNumber}","ArObject":"${arObject}","ArcDocId":"${arcDocId}","ArchivId":"${archivId}","FieldName":"MimeType$MimeType","FieldLabel":"MimeType","FieldValue":"${mimeType}"}

        --changeset_${fileUuid}--

        --batch_${fileUuid}--`
  }
}
