import { Module } from '@nestjs/common'
import { FileModule } from '../../../files/file.module.js'
import { SapUploadDocumentModule } from '../upload-document/upload-document.module.js'
import { SapCreateDocumentModule } from '../create-document/create-document.module.js'
import { SapUploadAndCreateDocumentUseCase } from './upload-and-create-document.use-case.js'

@Module({
  imports: [
    FileModule,
    SapUploadDocumentModule,
    SapCreateDocumentModule
  ],
  providers: [
    SapUploadAndCreateDocumentUseCase
  ],
  exports: [
    SapUploadAndCreateDocumentUseCase
  ]
})
export class SapUploadAndCreateDocumentModule {}
