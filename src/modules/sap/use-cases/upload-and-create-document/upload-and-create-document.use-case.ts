import assert from 'assert'
import { Injectable } from '@nestjs/common'
import dayjs from 'dayjs'
import { SapUploadDocumentUseCase } from '../upload-document/upload-document.use-case.js'
import { SapCreateDocumentUseCase } from '../create-document/create-document.use-case.js'
import { File } from '../../../files/entities/file.entity.js'
import { AzureBlobService } from '../../../files/services/azure-blob.service.js'
import { EntityPart } from '../../../files/enums/entity-part.enum.js'
import { SapEntityPartMapper } from '../../../files/mappers/sap-entity-part-mapper.js'

@Injectable()
export class SapUploadAndCreateDocumentUseCase {
  constructor (
    private readonly azureBlobService: AzureBlobService,
    private readonly uploadDocumentUseCase: SapUploadDocumentUseCase,
    private readonly createDocumentUseCase: SapCreateDocumentUseCase
  ) {}

  async execute (
    requestNumber: string,
    file: File,
    entityPart: EntityPart
  ): Promise<void> {
    assert(file.mimeType !== null)

    const fileBuffer = await this.azureBlobService.downloadFileBuffer(file)

    const uploadResponse = (await this.uploadDocumentUseCase.execute(
      fileBuffer,
      file.mimeType,
      requestNumber,
      SapEntityPartMapper.toSap(entityPart)
    )).d

    await this.createDocumentUseCase.execute({
      fileUuid: file.uuid,
      requestNumber,
      arObject: uploadResponse.ArObject,
      arcDocId: uploadResponse.ArcDocId,
      archivId: uploadResponse.ArchivId,
      mimeType: uploadResponse.Mimetype,
      fileName: file.name,
      timeStamp: dayjs().toString()
    })
  }
}
