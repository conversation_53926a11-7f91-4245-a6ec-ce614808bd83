import { describe, it } from 'node:test'
import { FilterQuery, PaginatedKeysetQuery, PaginatedKeysetSearchQuery, SearchQuery, SortDirection, SortQuery } from '@wisemen/pagination'
import { expect } from 'expect'
import { SapQuery } from '../sap-query.js'
import { FilterOperator } from '../../enums/odata-filter-operator.enum.js'

export class TestPaginatedKeysetSearchQuery extends PaginatedKeysetSearchQuery {
  pagination?: PaginatedKeysetQuery | undefined
  sort?: SortQuery[] | undefined
  filter?: FilterQuery | undefined
  search?: string | undefined
}

describe('SAP query unit test', () => {
  describe('Creating query from search query', () => {
    it('Should create query from SearchQuery', () => {
      const searchQuery: SearchQuery = {
        sort: [{
          key: 'field1',
          order: SortDirection.ASC
        }, {
          key: 'field2',
          order: SortDirection.DESC
        }]
      }

      const sorts = {
        keyMapper: (key: string) => key as keyof { field1: string, field2: string }
      }

      const query = new SapQuery(
        searchQuery,
        sorts
      )

      expect(query).toMatchObject({
        _orderBy: [
          { column: 'field1', direction: 'asc' },
          { column: 'field2', direction: 'desc' }
        ]
      })
    })

    it('Should create query from SearchQuery', () => {
      const searchQuery = new TestPaginatedKeysetSearchQuery()
      searchQuery.sort = [{
        key: 'field1',
        order: SortDirection.ASC
      }, {
        key: 'field2',
        order: SortDirection.DESC
      }]
      searchQuery.pagination = {
        limit: 10,
        key: 'someKey'
      }

      const sorts = {
        keyMapper: (key: string) => key as keyof { field1: string, field2: string }
      }

      const query = new SapQuery(
        searchQuery,
        sorts
      )

      expect(query).toMatchObject({
        _orderBy: [
          { column: 'field1', direction: 'asc' },
          { column: 'field2', direction: 'desc' }
        ],
        _top: 10,
        _skipToken: 'someKey'
      })
    })
  })

  describe('Create query using the builder methods', () => {
    it('Should add select fields', () => {
      const query = new SapQuery<{
        field1: string
        field2: string
        field3: string
      }>()
      query.addSelect('field1').addSelect(['field2', 'field3'])

      expect(query).toMatchObject({
        _select: ['field1', 'field2', 'field3']
      })
    })

    it('Should add where conditions', () => {
      const query = new SapQuery<{
        field1: string
        field2: string
        field3: string
      }>()
      query.where('field1', true)
        .andWhere('field2', 42, FilterOperator.GREATER_THAN)
        .orWhere((qb) => {
          return qb.where('field3', 'value1')
            .orWhere('field3', 'value2')
        })

      expect(query).toMatchObject({
        _filters: {
          conditions: [
            {
              expression: {
                field: 'field1',
                operator: 'eq',
                value: true
              }
            },
            {
              operator: 'and',
              expression: {
                field: 'field2',
                operator: 'gt',
                value: 42
              }
            },
            {
              operator: 'or',
              expression: {
                conditions: [
                  {
                    expression: {
                      field: 'field3',
                      operator: 'eq',
                      value: 'value1'
                    }
                  },
                  {
                    operator: 'or',
                    expression: {
                      field: 'field3',
                      operator: 'eq',
                      value: 'value2'
                    }
                  }
                ]
              }
            }
          ]
        }
      })
    })

    it('Should add order by conditions', () => {
      const query = new SapQuery<{
        field1: string
        field2: string
      }>()
      query.addOrderBy('field1', SortDirection.ASC)
        .addOrderBy('field2', SortDirection.DESC)

      expect(query).toMatchObject({
        _orderBy: [
          { column: 'field1', direction: 'asc' },
          { column: 'field2', direction: 'desc' }
        ]
      })
    })

    it('Should set top', () => {
      const query = new SapQuery<{
        field1: string
      }>()
      query.setTop(10)

      expect(query).toMatchObject({
        _top: 10
      })
    })

    it('Should set skip token', () => {
      const query = new SapQuery<{
        field1: string
      }>()
      query.setSkipToken('someKey')

      expect(query).toMatchObject({
        _skipToken: 'someKey'
      })
    })

    it('Should set expand', () => {
      const query = new SapQuery<{
        relation1: string
      }>()
      query.loadRelation('relation1')

      expect(query).toMatchObject({
        _expand: ['relation1']
      })
    })
  })

  describe('Build url from query', () => {
    it('Should build URL with select', () => {
      const query = new SapQuery<{
        field1: string
        field2: string
        field3: string
      }>()
      query.addSelect('field1').addSelect(['field2', 'field3'])

      const url = query.buildUrl('https://example.com')
      expect(url).toBe('https://example.com?$select=field1,field2,field3')
    })

    it('Should build URL with filters', () => {
      const query = new SapQuery<{
        field1: string
        field2: string
        field3: string
      }>()
      query.where('field1', true)
        .andWhere('field2', 42, FilterOperator.GREATER_THAN)
        .orWhere((qb) => {
          return qb.where('field3', 'value1')
            .orWhere('field3', 'value2')
        })

      const url = query.buildUrl('https://example.com')
      expect(url).toBe('https://example.com?$filter=field1 eq true and field2 gt 42 or (field3 eq \'value1\' or field3 eq \'value2\')')
    })

    it('Should build URL with order by', () => {
      const query = new SapQuery<{
        field1: string
        field2: string
      }>()
      query.addOrderBy('field1', SortDirection.ASC)
        .addOrderBy('field2', SortDirection.DESC)

      const url = query.buildUrl('https://example.com')
      expect(url).toBe('https://example.com?$orderby=field1 asc,field2 desc')
    })

    it('Should build URL with search', () => {
      const query = new SapQuery<{
        field1: string
      }>({
        search: 'test search'
      })

      const url = query.buildUrl('https://example.com')
      expect(url).toBe('https://example.com?$search=test%20search')
    })

    it('Should build URL with top', () => {
      const query = new SapQuery<{
        field1: string
      }>()
      query.setTop(10)

      const url = query.buildUrl('https://example.com')
      expect(url).toBe('https://example.com?$top=10')
    })

    it('Should build URL with skip token', () => {
      const query = new SapQuery<{
        field1: string
      }>()
      query.setSkipToken('someKey')

      const url = query.buildUrl('https://example.com')
      expect(url).toBe('https://example.com?$skiptoken=someKey')
    })

    it('Should build URL with expand', () => {
      const query = new SapQuery<{
        relation1: string
      }>()

      query.loadRelation('relation1')

      const url = query.buildUrl('https://example.com')
      expect(url).toBe('https://example.com?$expand=relation1')
    })
  })
})
