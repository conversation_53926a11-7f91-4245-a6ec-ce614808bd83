import { FilterOperator } from '../../enums/odata-filter-operator.enum.js'
import { SapFilterCondition } from './sap-filter-condition.type.js'
import { SapFilterExpression } from './sap-filter-expression.type.js'
import { SapFilterSubstringOf } from './sap-filter-subtring-of.type..js'

export class SapFilterGroup<T> {
  private conditions: Array<{
    operator?: FilterOperator.AND | FilterOperator.OR
    expression: SapFilterExpression<T>
  }> = []

  public where (
    fieldOrBrackets: keyof T | SapFilterGroup<T> | ((qb: SapFilterGroup<T>) => SapFilterGroup<T>),
    value?: string | number | boolean | null,
    operator: Exclude<FilterOperator, FilterOperator.AND | FilterOperator.OR> = FilterOperator.EQUAL
  ): this {
    if (typeof fieldOrBrackets === 'function') {
      const subQb = new SapFilterGroup<T>()
      fieldOrBrackets(subQb)
      this.conditions.push({
        expression: subQb
      })
    } else if (fieldOrBrackets instanceof SapFilterGroup) {
      this.conditions.push({ expression: fieldOrBrackets })
    } else {
      if (value === undefined) {
        throw new Error('Value must be provided for a simple filter condition.')
      }

      if (
        operator === FilterOperator.SUBSTRING_OF
        || operator === FilterOperator.NOT_SUBSTRING_OF
      ) {
        this.conditions.push(
          {
            expression: new SapFilterSubstringOf<T>({
              field: fieldOrBrackets,
              value: String(value),
              matches: operator === FilterOperator.SUBSTRING_OF
            })
          }
        )
        return this
      } else {
        this.conditions.push({
          expression: { field: fieldOrBrackets, operator, value } as SapFilterCondition<T>
        })
      }
    }
    return this
  }

  public andWhere (
    fieldOrBrackets: keyof T | SapFilterGroup<T> | ((qb: SapFilterGroup<T>) => SapFilterGroup<T>),
    value?: string | number | boolean | null,
    operator: Exclude<FilterOperator, FilterOperator.AND | FilterOperator.OR> = FilterOperator.EQUAL
  ): this {
    if (typeof fieldOrBrackets === 'function') {
      const subQb = new SapFilterGroup<T>()
      fieldOrBrackets(subQb)
      this.conditions.push({
        expression: subQb,
        operator: FilterOperator.AND
      })
    } else if (fieldOrBrackets instanceof SapFilterGroup) {
      this.conditions.push({ operator: FilterOperator.AND, expression: fieldOrBrackets })
    } else {
      if (value === undefined) {
        throw new Error('Value must be provided for a simple filter condition.')
      }

      if (
        operator === FilterOperator.SUBSTRING_OF
        || operator === FilterOperator.NOT_SUBSTRING_OF
      ) {
        this.conditions.push(
          {
            operator: FilterOperator.AND,
            expression: new SapFilterSubstringOf<T>({
              field: fieldOrBrackets,
              value: String(value),
              matches: operator === FilterOperator.SUBSTRING_OF
            })
          }
        )
        return this
      } else {
        this.conditions.push({
          operator: FilterOperator.AND,
          expression: { field: fieldOrBrackets, operator, value } as SapFilterCondition<T>
        })
      }
    }
    return this
  }

  public orWhere (
    fieldOrBrackets: keyof T | SapFilterGroup<T> | ((qb: SapFilterGroup<T>) => SapFilterGroup<T>),
    value?: string | number | boolean | null,
    operator: Exclude<FilterOperator, FilterOperator.AND | FilterOperator.OR> = FilterOperator.EQUAL
  ): this {
    if (typeof fieldOrBrackets === 'function') {
      const subQb = new SapFilterGroup<T>()
      fieldOrBrackets(subQb)
      this.conditions.push({
        expression: subQb,
        operator: FilterOperator.OR
      })
    } else if (fieldOrBrackets instanceof SapFilterGroup) {
      this.conditions.push({ operator: FilterOperator.OR, expression: fieldOrBrackets })
    } else {
      if (value === undefined) {
        throw new Error('Value must be provided for a simple filter condition.')
      }

      if (
        operator === FilterOperator.SUBSTRING_OF
        || operator === FilterOperator.NOT_SUBSTRING_OF
      ) {
        this.conditions.push(
          {
            operator: FilterOperator.OR,
            expression: new SapFilterSubstringOf<T>({
              field: fieldOrBrackets,
              value: String(value),
              matches: operator === FilterOperator.SUBSTRING_OF
            })
          }
        )
        return this
      } else {
        this.conditions.push({
          operator: FilterOperator.OR,
          expression: { field: fieldOrBrackets, operator, value } as SapFilterCondition<T>
        })
      }
    }
    return this
  }

  public buildFilterString (): string {
    if (this.conditions.length === 0) {
      return ''
    }

    const parts: string[] = []
    this.conditions.forEach((item, index) => {
      let expressionString: string
      if (item.expression instanceof SapFilterGroup) {
        expressionString = `(${item.expression.buildFilterString()})`
      } else if (item.expression instanceof SapFilterSubstringOf) {
        expressionString = this.formatSubstringOf(item.expression)
      } else {
        expressionString = this.formatCondition(item.expression)
      }

      if (index > 0 && item.operator !== undefined) {
        parts.push(item.operator)
      }
      parts.push(expressionString)
    })

    return parts.join(' ')
  }

  private formatSubstringOf (expression: SapFilterSubstringOf<T>): string {
    const formattedValue = `'${expression.value}'`
    if (expression.matches) {
      return `substringof(${formattedValue}, ${String(expression.field)}) eq true`
    } else {
      return `substringof(${formattedValue}, ${String(expression.field)}) eq false`
    }
  }

  private formatCondition (condition: SapFilterCondition<T>): string {
    let formattedValue: string
    if (typeof condition.value === 'string') {
      if (condition.value.startsWith('datetime')) {
        formattedValue = condition.value
      } else {
        formattedValue = `'${condition.value}'`
      }
    } else if (condition.value === null) {
      formattedValue = 'null'
    } else {
      formattedValue = String(condition.value)
    }
    return `${String(condition.field)} ${condition.operator} ${formattedValue}`
  }
}
