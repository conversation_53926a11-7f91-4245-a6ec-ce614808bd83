import { Controller, Get } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { SapGetSalesOrganisationIndexUseCase } from '../use-cases/get-sales-organisation-index/get-sales-organisation-index.use-case.js'
import { SapGetSalesOrganisationIndexResponse } from '../use-cases/get-sales-organisation-index/get-sales-organisation-index.response.js'
import { SapQuery } from '../query/sap-query.js'
import { Permission } from '../../permission/permission.enum.js'
import { Permissions } from '../../permission/permission.decorator.js'

@ApiTags('Sales Organisation')
@ApiOAuth2([])
@Controller('sales-organisations')
export class SalesOrganisationController {
  constructor (
    private readonly sapGetSalesOrganisationIndexUseCase: SapGetSalesOrganisationIndexUseCase
  ) {}

  @Get()
  @Permissions(
    Permission.WASTE_INQUIRY_MANAGE,
    Permission.WASTE_INQUIRY_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ
  )
  @ApiOkResponse({ type: SapGetSalesOrganisationIndexResponse, isArray: true })
  async getSalesOrganisations (): Promise<SapGetSalesOrganisationIndexResponse[]> {
    const query = new SapQuery<SapGetSalesOrganisationIndexResponse>()
    return await this.sapGetSalesOrganisationIndexUseCase.execute(query)
  }
}
