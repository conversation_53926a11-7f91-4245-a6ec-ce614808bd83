import { Injectable } from '@nestjs/common'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { SapFilterGroup } from '../../../sap/query/types/sap-filter-group.js'
import { SapGetDraftInvoiceIndexUseCase } from '../../../sap/use-cases/get-draft-invoice-index/get-draft-invoice-index.use-case.js'
import { DraftInvoiceMapper } from '../../mappers/draft-invoice.mapper.js'
import { DraftInvoice } from '../../types/draft-invoice-type.js'
import { SapGetDraftInvoiceIndexResponse } from '../../../sap/use-cases/get-draft-invoice-index/get-draft-invoice-index.response.js'
import { DraftInvoiceStatus } from '../../enums/draft-invoice-status.enum.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewDraftInvoiceIndexQuery } from './queries/view-draft-invoice-index.query.js'
import { ViewDraftInvoiceIndexResponse } from './view-draft-invoice-index.response.js'
import { ViewDraftInvoiceIndexValidator } from './view-draft-invoice-index.validator.js'

@Injectable()
export class ViewDraftInvoiceIndexUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly validator: ViewDraftInvoiceIndexValidator,
    private readonly sapGetDraftInvoiceIndexUseCase: SapGetDraftInvoiceIndexUseCase
  ) { }

  async execute (
    query: ViewDraftInvoiceIndexQuery
  ): Promise<ViewDraftInvoiceIndexResponse> {
    this.validator.validate(query.filter)

    const invoices = await this.getInvoices(query)

    return new ViewDraftInvoiceIndexResponse(invoices, null)
  }

  private async getInvoices (
    query: ViewDraftInvoiceIndexQuery
  ): Promise<DraftInvoice[]> {
    const sapQuery = this.getInvoiceQuery(query)
    const responses = await this.sapGetDraftInvoiceIndexUseCase.execute(sapQuery)

    return DraftInvoiceMapper.fromSapResponses(responses)
  }

  private getInvoiceQuery (
    query: ViewDraftInvoiceIndexQuery
  ): SapQuery<SapGetDraftInvoiceIndexResponse> {
    const sapQuery = new SapQuery<SapGetDraftInvoiceIndexResponse>(query)
      .addOrderBy('Vbeln')

    this.applyStatusFilter(query, sapQuery)
    this.applyCustomerFilter(sapQuery)

    return sapQuery
  }

  private applyCustomerFilter (
    sapQuery: SapQuery<SapGetDraftInvoiceIndexResponse>
  ): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()

    if (selectedCustomerId != null) {
      sapQuery.andWhere('Kunrg', selectedCustomerId)
    }
  }

  private applyStatusFilter (
    query: ViewDraftInvoiceIndexQuery,
    sapQuery: SapQuery<SapGetDraftInvoiceIndexResponse>
  ): void {
    if (
      query.filter.statuses !== undefined
      && query.filter.statuses.length > 0
    ) {
      sapQuery.where((qb) => {
        for (const [index, status] of query.filter.statuses.entries()) {
          if (index === 0) {
            qb.where(qb1 => this.addStatusQuery(status, qb1))
          } else {
            qb.orWhere(qb1 => this.addStatusQuery(status, qb1))
          }
        }

        return qb
      })
    }
  }

  private addStatusQuery (
    status: DraftInvoiceStatus,
    filterGroup: SapFilterGroup<SapGetDraftInvoiceIndexResponse>
  ): SapFilterGroup<SapGetDraftInvoiceIndexResponse> {
    switch (status) {
      case DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER:
        return filterGroup.where('Salestoberel', true)

      case DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER:
        return filterGroup.where('Custtoberel', true)

      case DraftInvoiceStatus.APPROVED_BY_CUSTOMER:
        return filterGroup.where('Custapprove', true)

      case DraftInvoiceStatus.REJECTED_BY_CUSTOMER:
        return filterGroup.where('Custreject', true)

      case DraftInvoiceStatus.REJECTED_BY_INDAVER:
        return filterGroup.where('Salesreject', true)

      case DraftInvoiceStatus.INTERNAL_APPROVED:
        return filterGroup.where('Salesapprove', true)

      case DraftInvoiceStatus.AUTO_APPROVED:
        return filterGroup.where('Status3', '4')

      default:
        exhaustiveCheck(status)
    }
  }
}
