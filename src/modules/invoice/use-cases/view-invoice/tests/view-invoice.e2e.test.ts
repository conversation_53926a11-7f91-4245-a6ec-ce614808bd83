import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { randNumber } from '@ngneat/falso'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'

describe('View invoice e2e test', () => {
  let setup: EndToEndTestSetup

  let unauthorizedUser: TestUser
  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    const [_unauthorizedUser, _authorizedUser] = await Promise.all([
      await setup.authContext.getUser([]),
      await setup.authContext.getUser([Permission.INVOICE_MANAGE])
    ])

    unauthorizedUser = _unauthorizedUser
    authorizedUser = _authorizedUser
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const requestNumber = randNumber({ min: 1000000, max: 9999999 }).toString()

    const response = await request(setup.httpServer)
      .get(`/invoices/${requestNumber}`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const requestNumber = randNumber({ min: 1000000, max: 9999999 }).toString()

    const response = await request(setup.httpServer)
      .get(`/invoices/${requestNumber}`)
      .set('Authorization', `Bearer ${unauthorizedUser.token}`)

    expect(response).toHaveStatus(403)
  })

  it('retrieves the invoice by request number', async () => {
    const requestNumber = randNumber({ min: 1000000, max: 9999999 }).toString()

    const response = await request(setup.httpServer)
      .get(`/invoices/${requestNumber}`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)

    expect(response).toHaveStatus(200)
  })
})
