import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'node:crypto'
import Sinon, { assert, SinonStubbedInstance, createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewInvoiceDetailUseCase } from '../view-invoice.use-case.js'
import { SapGetInvoiceIndexUseCase } from '../../../../sap/use-cases/get-invoice-index/get-invoice-index.use-case.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { SapGetInvoiceIndexResponseBuilder } from '../../../../sap/use-cases/get-invoice-index/tests/builders/get-invoice-index.response.builder.js'
import { InvoiceNotFoundError } from '../../../errors/invoice-not-found.error.js'

describe('View Invoice use-case unit test', () => {
  let useCase: ViewInvoiceDetailUseCase

  let sapGetInvoiceIndexUseCase: SinonStubbedInstance<SapGetInvoiceIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    sapGetInvoiceIndexUseCase = createStubInstance(SapGetInvoiceIndexUseCase)

    const authContext = createStubInstance(AuthContext, {
      getSelectedCustomerId: randomUUID()
    })

    useCase = new ViewInvoiceDetailUseCase(
      authContext,
      sapGetInvoiceIndexUseCase
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    Sinon.resetHistory()

    sapGetInvoiceIndexUseCase.execute.resolves()
  }

  it('Throws an error when invoice not found', async () => {
    sapGetInvoiceIndexUseCase.execute.resolves([])
    await expect(useCase.execute(randomUUID())).rejects.toThrow(InvoiceNotFoundError)
  })

  it('Calls all methods', async () => {
    sapGetInvoiceIndexUseCase.execute.resolves([
      new SapGetInvoiceIndexResponseBuilder().build()
    ])

    await useCase.execute(randomUUID())

    assert.calledOnce(sapGetInvoiceIndexUseCase.execute)
  })
})
