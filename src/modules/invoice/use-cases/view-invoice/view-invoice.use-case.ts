import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { SapGetInvoiceIndexUseCase } from '../../../sap/use-cases/get-invoice-index/get-invoice-index.use-case.js'
import { SapGetInvoiceIndexResponse } from '../../../sap/use-cases/get-invoice-index/get-invoice-index.response.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { InvoiceMapper } from '../../mappers/invoice.mapper.js'
import { InvoiceNotFoundError } from '../../errors/invoice-not-found.error.js'
import { ViewInvoiceResponse } from './view-invoice.response.js'

@Injectable()
export class ViewInvoiceDetailUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly sapGetInvoiceIndexUseCase: SapGetInvoiceIndexUseCase
  ) {}

  async execute (
    requestNumber: string
  ): Promise<ViewInvoiceResponse> {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()

    const sapQuery = new SapQuery<SapGetInvoiceIndexResponse>()
      .where('Vbeln', requestNumber)
      .setTop(1)

    if (selectedCustomerId !== null) {
      sapQuery.andWhere('Kunrg', selectedCustomerId)
    }

    const results = await this.sapGetInvoiceIndexUseCase.execute(sapQuery)

    if (results.length === 0) {
      throw new InvoiceNotFoundError({ invoiceNumber: requestNumber })
    }

    // TODO: - IND-892: Add view pdf

    const invoice = InvoiceMapper.fromSapResponse(results[0])

    return new ViewInvoiceResponse(invoice)
  }
}
