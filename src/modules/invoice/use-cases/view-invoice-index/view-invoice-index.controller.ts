import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOkResponse, ApiOAuth2 } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewInvoiceIndexUseCase } from './view-invoice-index.use-case.js'
import { ViewInvoiceIndexQuery } from './queries/view-invoice-index.query.js'
import { ViewInvoiceIndexResponse } from './view-invoice-index.response.js'

@ApiTags('Invoice')
@ApiOAuth2([])
@Controller('invoices')
export class ViewInvoiceIndexController {
  constructor (
    private readonly useCase: ViewInvoiceIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.INVOICE_READ, Permission.INVOICE_MANAGE)
  @ApiOkResponse({ type: ViewInvoiceIndexResponse })
  public async viewInvoiceIndex (
    @Query() query: ViewInvoiceIndexQuery
  ): Promise<ViewInvoiceIndexResponse> {
    return await this.useCase.execute(query)
  }
}
