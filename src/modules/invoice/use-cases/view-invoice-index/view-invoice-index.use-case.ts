import { Injectable } from '@nestjs/common'
import { SapGetInvoiceIndexUseCase } from '../../../sap/use-cases/get-invoice-index/get-invoice-index.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetInvoiceIndexResponse } from '../../../sap/use-cases/get-invoice-index/get-invoice-index.response.js'
import { InvoiceStatus } from '../../enums/invoice-status.enum.js'
import { Invoice } from '../../types/invoice.type.js'
import { InvoiceMapper } from '../../mappers/invoice.mapper.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { SapFilterGroup } from '../../../sap/query/types/sap-filter-group.js'
import { FilterOperator } from '../../../sap/enums/odata-filter-operator.enum.js'
import { SapDateFormatterService } from '../../../sap/services/sap-date-formatter.service.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewInvoiceIndexQuery } from './queries/view-invoice-index.query.js'
import { ViewInvoiceIndexResponse } from './view-invoice-index.response.js'

@Injectable()
export class ViewInvoiceIndexUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly sapGetInvoiceIndexUseCase: SapGetInvoiceIndexUseCase
  ) { }

  async execute (
    query: ViewInvoiceIndexQuery
  ): Promise<ViewInvoiceIndexResponse> {
    const invoices = await this.getInvoices(query)

    return new ViewInvoiceIndexResponse(invoices, null)
  }

  private async getInvoices (
    query: ViewInvoiceIndexQuery
  ): Promise<Invoice[]> {
    const sapQuery = this.getInvoiceQuery(query)
    const results = await this.sapGetInvoiceIndexUseCase.execute(sapQuery)

    return InvoiceMapper.fromSapResponses(results)
  }

  private getInvoiceQuery (
    query: ViewInvoiceIndexQuery
  ): SapQuery<SapGetInvoiceIndexResponse> {
    const sapQuery = new SapQuery<SapGetInvoiceIndexResponse>(query)
      .addOrderBy('Vbeln')

    this.applyStatusFilter(query, sapQuery)
    this.applyCustomerFilter(sapQuery)

    return sapQuery
  }

  private applyStatusFilter (
    query: ViewInvoiceIndexQuery,
    sapQuery: SapQuery<SapGetInvoiceIndexResponse>
  ): void {
    if (
      query.filter.statuses !== undefined
      && query.filter.statuses.length > 0
    ) {
      sapQuery.where((qb) => {
        for (const [index, status] of query.filter.statuses.entries()) {
          if (index === 0) {
            qb.where(qb1 => this.addStatusQuery(status, qb1))
          } else {
            qb.orWhere(qb1 => this.addStatusQuery(status, qb1))
          }
        }

        return qb
      })
    }
  }

  private applyCustomerFilter (
    sapQuery: SapQuery<SapGetInvoiceIndexResponse>
  ): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()

    if (selectedCustomerId != null) {
      sapQuery.andWhere('Kunrg', selectedCustomerId)
    }
  }

  private addStatusQuery (
    status: InvoiceStatus,
    filterGroup: SapFilterGroup<SapGetInvoiceIndexResponse>
  ): SapFilterGroup<SapGetInvoiceIndexResponse> {
    switch (status) {
      case InvoiceStatus.CLEARED:
        return filterGroup.where('Augbl', '', FilterOperator.NOT_EQUAL)

      case InvoiceStatus.OUTSTANDING:
        return filterGroup
          .where('Augbl', '')
          .andWhere('Duedate', SapDateFormatterService.toSapFilterDate(), FilterOperator.GREATER_THAN)

      case InvoiceStatus.OVERDUE:
        return filterGroup
          .where('Augbl', '')
          .andWhere('Duedate', SapDateFormatterService.toSapFilterDate(), FilterOperator.LESS_THAN_OR_EQUAL)
          .andWhere('Duedate', null, FilterOperator.NOT_EQUAL)
      default:
        exhaustiveCheck(status)
    }
  }
}
