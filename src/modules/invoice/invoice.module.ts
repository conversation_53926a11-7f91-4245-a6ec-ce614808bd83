import { Module } from '@nestjs/common'
import { ViewInvoiceIndexModule } from './use-cases/view-invoice-index/view-invoice-index.module.js'
import { ApproveDraftInvoiceModule } from './use-cases/approve-draft-invoice/approve-draft-invoice.module.js'
import { ViewDraftInvoiceIndexModule } from './use-cases/view-draft-invoice-index/view-draft-invoice-index.module.js'
import { RejectDraftInvoiceModule } from './use-cases/reject-draft-invoice/reject-draft-invoice.module.js'
import { ViewInvoiceModule } from './use-cases/view-invoice/view-invoice.module.js'

@Module({
  imports: [
    ViewInvoiceIndexModule,
    ViewInvoiceModule,
    ViewDraftInvoiceIndexModule,
    ApproveDraftInvoiceModule,
    RejectDraftInvoiceModule
  ]
})
export class InvoiceModule {}
