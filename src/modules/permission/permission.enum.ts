export enum Permission {
  ALL_PERMISSIONS = 'all_permissions',

  ANNOUNCEMENT_MANAGE = 'announcement.manage',

  BALANCED_SCORECARD_READ = 'balanced-scorecard.read',

  CERTIFICATE_READ = 'certificate.read',

  CONTACT_MANAGE = 'contact.manage',
  CONTACT_READ = 'contact.read',

  CONTRACT_LINE_READ = 'contract-line.read',
  CONTRACT_LINE_MANAGE = 'contract-line.manage',

  DOCUMENT_READ_MASTER_TABLE = 'document.master-table',
  DOCUMENT_READ_TFS = 'document.tfs',
  DOCUMENT_READ_QUOTATION = 'document.quotation',
  DOCUMENT_READ_MINUTES_AND_PRESENTATIONS = 'document.minutes-and-presentations',
  DOCUMENT_READ_MANUAL = 'document.manual',
  DOCUMENT_READ_BSC = 'document.bsc',
  DOCUMENT_READ_CONTRACT = 'document.contract',
  DOCUMENT_READ_TRANSPORT = 'document.transport',

  DYNAMIC_TABLE_VIEW_MANAGE = 'dynamic-table-view.manage',

  ECMR_READ = 'ecmr.read',

  EVENT_LOG_READ = 'event-log.read',

  INVOICE_READ = 'invoice.read',
  INVOICE_MANAGE = 'invoice.manage',

  JOBS_READ_INDEX = 'jobs.read.index',
  JOBS_READ_DETAIL = 'jobs.read.detail',

  NEWS_ITEM_MANAGE = 'news-item.manage',

  NEWSLETTER_SUBSCRIBE = 'newsletter.subscribe',

  PICK_UP_REQUEST_READ = 'pick-up-request.read',
  PICK_UP_REQUEST_MANAGE = 'pick-up-request.manage',

  PACKAGING_REQUEST_READ = 'packaging-request.read',
  PACKAGING_REQUEST_MANAGE = 'packaging-request.manage',

  POWER_BI_READ = 'power-bi.read',

  ROLE_READ = 'role.read',
  ROLE_MANAGE = 'role.manage',

  SEND_PUSH_NOTIFICATION = 'send_push_notification',

  TYPESENSE = 'typesense',

  USER_READ = 'user.read',
  USER_MANAGE = 'user.manage',
  USER_IMPERSONATE = 'user.impersonate',

  WASTE_INQUIRY_READ = 'waste-inquiry.read',
  WASTE_INQUIRY_MANAGE = 'waste-inquiry.manage',

  WEEKLY_PLANNING_REQUEST_READ = 'weekly-planning-request.read',
  WEEKLY_PLANNING_REQUEST_MANAGE = 'weekly-planning-request.manage',

  GUIDANCE_LETTER_READ = 'guidance-letter.read'
}
