import { Injectable } from '@nestjs/common'
import { FieldMustBeNullError } from '../../exceptions/generic/field-must-be-null.error.js'
import { AuthContext } from '../../auth/auth.context.js'
import { MissingRequiredFieldError } from '../../exceptions/generic/missing-required-field.error.js'
import { PackagingRequestMaterialValidationUtil } from '../utils/packaging-request-material-validation.util.js'
import { PackagingRequestMaterial } from '../../pick-up-request/types/packaging-request-material.type.js'
import { ContractLineValidatorService } from '../../contract-line/validators/contract-line-validator.service.js'
import { PackagingRequest } from '../types/packaging-request.type.js'
import { PickUpRequestMaterial } from '../../pick-up-request/types/pick-up-request-material.type.js'
import { UserWasteProducerAuthService } from '../../auth/services/user-waste-producer-auth.service.js'

interface ValidateMaterialsOptions {
  materials: PickUpRequestMaterial[]
  customerId: string | null
  pickUpAddressIds: string[]
}

@Injectable()
export class PackagingRequestMaterialValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly contractLineValidatorService: ContractLineValidatorService,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService
  ) {}

  private validationUtil: PackagingRequestMaterialValidationUtil

  validateUpdate (
    materials: PackagingRequestMaterial[]
  ): void {
    this.validationUtil = new PackagingRequestMaterialValidationUtil()

    for (const material of materials) {
      this.validateCostCenter(material.costCenter, false)
      this.validatePoNumber(material.poNumber, false)
    }
  }

  async validateSubmit (
    packagingRequest: PackagingRequest
  ): Promise<void> {
    this.validationUtil = new PackagingRequestMaterialValidationUtil()

    await this.validateMaterialAccess (
      {
        materials: packagingRequest.packagingRequestMaterials,
        customerId: packagingRequest.customerId,
        pickUpAddressIds: packagingRequest.pickUpAddressIds
      }
    )

    for (const material of packagingRequest.packagingRequestMaterials) {
      this.validateCostCenter(material.costCenter, true)
      this.validatePoNumber(material.poNumber, true)
    }
  }

  private validateCostCenter (
    costCenter: string | null,
    isSubmit: boolean
  ) {
    if (
      !this.validationUtil.costCenterAllowed
      && costCenter != null
    ) {
      throw new FieldMustBeNullError({ pointer: '$.costCenter' })
    }

    if (isSubmit && this.validationUtil.costCenterRequired) {
      if (costCenter === null) {
        throw new MissingRequiredFieldError({ pointer: '$.costCenter' })
      }
    }
  }

  private validatePoNumber (
    poNumber: string | null,
    isSubmit: boolean
  ) {
    if (
      !this.validationUtil.poNumberAllowed
      && poNumber != null
    ) {
      throw new FieldMustBeNullError({ pointer: '$.poNumber' })
    }

    if (isSubmit && this.validationUtil.poNumberRequired) {
      if (poNumber === null) {
        throw new MissingRequiredFieldError({ pointer: '$.poNumber' })
      }
    }
  }

  private async validateMaterialAccess (
    options: ValidateMaterialsOptions
  ): Promise<void> {
    const { materials, customerId, pickUpAddressIds } = options

    if (materials.length === 0) return

    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    let restrictedWasteProducerIds: string[] | undefined

    if (selectedCustomerId != null) {
      restrictedWasteProducerIds = await this.userWasteProducerAuthService
        .getRestrictedWasteProducerIds(
          this.authContext.getAzureEntraUpnOrFail(),
          selectedCustomerId
        )
    }

    await this.contractLineValidatorService.checkAccessContractLines(
      'packaging',
      materials.map(material => ({
        contractNumber: material.contractNumber,
        contractItem: material.contractItem,
        tcNumber: material.tcNumber
      })),
      {
        customerId: customerId ?? selectedCustomerId ?? undefined,
        wasteProducerIds: restrictedWasteProducerIds
      },
      {
        pickUpAddressIds: pickUpAddressIds
      }
    )
  }
}
