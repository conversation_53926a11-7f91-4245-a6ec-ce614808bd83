import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { CustomerPickUpAddressAuthService } from '../../../auth/services/customer-pick-up-address-auth.service.js'
import { CustomerWasteProducerAuthService } from '../../../auth/services/customer-waste-producer-auth.service.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { PackagingRequestGeneralInfoValidator } from '../packaging-request-general-info.validator.js'
import { PackagingRequest } from '../../types/packaging-request.type.js'
import { UpdatePackagingRequestCommand } from '../../use-cases/update-packaging-request/update-packaging-request.command.js'
import { CustomerNotProvidedError } from '../../../customer/errors/customer-not-provided.error.js'
import { WasteProducerNotAccessibleError } from '../../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { UpdatePackagingRequestCommandBuilder } from '../../use-cases/update-packaging-request/tests/update-packaging-request-command.builder.js'
import { PackagingRequestEntityBuilder } from '../../tests/packaging-request-entity.builder.js'
import { UpdatePackagingRequestMapper } from '../../use-cases/update-packaging-request/mappers/update-packaging-request.mapper.js'
import { DeliveryAddressNotAccessibleError } from '../../errors/delivery-address-not-accessible.error.js'

describe('Packaging request general info validator unit test', () => {
  let validator: PackagingRequestGeneralInfoValidator

  let userUuid: string
  let packagingRequest: PackagingRequest
  let command: UpdatePackagingRequestCommand

  let packagingRequestBuilder: PackagingRequestEntityBuilder
  let commandBuilder: UpdatePackagingRequestCommandBuilder

  let authContext: SinonStubbedInstance<AuthContext>
  let customerWasteProducerAuthService: SinonStubbedInstance<CustomerWasteProducerAuthService>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>
  let customerPickUpAddressAuthService: SinonStubbedInstance<CustomerPickUpAddressAuthService>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()

    packagingRequestBuilder = new PackagingRequestEntityBuilder()
    commandBuilder = new UpdatePackagingRequestCommandBuilder()

    packagingRequest = packagingRequestBuilder
      .createdByUserUuid(userUuid)
      .withIsOnlyPackagingRequest(true)
      .build()

    command = commandBuilder
      .withCustomerId(randomUUID())
      .withWasteProducerId(randomUUID())
      .withDeliveryAddressId(randomUUID())
      .build()

    authContext = createStubInstance(AuthContext)
    customerWasteProducerAuthService = createStubInstance(CustomerWasteProducerAuthService)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)
    customerPickUpAddressAuthService = createStubInstance(CustomerPickUpAddressAuthService)

    validator = new PackagingRequestGeneralInfoValidator(
      authContext,
      customerWasteProducerAuthService,
      userWasteProducerAuthService,
      customerPickUpAddressAuthService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getAzureEntraUpnOrFail.returns(userUuid)
    userWasteProducerAuthService.canUserAccessWasteProducer.resolves(true)
    customerWasteProducerAuthService.canCustomerAccessWasteProducer.resolves(true)
    customerPickUpAddressAuthService.canCustomerAccessPickUpAddresses.resolves(true)
  }

  describe('Packaging request general info validator', () => {
    describe('Waste producer', () => {
      it('throws an error when wasteProducerId is not undefined but customerId is not provided', async () => {
        command = commandBuilder.withWasteProducerId(randomUUID()).build()

        await expect(validator.validateUpdate(command, packagingRequest))
          .rejects.toThrow(CustomerNotProvidedError)
      })

      it('throws an error when waste producer is not accessible by customer', async () => {
        command = commandBuilder
          .withCustomerId(randomUUID())
          .withWasteProducerId(randomUUID())
          .build()
        customerWasteProducerAuthService.canCustomerAccessWasteProducer.resolves(false)

        const mergedPackagingRequest = UpdatePackagingRequestMapper.mapToMergedPackagingRequest(
          command,
          packagingRequest
        )

        await expect(validator.validateUpdate(command, mergedPackagingRequest))
          .rejects.toThrow(WasteProducerNotAccessibleError)
      })

      it('throws an error when waste producer is not accessible by auth user', async () => {
        command = commandBuilder
          .withCustomerId(randomUUID())
          .withWasteProducerId(randomUUID())
          .build()
        userWasteProducerAuthService.canUserAccessWasteProducer.resolves(false)

        const mergedPackagingRequest = UpdatePackagingRequestMapper.mapToMergedPackagingRequest(
          command,
          packagingRequest
        )

        await expect(validator.validateUpdate(command, mergedPackagingRequest))
          .rejects.toThrow(WasteProducerNotAccessibleError)
      })
    })

    describe('Delivery address', () => {
      it('throws an error when deliveryAddress is not undefined but customerId is not provided', async () => {
        command = commandBuilder
          .withDeliveryAddressId(randomUUID())
          .build()

        await expect(validator.validateUpdate(command, packagingRequest))
          .rejects.toThrow(CustomerNotProvidedError)
      })

      it('throws an error when deliveryAddress not accessible by customer', async () => {
        command = commandBuilder
          .withCustomerId(randomUUID())
          .withDeliveryAddressId(randomUUID())
          .build()
        customerPickUpAddressAuthService.canCustomerAccessPickUpAddresses.resolves(false)

        const mergedPackagingRequest = UpdatePackagingRequestMapper.mapToMergedPackagingRequest(
          command,
          packagingRequest
        )

        await expect(validator.validateUpdate(command, mergedPackagingRequest))
          .rejects.toThrow(DeliveryAddressNotAccessibleError)
      })
    })
  })
})
