import { randomUUID } from 'crypto'
import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { SapGetPickUpRequestDetailUseCase } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetPickUpRequestDetailResponse } from '../../../sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.response.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { MapUniformPickUpRequestDetailSapService } from '../../../pick-up-request/services/map-uniform-pick-up-request-detail-sap.service.js'
import { PickUpRequest } from '../../../pick-up-request/entities/pick-up-request.entity.js'
import { UniformDetailPickUpRequest } from '../../../pick-up-request/types/uniform-pick-up-request-detail.type.js'
import { PackagingRequestEntityBuilder } from '../../tests/packaging-request-entity.builder.js'
import { PackagingRequest } from '../../types/packaging-request.type.js'
import { CopyPackagingRequestSapResponse } from './copy-packaging-request-sap.response.js'
import { PackagingRequestSapCopiedEvent } from './packaging-request-sap-copied.event.js'
import { CopyPackagingRequestValidator } from './copy-packaging-request.validator.js'

@Injectable()
export class CopyPackagingRequestSapUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(PickUpRequest)
    private readonly repository: Repository<PickUpRequest>,
    private readonly validator: CopyPackagingRequestValidator,
    private readonly sapGetPickUpRequestDetailUseCase: SapGetPickUpRequestDetailUseCase
  ) {}

  async execute (
    requestNumber: string
  ): Promise<CopyPackagingRequestSapResponse> {
    const sapPackagingRequest = await this.getSapPackagingRequest(requestNumber)

    await this.validator.validate(requestNumber, sapPackagingRequest)

    const uniFormPickUpRequest = MapUniformPickUpRequestDetailSapService
      .mapResultToUniformPickUpRequest(sapPackagingRequest, null, [], [])

    const copy = this.copyFromUniformDetailPickUpRequest(uniFormPickUpRequest)

    await transaction(this.dataSource, async () => {
      await this.repository.insert(copy)
      await this.eventEmitter.emitOne(new PackagingRequestSapCopiedEvent(requestNumber, copy.uuid))
    })

    return new CopyPackagingRequestSapResponse(copy)
  }

  private async getSapPackagingRequest (
    requestNumber: string
  ): Promise<SapGetPickUpRequestDetailResponse[]> {
    const sapQuery = new SapQuery<SapGetPickUpRequestDetailResponse>()
      .where('Reqno', requestNumber)

    return await this.sapGetPickUpRequestDetailUseCase.execute(sapQuery)
  }

  private copyFromUniformDetailPickUpRequest (
    uniformPickUpRequest: UniformDetailPickUpRequest
  ): PackagingRequest {
    return new PackagingRequestEntityBuilder()
      .withUuid(randomUUID())
      .withCustomerId(uniformPickUpRequest.customer?.id ?? null)
      .withWasteProducerId(uniformPickUpRequest.wasteProducer?.id ?? null)
      .withIsOnlyPackagingRequest(true)
      .withCreatedByUserUuid(this.authContext.getUserUuidOrFail())
      .withDeliveryAddressId(uniformPickUpRequest.pickUpAddresses.at(0)?.id ?? null)
      .withPackagingRequestMaterials(uniformPickUpRequest.packagingRequestMaterials)

      .withIsReturnPackaging(null)
      .withIsTransportByIndaver(null)
      .withMaterials([])
      .withTotalQuantityPallets(null)
      .withRequestNumber(null)
      .withStartDate(null)
      .withEndDate(null)
      .withStartTime(null)
      .withRemarks(null)
      .withSubmittedOn(null)
      .withRequestNumber(null)
      .withTransportMode(null)
      .withPackagingRemark(null)
      .withPickUpAddressId([])
      .withSendCopyToContracts([])
      .build()
  }
}
