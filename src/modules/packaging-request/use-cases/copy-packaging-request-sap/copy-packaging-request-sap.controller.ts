import { <PERSON>, <PERSON><PERSON>, <PERSON> } from '@nestjs/common'
import { ApiT<PERSON><PERSON>, ApiOAuth2, ApiCreatedResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ApiConflictErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { PickUpRequestNotFoundError } from '../../../pick-up-request/errors/pick-up-request-sap-not-found.error.js'
import { CopyNonSubmittedPickUpRequestError } from '../../../pick-up-request/errors/copy-non-submitted-pick-up-request.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { CopyPackagingRequestSapResponse } from './copy-packaging-request-sap.response.js'
import { CopyPackagingRequestSapUseCase } from './copy-packaging-request-sap.use-case.js'

@ApiTags('Packaging request')
@ApiOAuth2([])
@Controller('packaging-requests/sap/:requestNumber/copy')
export class CopyPackagingRequestSapController {
  constructor (
    private readonly useCase: CopyPackagingRequestSapUseCase
  ) {}

  @Post()
  @GlobalCustomerRequired()
  @Permissions(Permission.PACKAGING_REQUEST_MANAGE)
  @ApiCreatedResponse({ type: CopyPackagingRequestSapResponse })
  @ApiConflictErrorResponse(CopyNonSubmittedPickUpRequestError)
  @ApiNotFoundErrorResponse(PickUpRequestNotFoundError)
  public async copyPackagingRequest (
    @Param('requestNumber') requestNumber: string
  ): Promise<CopyPackagingRequestSapResponse> {
    return await this.useCase.execute(requestNumber)
  }
}
