import { <PERSON>, Controller, Patch } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { ApiBadRequestErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { CustomerNotAccessibleError } from '../../../customer/errors/customer-not-accessible.error.js'
import { PickUpAddressNotAccessibleError } from '../../../pick-up-address/errors/pick-up-address-not-accessible.error.js'
import { WasteProducerNotAccessibleError } from '../../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { MissingRequiredFieldError } from '../../../exceptions/generic/missing-required-field.error.js'
import { FieldMustBeNullError } from '../../../exceptions/generic/field-must-be-null.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { UpdatePackagingRequestUseCase } from './update-packaging-request.use-case.js'
import { UpdatePackagingRequestResponse } from './update-packaging-request.response.js'
import { UpdatePackagingRequestCommand } from './update-packaging-request.command.js'

@ApiTags('Packaging request')
@Controller('packaging-requests/:uuid')
@ApiOAuth2([])
export class UpdatePackagingRequestController {
  constructor (
    private readonly useCase: UpdatePackagingRequestUseCase
  ) {}

  @Patch()
  @GlobalCustomerRequired()
  @Permissions(
    Permission.PACKAGING_REQUEST_MANAGE
  )
  @ApiOkResponse({ type: UpdatePackagingRequestResponse })
  @ApiNotFoundErrorResponse(NotFoundError)
  @ApiBadRequestErrorResponse(
    CustomerNotAccessibleError,
    FieldMustBeNullError,
    MissingRequiredFieldError,
    WasteProducerNotAccessibleError,
    PickUpAddressNotAccessibleError
  )
  async updatePackagingRequest (
    @UuidParam('uuid') packagingRequestUuid: string,
    @Body() command: UpdatePackagingRequestCommand
  ): Promise<UpdatePackagingRequestResponse> {
    return await this.useCase.execute(packagingRequestUuid, command)
  }
}
