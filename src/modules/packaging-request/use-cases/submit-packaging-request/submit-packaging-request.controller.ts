import { Controller, HttpC<PERSON>, Post } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { ApiBadRequestErrorResponse, ApiConflictErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { PackagingRequestAlreadySubmitted } from '../../errors/packaging-request-already-submitted.error.js'
import { UpdateOnlyPackagingRequestError } from '../../errors/update-only-packaging-request.error.js'
import { ContractLineNotAccessibleError } from '../../../contract-line/errors/contract-line-not-accessible.error.js'
import { ContractLineNotOfCustomerError } from '../../../contract-line/errors/contract-line-not-of-customer.error.js'
import { ContractLineNotOfPickUpAddressesError } from '../../../contract-line/errors/contract-line-not-of-pick-up-addresses.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { SubmitPackagingRequestUseCase } from './submit-packaging-request.use-case.js'
import { SubmitPackagingRequestResponse } from './submit-packaging-request.response.js'

@ApiTags('Packaging request')
@Controller('packaging-requests/:uuid/submit')
@ApiOAuth2([])
export class SubmitPackagingRequestController {
  constructor (
    private readonly useCase: SubmitPackagingRequestUseCase
  ) {}

  @Post()
  @GlobalCustomerRequired()
  @HttpCode(200)
  @Permissions(
    Permission.PACKAGING_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_MANAGE
  )
  @ApiOkResponse({
    description: 'Packaging request submitted',
    type: SubmitPackagingRequestResponse
  })
  @ApiNotFoundErrorResponse(NotFoundError)
  @ApiConflictErrorResponse(PackagingRequestAlreadySubmitted)
  @ApiBadRequestErrorResponse(
    ContractLineNotAccessibleError,
    ContractLineNotOfCustomerError,
    ContractLineNotOfPickUpAddressesError,
    UpdateOnlyPackagingRequestError
  )
  async submitPackagingRequest (
    @UuidParam('uuid') packagingRequestUuid: string
  ): Promise<SubmitPackagingRequestResponse> {
    return await this.useCase.execute(packagingRequestUuid)
  }
}
