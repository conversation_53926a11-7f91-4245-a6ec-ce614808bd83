import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ViewNewsIndexResponse } from './view-news-item-index.response.js'
import { ViewNewsItemIndexQuery } from './view-news-item-index.query.js'
import { ViewNewsItemIndexUseCase } from './view-news-item-index.use-case.js'

@ApiTags('News item')
@ApiOAuth2([])
@Controller('news-items')
export class ViewNewsItemIndexController {
  constructor (
    private readonly useCase: ViewNewsItemIndexUseCase
  ) { }

  @Get()
  @Permissions(Permission.NEWS_ITEM_MANAGE)
  @ApiOkResponse({ type: ViewNewsIndexResponse })
  public async viewNewsItemIndex (
    @Query() query: ViewNewsItemIndexQuery
  ): Promise<ViewNewsIndexResponse> {
    return await this.useCase.execute(query)
  }
}
