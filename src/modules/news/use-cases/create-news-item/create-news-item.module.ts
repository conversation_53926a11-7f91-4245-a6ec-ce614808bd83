import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { NewsItemTranslation } from '../../entities/news-item-translation.entity.js'
import { NewsItem } from '../../entities/news-item.entity.js'
import { File } from '../../../files/entities/file.entity.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { CreateNewsItemController } from './create-news-item.controller.js'
import { CreateNewsItemUseCase } from './create-news-item.use-case.js'
import { CreateNewsItemValidator } from './create-news-item.validator.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      NewsItem,
      NewsItemTranslation,
      File
    ]),
    DomainEventEmitterModule
  ],
  controllers: [
    CreateNewsItemController
  ],
  providers: [
    CreateNewsItemUseCase,
    CreateNewsItemValidator
  ]
})
export class CreateNewsItemModule { }
