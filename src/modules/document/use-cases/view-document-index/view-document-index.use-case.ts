import { Injectable } from '@nestjs/common'
import { GetDocumentIndexUseCae } from '../../../sharepoint/use-cases/get-document-index/get-document-index.use-case.js'
import { GetDocumentIndexCommandBuilder } from '../../../sharepoint/use-cases/get-document-index/builders/get-document-index.command.builder.js'
import { ViewDocumentIndexResponse } from './view-document-index.response.js'
import { ViewDocumentIndexQuery } from './view-document-index.query.js'
import { ViewDocumentIndexValidator } from './view-document-index.validator.js'

@Injectable()
export class ViewDocumentIndexUseCase {
  constructor (
    private readonly validator: ViewDocumentIndexValidator,
    private readonly getDocumentIndexUseCase: GetDocumentIndexUseCae
  ) {}

  async execute (
    query: ViewDocumentIndexQuery
  ): Promise<ViewDocumentIndexResponse> {
    const matchingSite = await this.validator.validateAndReturnMatchingSite(query.filter)

    const command = new GetDocumentIndexCommandBuilder()
      .withViewName(query.filter.viewName)
      .withSiteId(query.filter.customerUuid)
      .withPublishedDocumentsLibraryId(matchingSite.publishedDocumentsLibraryId)
      .withWasteProducerIds(query.filter.wasteProducerIds.map(id => Number(id)))
      .withTextSearchFilter(query.search ?? '')
      .withStatusFilter(query.filter.status ?? '')
      .withNextLink(query.pagination?.key ?? '')
      .withWebUrl(matchingSite.webUrl)
      .withYearFilter(query.filter.year ?? '')
      .withRefextFilter(query.filter.refExt ?? '')
      .withTypeTransportDocFilter(query.filter.transportType ?? '')
      .build()

    const response = await this.getDocumentIndexUseCase.execute(command)

    return new ViewDocumentIndexResponse(response)
  }
}
