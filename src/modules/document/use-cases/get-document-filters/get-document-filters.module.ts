import { Module } from '@nestjs/common'
import { SharepointModule } from '../../../sharepoint/sharepoint.module.js'
import { PermissionsGuardModule } from '../../../permission/guards/permission.guard.module.js'
import { GetDocumentFiltersController } from './get-document-filters.controller.js'
import { GetDocumentFiltersUseCase } from './get-document-filters.use-case.js'
import { GetDocumentFiltersValidator } from './get-document-filters.validator.js'

@Module({
  imports: [
    SharepointModule,
    PermissionsGuardModule
  ],
  controllers: [
    GetDocumentFiltersController
  ],
  providers: [
    GetDocumentFiltersUseCase,
    GetDocumentFiltersValidator
  ]
})
export class GetDocumentFiltersModule {}
