import { ApiProperty } from '@nestjs/swagger'
import { FilterQuery } from '@wisemen/pagination'
import { Type } from 'class-transformer'
import { ArrayMinSize, ArrayUnique, IsArray, IsEnum, IsNotEmpty, IsString, IsUUID, ValidateNested } from 'class-validator'
import { SharepointDocumentViewName, SharepointDocumentViewNameApiProperty } from '../../../sharepoint/enums/sharepoint-document-view-name.enum.js'

export class GetDocumentFiltersFilterQuery extends FilterQuery {
  @SharepointDocumentViewNameApiProperty()
  @IsEnum(SharepointDocumentViewName)
  viewName: SharepointDocumentViewName

  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  customerUuid: string

  @ApiProperty({ type: String, isArray: true })
  @IsString({ each: true })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  wasteProducerIds: string[]
}

export class GetDocumentFiltersQuery {
  @ApiProperty({ type: GetDocumentFiltersFilterQuery })
  @ValidateNested()
  @Type(() => GetDocumentFiltersFilterQuery)
  @IsNotEmpty()
  filter: GetDocumentFiltersFilterQuery
}
