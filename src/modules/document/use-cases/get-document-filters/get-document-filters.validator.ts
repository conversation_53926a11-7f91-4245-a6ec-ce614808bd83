import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { GetUserSiteIndexUseCase } from '../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.use-case.js'
import { PermissionGuardService } from '../../../permission/guards/permission.guard.service.js'
import { SharepointDocumentViewName } from '../../../sharepoint/enums/sharepoint-document-view-name.enum.js'
import { Permission } from '../../../permission/permission.enum.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { ForbiddenError } from '../../../exceptions/generic/forbidden.error.js'
import { GetUserSiteIndexResponse } from '../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.response.js'
import { GetDocumentFiltersQuery } from './get-document-filters.query.js'

@Injectable()
export class GetDocumentFiltersValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly permissionGuardService: PermissionGuardService,
    private readonly getUserSiteIndexUseCase: GetUserSiteIndexUseCase
  ) {}

  async validateAndReturnMatchingSite (
    query: GetDocumentFiltersQuery
  ): Promise<GetUserSiteIndexResponse> {
    await this.validateViewName(query.filter.viewName)
    const entraUpn = this.authContext.getAzureEntraUpnOrFail()
    const siteResponses = await this.getUserSiteIndexUseCase.execute(entraUpn)

    const matchingSite = siteResponses.find(r => r.siteId === query.filter.customerUuid)

    if (matchingSite === undefined) {
      throw new ForbiddenError()
    }

    const allowedWasteProducerIds = matchingSite.wasteProducerResource.map(wp => wp.id.toString())

    const isAllowed = query.filter.wasteProducerIds
      .every(wp => allowedWasteProducerIds.includes(wp))

    if (!isAllowed) {
      throw new ForbiddenError()
    }

    return matchingSite
  }

  private async validateViewName (viewName: SharepointDocumentViewName): Promise<void> {
    const userUuid = this.authContext.getUserUuidOrFail()

    const permissions: Permission[] = []

    switch (viewName) {
      case SharepointDocumentViewName.MASTER_TABLE:
        permissions.push(Permission.DOCUMENT_READ_MASTER_TABLE)
        break
      case SharepointDocumentViewName.TFS:
        permissions.push(Permission.DOCUMENT_READ_TFS)
        break
      case SharepointDocumentViewName.QUOTATION:
        permissions.push(Permission.DOCUMENT_READ_QUOTATION)
        break
      case SharepointDocumentViewName.MEETINGS:
        permissions.push(Permission.DOCUMENT_READ_MINUTES_AND_PRESENTATIONS)
        break
      case SharepointDocumentViewName.MANUAL:
        permissions.push(Permission.DOCUMENT_READ_MANUAL)
        break
      case SharepointDocumentViewName.BALANCED_SCORE_CARD:
        permissions.push(Permission.DOCUMENT_READ_BSC)
        break
      case SharepointDocumentViewName.CONTRACT:
        permissions.push(Permission.DOCUMENT_READ_CONTRACT)
        break
      case SharepointDocumentViewName.TRANSPORT:
        permissions.push(Permission.DOCUMENT_READ_TRANSPORT)
        return
      default:
        exhaustiveCheck(viewName)
    }

    const hasRequiredPermissions = await this.permissionGuardService.hasPermissions(
      userUuid, permissions
    )

    if (!hasRequiredPermissions) {
      throw new ForbiddenError()
    }
  }
}
