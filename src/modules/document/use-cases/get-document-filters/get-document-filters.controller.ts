import { Controller, Get, Query } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiForbiddenErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { ForbiddenError } from '../../../exceptions/generic/forbidden.error.js'
import { GetDocumentFiltersResponse } from './get-document-filters.response.js'
import { GetDocumentFiltersQuery } from './get-document-filters.query.js'
import { GetDocumentFiltersUseCase } from './get-document-filters.use-case.js'

@ApiTags('Document')
@ApiOAuth2([])
@Controller('documents/filters')
export class GetDocumentFiltersController {
  constructor (
    private readonly useCase: GetDocumentFiltersUseCase
  ) {}

  @Get()
  @Permissions(
    Permission.DOCUMENT_READ_MASTER_TABLE,
    Permission.DOCUMENT_READ_TFS,
    Permission.DOCUMENT_READ_QUOTATION,
    Permission.DOCUMENT_READ_MINUTES_AND_PRESENTATIONS,
    Permission.DOCUMENT_READ_MANUAL,
    Permission.DOCUMENT_READ_BSC,
    Permission.DOCUMENT_READ_CONTRACT,
    Permission.DOCUMENT_READ_TRANSPORT
  )
  @ApiOkResponse({ type: GetDocumentFiltersResponse })
  @ApiForbiddenErrorResponse(ForbiddenError)
  async getDocumentFilters (
      @Query() query: GetDocumentFiltersQuery
  ): Promise<GetDocumentFiltersResponse> {
    return await this.useCase.execute(query)
  }
}
