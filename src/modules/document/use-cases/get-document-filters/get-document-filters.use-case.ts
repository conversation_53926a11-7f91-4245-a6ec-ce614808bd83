import { Injectable } from '@nestjs/common'
import { GetDocumentFiltersCommandBuilder } from '../../../sharepoint/use-cases/get-document-filters/builders/get-document-filters.command.builder.js'
import { SharepointGetDocumentFiltersUseCase } from '../../../sharepoint/use-cases/get-document-filters/get-document-filters.use-case.js'
import { SharepointGetDocumentFiltersResponse } from '../../../sharepoint/use-cases/get-document-filters/get-document-filters.response.js'
import { SharepointDocumentViewName } from '../../../sharepoint/enums/sharepoint-document-view-name.enum.js'
import { GetDocumentFiltersQuery } from './get-document-filters.query.js'
import { DocumentFilterResponse, GetDocumentFiltersResponse } from './get-document-filters.response.js'
import { GetDocumentFiltersValidator } from './get-document-filters.validator.js'

@Injectable()
export class GetDocumentFiltersUseCase {
  constructor (
    private readonly validator: GetDocumentFiltersValidator,
    private readonly getDocumentFilters: SharepointGetDocumentFiltersUseCase
  ) {}

  async execute (query: GetDocumentFiltersQuery): Promise<GetDocumentFiltersResponse> {
    const matchingSite = await this.validator.validateAndReturnMatchingSite(query)

    const command = new GetDocumentFiltersCommandBuilder()
      .withViewName(query.filter.viewName)
      .withSiteId(query.filter.customerUuid)
      .withWasteProducerIds(query.filter.wasteProducerIds)
      .withWebUrl(matchingSite.webUrl)
      .withPublishedDocumentsLibraryId(matchingSite.publishedDocumentsLibraryId)
      .withTypeTFSListId(matchingSite.typeTFSListId)
      .withTypeTransportDocListId(matchingSite.typeTransportDocListId)
      .build()
    const externalResponse = await this.getDocumentFilters.execute(command)

    const filters: DocumentFilterResponse[] = this.getAllViewFilters(externalResponse)

    if (query.filter.viewName === SharepointDocumentViewName.TFS) {
      filters.push(...this.getTfsViewFilters(externalResponse))
    }

    if (query.filter.viewName === SharepointDocumentViewName.QUOTATION) {
      filters.push(...this.getTransportTypeFilters(externalResponse))
    }

    return new GetDocumentFiltersResponse(filters)
  }

  private getAllViewFilters (
    response: SharepointGetDocumentFiltersResponse
  ): DocumentFilterResponse[] {
    const yearFilters: DocumentFilterResponse = {
      filterName: 'year',
      filterValues: Object.entries(response.distinctLookupValues.yearLookupValues)
        .map(([_key, value]) => ({
          key: value,
          value
        }))
    }

    return [yearFilters]
  }

  private getTfsViewFilters (
    response: SharepointGetDocumentFiltersResponse
  ): DocumentFilterResponse[] {
    const refExtFilters: DocumentFilterResponse = {
      filterName: 'refExt',
      filterValues: Object.entries(response.distinctLookupValues.extRefLookupValues)
        .map(([_key, value]) => ({
          key: value,
          value
        }))
    }

    return [refExtFilters]
  }

  private getTransportTypeFilters (
    response: SharepointGetDocumentFiltersResponse
  ): DocumentFilterResponse[] {
    const transportTypeFilters: DocumentFilterResponse = {
      filterName: 'transportType',
      filterValues: response.dropdownValuesTransportType.map(item => ({
        key: item.key,
        value: item.value
      }))
    }
    transportTypeFilters.filterValues.filter(filterValue =>
      !response.distinctLookupValues.transportDocTypeIds.includes(filterValue.key)
    )

    return [transportTypeFilters]
  }
}
