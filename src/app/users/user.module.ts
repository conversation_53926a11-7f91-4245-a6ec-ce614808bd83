import { Module } from '@nestjs/common'

import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Role } from '../roles/entities/role.entity.js'
import { UserRole } from '../roles/entities/user-role.entity.js'
import { RedisModule } from '../../modules/redis/redis.module.js'
import { TypesenseModule } from '../../modules/typesense/typesense.module.js'
import { UserAuthService } from './services/user-auth.service.js'
import { ViewMeModule } from './use-cases/view-me/view-me.module.js'
import { ViewUserDetailModule } from './use-cases/view-user-detail/view-user-detail.module.js'
import { ViewUserIndexModule } from './use-cases/view-user-index/view-user-index.module.js'
import { GetOrCreateUserModule } from './use-cases/get-or-create-user/get-or-create-user.module.js'
import { ViewUsersModule } from './use-cases/view-users/view-users.module.js'
import { UsersController } from './controllers/users.controller.js'
import { User } from './entities/user.entity.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Role,
      UserRole
    ]),
    RedisModule,
    TypesenseModule,
    ViewMeModule,
    ViewUserDetailModule,
    ViewUserIndexModule,
    GetOrCreateUserModule,
    ViewUsersModule
  ],
  controllers: [UsersController],
  providers: [UserAuthService],
  exports: [UserAuthService]
})
export class UserModule { }
