import { Controller, Get, Query, UseGuards } from '@nestjs/common'
import { ApiOkResponse } from '@nestjs/swagger'
import { AuthGuard } from '../../../modules/auth/guards/auth.guard.js'
import { User } from '../entities/user.entity.js'
import { ViewUsersUseCase } from '../use-cases/view-users/view-users.use-case.js'

@Controller('api/users')
@UseGuards(AuthGuard)
export class UsersController {
  constructor (private readonly viewUsersUseCase: ViewUsersUseCase) {}

  @Get()
  @ApiOkResponse({ type: User, isArray: true })
  async getUsers (@Query('search') search?: string): Promise<User[]> {
    return await this.viewUsersUseCase.execute(search)
  }
}
