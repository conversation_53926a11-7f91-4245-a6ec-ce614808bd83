import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  type Relation,
  UpdateDateColumn, DeleteDateColumn,
  OneToMany
} from 'typeorm'
import { UserRole } from '../../roles/entities/user-role.entity.js'
import { WasteInquiry } from '../../../modules/waste-inquiry/entities/waste-inquiry.entity.js'
import { Contact } from '../../../modules/contact/entities/contact.entity.js'
import { NewsItem } from '../../../modules/news/entities/news-item.entity.js'
import { Announcement } from '../../../modules/announcement/entities/announcement.entity.js'
import { PickUpRequest } from '../../../modules/pick-up-request/entities/pick-up-request.entity.js'
import { DynamicTableView } from '../../../modules/dynamic-tables/entities/dynamic-table-view.entity.js'
import { UserDefaultDynamicTableView } from '../../../modules/dynamic-tables/entities/user-default-dynamic-table-view.entity.js'
import { WeeklyPlanningRequest } from '../../../modules/weekly-planning-request/entities/weekly-planning-request.entity.js'

@Entity()
export class User {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @Column({ type: 'varchar', nullable: true, unique: true })
  zitadelSub: string | null

  @Column({ type: 'varchar', nullable: true, unique: true })
  azureEntraId: string | null

  @Column({ type: 'varchar', nullable: true, unique: true })
  azureEntraUpn: string | null

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @DeleteDateColumn({ precision: 3 })
  deletedAt: Date

  @Column({ type: 'varchar', unique: true })
  @Index({ unique: true })
  email: string

  @Column({ type: 'varchar', nullable: true })
  firstName: string | null

  @Column({ type: 'varchar', nullable: true })
  lastName: string | null

  @OneToMany(() => UserRole, role => role.user)
  userRoles?: Array<Relation<UserRole>>

  @OneToMany(() => WasteInquiry, wasteInquiry => wasteInquiry.createdByUser)
  wasteInquiries?: Array<Relation<WasteInquiry>>

  @OneToMany(() => PickUpRequest, pickUpRequest => pickUpRequest.createdByUser)
  pickUpRequests?: Array<Relation<PickUpRequest>>

  @OneToMany(() => Contact, contact => contact.user)
  contacts?: Array<Relation<Contact>>

  @OneToMany(() => NewsItem, newsItem => newsItem.createdByUser)
  newsItems?: Array<Relation<NewsItem>>

  @OneToMany(() => Announcement, announcement => announcement.createdByUser)
  announcements?: Array<Relation<Announcement>>

  @OneToMany(() => DynamicTableView, dynamicTableView => dynamicTableView.createdByUser)
  dynamicTableViews?: Array<Relation<DynamicTableView>>

  @OneToMany(() => UserDefaultDynamicTableView,
    defaultTableView => defaultTableView.user
  )
  defaultDynamicTableViews?: Array<Relation<UserDefaultDynamicTableView>>

  @OneToMany(() => WeeklyPlanningRequest,
    weeklyPlanningRequest => weeklyPlanningRequest.createdByUser
  )
  weeklyPlanningRequests?: Array<Relation<WeeklyPlanningRequest>>

  get fullName (): string {
    return `${this.firstName ?? ''} ${this.lastName ?? ''}`.trim()
  }

  get isInternalUser (): boolean {
    const domain = this.email.split('@').at(-1)

    return domain?.toLocaleLowerCase() === 'indaver.com'
  }
}
