import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository, Like } from 'typeorm'
import { User } from '../../entities/user.entity.js'

@Injectable()
export class ViewUsersUseCase {
  constructor (
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {}

  async execute (search?: string): Promise<User[]> {
    const queryBuilder = this.userRepository.createQueryBuilder('user')
      .leftJoinAndSelect('user.userRoles', 'userRoles')
      .leftJoinAndSelect('userRoles.role', 'role')
      .where('user.email NOT LIKE :indaverDomain', { indaverDomain: '%@indaver.com' })
      .orderBy('user.lastName', 'ASC')
      .addOrderBy('user.firstName', 'ASC')

    if (search) {
      const searchTerm = `%${search.toLowerCase()}%`
      queryBuilder.andWhere(
        '(LOWER(user.firstName) LIKE :search OR LOWER(user.lastName) LIKE :search OR LOWER(user.email) LIKE :search)',
        { search: searchTerm }
      )
    }

    return await queryBuilder.getMany()
  }
}
