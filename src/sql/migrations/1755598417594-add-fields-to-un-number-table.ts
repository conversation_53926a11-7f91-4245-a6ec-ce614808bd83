import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddFieldsToUnNumberTable1755598417594 implements MigrationInterface {
  name = 'AddFieldsToUnNumberTable1755598417594'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "un_number" ADD "key" character varying`)
    await queryRunner.query(`ALTER TABLE "un_number" ADD "language" "public"."Locale" NOT NULL DEFAULT 'en-GB'`)
    await queryRunner.query(`ALTER TABLE "un_number" ADD CONSTRAINT "UQ_dcbd069b7163e4345022c1bc97a" UNIQUE ("key", "language")`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "un_number" DROP COLUMN "language"`)
    await queryRunner.query(`ALTER TABLE "un_number" DROP COLUMN "key"`)
    await queryRunner.query(`ALTER TABLE "un_number" DROP CONSTRAINT "UQ_dcbd069b7163e4345022c1bc97a"`)
  }
}
