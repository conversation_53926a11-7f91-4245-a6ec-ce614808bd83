import { MigrationInterface, QueryRunner } from 'typeorm'

export class RemoveAdrClassFromUnNumberTable1755599841162 implements MigrationInterface {
  name = 'RemoveAdrClassFromUnNumberTable1755599841162'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "un_number" DROP COLUMN "adr_class"`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "un_number" ADD "adr_class" character varying`)
  }
}
