import { Coordinates } from '@wisemen/coordinates'
import { randCity, randStreetAddress, randZipCode } from '@ngneat/falso'
import { Address } from './address.type.js'

export class AddressTypeBuilder {
  private readonly address: Address

  constructor () {
    this.address = {
      countryCode: null,
      postalCode: randZipCode(),
      locality: randCity(),
      addressLine1: randStreetAddress(),
      addressLine2: null,
      coordinates: null
    }
  }

  withCountryCode (countryCode: string): this {
    this.address.countryCode = countryCode
    return this
  }

  withPostalCode (postalCode: string): this {
    this.address.postalCode = postalCode
    return this
  }

  withLocality (locality: string): this {
    this.address.locality = locality
    return this
  }

  withAddressLine1 (addressLine1: string): this {
    this.address.addressLine1 = addressLine1
    return this
  }

  withAddressLine2 (addressLine2: string): this {
    this.address.addressLine2 = addressLine2
    return this
  }

  withCoordinates (coordinates: Coordinates): this {
    this.address.coordinates = coordinates
    return this
  }

  build (): Address {
    return this.address
  }
}
