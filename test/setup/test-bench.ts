import { after, mock } from 'node:test'
import process from 'node:process'
import { NestExpressApplication } from '@nestjs/platform-express'
import { Test, TestingModule } from '@nestjs/testing'
import { DataSource } from 'typeorm'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat.js'
import { expect } from 'expect'
import { I18nContext, I18nService } from 'nestjs-i18n'
import { createStubInstance } from 'sinon'
import { TestAuthContext } from '../utils/test-auth-context.js'
import { AuthMiddleware } from '../../src/modules/auth/middleware/auth.middleware.js'
import { uuid } from '../expect/expect-uuid.js'
import { toHaveErrorCode } from '../expect/expect-error-code.js'
import { toHaveStatus } from '../expect/expect-status.js'
import { isEnumValue } from '../expect/expect-enum.js'
import { toHaveApiError } from '../expect/expect-api-error.js'
import { ApiModule } from '../../src/modules/api/api.module.js'
import { toHaveValidationErrors } from '../expect/expect-validation-errors.js'
import { toHaveEmitted } from '../expect/expect-to-have-emitted.js'
import { SubmitOptInRequest } from '../../src/modules/flexmail/requests/submit-opt-in/submit-opt-in.request.js'
import { SapGetUnNumberIndexUseCase } from '../../src/modules/sap/use-cases/get-un-number-index/get-un-number-index.use-case.js'
import { SapGetEwcCodeIndexUseCase } from '../../src/modules/sap/use-cases/get-ewc-code-index/get-ewc-code-index.use-case.js'
import { AzureBlobService } from '../../src/modules/files/services/azure-blob.service.js'
import { GetEwcCodeIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-ewc-code-index/tests/get-ewc-code-index.response.builder.js'
import { GetUnNumberIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-un-number-index/tests/get-un-number-index.response.builder.js'
import { SapCreatePickUpRequestUseCase } from '../../src/modules/sap/use-cases/create-pick-up-request/create-pick-up-request.use-case.js'
import { SapCreatePickUpRequestResponseBuilder } from '../../src/modules/sap/use-cases/create-pick-up-request/tests/create-pick-up-request.response.builder.js'
import { SapCreateWasteInquiryUseCase } from '../../src/modules/sap/use-cases/create-waste-inquiry/create-waste-inquiry.use-case.js'
import { SapCreateWasteInquiryResponseBuilder } from '../../src/modules/sap/use-cases/create-waste-inquiry/tests/create-waste-inquiry.response.builder.js'
import { SapGetContainerTypeIndexUseCase } from '../../src/modules/sap/use-cases/get-container-type-index/get-container-type-index.use-case.js'
import { SapGetContractLineIndexUseCase } from '../../src/modules/sap/use-cases/get-contract-line-index/get-contract-line-index.use-case.js'
import { GetContractLineIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-contract-line-index/tests/get-contract-line-index.response.builder.js'
import { SapGetCustomerIndexUseCase } from '../../src/modules/sap/use-cases/get-customer-index/get-customer-index.use-case.js'
import { SapGetPackagingTypeIndexUseCase } from '../../src/modules/sap/use-cases/get-packaging-type-index/get-packaging-type-index.use-case.js'
import { GetPackagingTypeIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-packaging-type-index/tests/get-packaging-type-index.response.builder.js'
import { SapGetPickUpAddressIndexUseCase } from '../../src/modules/sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.use-case.js'
import { SapGetPickUpRequestIndexUseCase } from '../../src/modules/sap/use-cases/get-pick-up-request-index/get-pick-up-request-index.use-case.js'
import { GetPickUpRequestIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-pick-up-request-index/tests/get-pick-up-request-index.response.builder.js'
import { GetContainerTypeIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-container-type-index/tests/get-container-type-index.response.builder.js'
import { ISO8601 } from '../expect/expect-iso-8601.js'
import { SapGetWasteInquiryIndexUseCase } from '../../src/modules/sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.use-case.js'
import { GetWasteInquiryIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-waste-inquiry-index/tests/get-waste-inquiry-index.response.builder.js'
import { SapGetCustomerSalesOrganizationIndexUseCase } from '../../src/modules/sap/use-cases/get-customer-sales-organization-index/get-customer-sales-organization-index.use-case.js'
import { SapPaginatedResponseBuilder } from '../../src/modules/sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapGetCustomerSalesOrganizationIndexResponse } from '../../src/modules/sap/use-cases/get-customer-sales-organization-index/get-customer-sales-organization-index.response.js'
import { GetCustomerSalesOrganizationIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-customer-sales-organization-index/tests/get-customer-sales-organization-index.response.builder.js'
import { GetCustomerIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-customer-index/tests/get-customer-index.response.builder.js'
import { SapGetCustomerIndexResponse } from '../../src/modules/sap/use-cases/get-customer-index/get-customer-index.response.js'
import { SapGetWasteProducerIndexUseCase } from '../../src/modules/sap/use-cases/get-waste-producer-index/get-waste-producer-index.use-case.js'
import { SapGetWasteProducerIndexResponse } from '../../src/modules/sap/use-cases/get-waste-producer-index/get-waste-producer-index.response.js'
import { GetWasteProducerIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-waste-producer-index/tests/get-waste-producer-index.response.builder.js'
import { SapGetPickUpAddressIndexResponse } from '../../src/modules/sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.response.js'
import { GetPickUpAddressIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-pick-up-address-index/tests/get-pick-up-address-index.response.builder.js'
import { SapCreateWeeklyPlanningRequestResponseBuilder } from '../../src/modules/sap/use-cases/create-weekly-planning-request/tests/create-weekly-planning-request.response.builder.js'
import { SapCreateWeeklyPlanningRequestUseCase } from '../../src/modules/sap/use-cases/create-weekly-planning-request/create-weekly-planning.use-case.js'
import { SapGetWasteInquiryIndexResponse } from '../../src/modules/sap/use-cases/get-waste-inquiry-index/get-waste-inquiry-index.response.js'
import { SapGetIsPoReferenceRequiredUseCase } from '../../src/modules/sap/use-cases/get-is-po-reference-required/get-is-po-reference-required.use-case.js'
import { SapGetIsCostCenterRequiredUseCase } from '../../src/modules/sap/use-cases/get-is-cost-center-required/get-is-cost-center-required.use-case.js'
import { SapGetPickUpRequestDetailUseCase } from '../../src/modules/sap/use-cases/get-pick-up-request-detail/get-pick-up-request-detail.use-case.js'
import { SapGetPickUpRequestDetailResponseBuilder } from '../../src/modules/sap/use-cases/get-pick-up-request-detail/tests/get-pick-up-request-detail.response.builder.js'
import { SapGetInvoiceIndexUseCase } from '../../src/modules/sap/use-cases/get-invoice-index/get-invoice-index.use-case.js'
import { SapGetInvoiceIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-invoice-index/tests/builders/get-invoice-index.response.builder.js'
import { SapGetPickUpRequestCommentUseCase } from '../../src/modules/sap/use-cases/get-pick-up-request-comment/get-pick-up-request-comment.use-case.js'
import { GetPickUpRequestCommentResponseBuilder } from '../../src/modules/sap/use-cases/get-pick-up-request-comment/tests/get-pick-up-request-comment.response.builder.js'
import { SapGetPickUpRequestContactsUseCase } from '../../src/modules/sap/use-cases/get-pick-up-request-contacts/get-pick-up-request-contacts.use-case.js'
import { GetPickUpRequestContactsResponseBuilder } from '../../src/modules/sap/use-cases/get-pick-up-request-contacts/tests/get-pick-up-request-contacts.response.builder.js'
import { SapGetPickUpRequestAttachmentsUseCase } from '../../src/modules/sap/use-cases/get-pick-up-request-attachments/get-pick-up-request-attachments.use-case.js'
import { GetPickUpRequestAttachmentsResponseBuilder } from '../../src/modules/sap/use-cases/get-pick-up-request-attachments/tests/get-pick-up-request-attachments.response.builder.js'
import { SapGetTankerTypeIndexUseCase } from '../../src/modules/sap/use-cases/get-tanker-type-index/get-tanker-type-index.use-case.js'
import { GetTankerTypeIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-tanker-type-index/tests/get-tanker-type-index.response.builder.js'
import { SapGetUnNumberIndexForPickUpRequestUseCase } from '../../src/modules/sap/use-cases/get-un-number-index-for-pick-up-request/get-un-number-index-for-pick-up-request.use-case.js'
import { GetUnNumberIndexForPickUpRequestResponseBuilder } from '../../src/modules/sap/use-cases/get-un-number-index-for-pick-up-request/tests/get-un-number-index-for-pick-up-request.response.builder.js'
import { SapGetDraftInvoiceIndexUseCase } from '../../src/modules/sap/use-cases/get-draft-invoice-index/get-draft-invoice-index.use-case.js'
import { SapGetDraftInvoiceIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-draft-invoice-index/tests/builders/get-draft-invoice-index.response.builder.js'
import { SapGetTransportTypeIndexUseCase } from '../../src/modules/sap/use-cases/get-transport-type-index/get-transport-type-index.use-case.js'
import { GetTransportTypeIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-transport-type-index/tests/get-transport-type-index.response.builder.js'
import { SapGetSalesOrganisationIndexUseCase } from '../../src/modules/sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.use-case.js'
import { GetSalesOrganisationIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-sales-organisation-index/tests/get-sales-organisation-index.response.builder.js'
import { SapUpdatePickUpRequestUseCase } from '../../src/modules/sap/use-cases/update-pick-up-request/update-pick-up-request.use-case.js'
import { SapApproveDraftInvoiceCustomerUseCase } from '../../src/modules/sap/use-cases/approve-draft-invoice-customer/approve-draft-invoice-customer.use-case.js'
import { SapApproveDraftInvoiceSalesRepUseCase } from '../../src/modules/sap/use-cases/approve-draft-invoice-sales-rep/approve-draft-invoice-sales-rep.use-case.js'
import { GetUserSiteIndexUseCase } from '../../src/modules/sharepoint/use-cases/get-user-site-index/get-user-site-index.use-case.js'
import { GetUserSiteIndexResponseBuilder } from '../../src/modules/sharepoint/use-cases/get-user-site-index/builders/get-user-site-index.response.builder.js'
import { GetDocumentIndexUseCae } from '../../src/modules/sharepoint/use-cases/get-document-index/get-document-index.use-case.js'
import { GetDocumentIndexResponseBuilder } from '../../src/modules/sharepoint/use-cases/get-document-index/builders/get-document-index.response.builder.js'
import { SharepointDownloadDocumentUseCase } from '../../src/modules/sharepoint/use-cases/download-document/download-document.use-case.js'
import { SapRejectDraftInvoiceCustomerUseCase } from '../../src/modules/sap/use-cases/reject-draft-invoice-customer/reject-draft-invoice-customer.use-case.js'
import { SapRejectDraftInvoiceSalesRepUseCase } from '../../src/modules/sap/use-cases/reject-draft-invoice-sales-rep/reject-draft-invoice-sales-rep.use-case.js'
import { SapGetIndascanDetailUseCase } from '../../src/modules/sap/use-cases/get-indascan-detail/get-indascan-detail.use-case.js'
import { SapGetIndascanDetailResponseBuilder } from '../../src/modules/sap/use-cases/get-indascan-detail/tests/builders/get-indascan-detail.response.builder.js'
import { SapGenerateContractLinesPdfUseCase } from '../../src/modules/sap/use-cases/generate-contract-lines-pdf/generate-contract-lines-pdf.use-case.js'
import { SapGenerateContractLinesPdfResponseBuilder } from '../../src/modules/sap/use-cases/generate-contract-lines-pdf/tests/generate-contract-lines-pdf.response.builder.js'
import { SapGetGuidanceLetterIndexUseCase } from '../../src/modules/sap/use-cases/get-guidance-letter-index/get-guidance-letter-index.use-case.js'
import { SapGetGuidanceLetterIndexResponseBuilder } from '../../src/modules/sap/use-cases/get-guidance-letter-index/tests/get-guidance-letter-index.response.builder.js'
import { SapUploadDocumentUseCase } from '../../src/modules/sap/use-cases/upload-document/upload-document.use-case.js'
import { SapUploadDocumentResponseBuilder } from '../../src/modules/sap/use-cases/upload-document/tests/upload-document.response.builder.js'
import { SapCreateDocumentUseCase } from '../../src/modules/sap/use-cases/create-document/create-document.use-case.js'
import { SapGetDocumentsIndexUseCase } from '../../src/modules/sap/use-cases/get-documents-index/get-documents-index.use-case.js'
import { SharepointGetDocumentFiltersUseCase } from '../../src/modules/sharepoint/use-cases/get-document-filters/get-document-filters.use-case.js'
import { GetDocumentFiltersResponseBuilder } from '../../src/modules/sharepoint/use-cases/get-document-filters/builders/get-document-filters.response.builder.js'
import { EndToEndTestSetup } from './end-to-end-test-setup.js'
import { RepositoryTestSetup } from './repository-test-setup.js'

after(async () => TestBench.tearDown())

export class TestBench {
  private static _app: NestExpressApplication | undefined
  private static _testModule: TestingModule | undefined
  private static _dataSource: DataSource | undefined
  private static _authContext: TestAuthContext | undefined
  private static _isUnitTestSetup: boolean = false
  private static _isEndToEndTestSetup: boolean = false

  public static async setupEndToEndTest (): Promise<EndToEndTestSetup> {
    if (process.env.NODE_ENV !== 'test') {
      throw new Error('NODE_ENV must be set to test')
    }

    this.setupUnitTest()
    await this.initApp()

    return await EndToEndTestSetup.create({
      app: this._app as NestExpressApplication,
      authContext: this._authContext as TestAuthContext,
      dataSource: this._dataSource as DataSource,
      testModule: this._testModule as TestingModule
    })
  }

  public static async setupRepositoryTest (): Promise<RepositoryTestSetup> {
    if (process.env.NODE_ENV !== 'test') {
      throw new Error('NODE_ENV must be set to test')
    }

    this.setupUnitTest()
    await this.initApp()

    return RepositoryTestSetup.create(this._dataSource as DataSource)
  }

  public static setupUnitTest (): void {
    if (process.env.NODE_ENV !== 'test') {
      throw new Error('NODE_ENV must be set to test')
    }

    if (!this._isUnitTestSetup) {
      this.mockAzureBlob()
      this.mockFlexmail()
      this.mockSap()
      this.mockSharepoint()
      this.extendExpect()
      this.mockI18n()
      dayjs.extend(customParseFormat)
      this._isUnitTestSetup = true
    }
  }

  public static async tearDown (): Promise<void> {
    await this._app?.close()
  }

  private static async initApp () {
    if (!this._isEndToEndTestSetup) {
      const testModule = await Test.createTestingModule({ imports: [ApiModule] }).compile()

      const [app, dataSource] = await Promise.all([
        this.initializeApp(testModule),
        this.initializeDatabaseConnection(testModule)
      ])

      const authContext = new TestAuthContext(dataSource.manager)

      this.mockAuth(authContext)

      this._app = app
      this._testModule = testModule
      this._dataSource = dataSource
      this._authContext = authContext

      this._isEndToEndTestSetup = true
    }
  }

  private static async initializeDatabaseConnection (testModule: TestingModule) {
    const dataSource = testModule.get(DataSource)
    const qr = dataSource.createQueryRunner()

    await qr.connect()
    Object.defineProperty(dataSource.manager, 'queryRunner', {
      configurable: true,
      value: qr
    })

    return dataSource
  }

  private static async initializeApp (testModule: TestingModule) {
    const app = testModule.createNestApplication<NestExpressApplication>()

    await app.init()

    return app
  }

  private static mockAuth (context: TestAuthContext): void {
    mock.method(AuthMiddleware.prototype, 'verify', async (token: string) => {
      const user = context.resolveUser(token)

      return Promise.resolve({
        zitadelSub: user.zitadelSub,
        uuid: user.uuid,
        azureEntraId: user.azureEntraId,
        azureEntraUpn: user.azureEntraUpn,
        selectedCustomerId: null
      })
    })
  }

  private static mockAzureBlob (): void {
    mock.method(AzureBlobService.prototype, 'createTemporaryDownloadUrl', () => 'http://localhost:3000')
    mock.method(AzureBlobService.prototype, 'createTemporaryUploadUrl', () => 'http://localhost:3000')
    mock.method(AzureBlobService.prototype, 'delete', () => {})
  }

  private static mockI18n (): void {
    const service = createStubInstance(I18nService)

    mock.method(I18nContext, 'current', () => {
      return new I18nContext('en', service)
    })
  }

  private static mockFlexmail (): void {
    mock.method(SubmitOptInRequest.prototype, 'execute', () => {})
  }

  private static mockSap (): void {
    mock.method(SapApproveDraftInvoiceCustomerUseCase.prototype, 'execute', () => {})
    mock.method(SapApproveDraftInvoiceSalesRepUseCase.prototype, 'execute', () => {})
    mock.method(SapCreatePickUpRequestUseCase.prototype, 'execute', () => {
      return new SapCreatePickUpRequestResponseBuilder().build()
    })
    mock.method(SapCreateWasteInquiryUseCase.prototype, 'execute', () => {
      return new SapCreateWasteInquiryResponseBuilder().build()
    })
    mock.method(SapCreateWeeklyPlanningRequestUseCase.prototype, 'execute', () => {
      return new SapCreateWeeklyPlanningRequestResponseBuilder().build()
    })
    mock.method(SapGenerateContractLinesPdfUseCase.prototype, 'execute', () => {
      return new SapGenerateContractLinesPdfResponseBuilder().build()
    })
    mock.method(SapGetContainerTypeIndexUseCase.prototype, 'execute', () => {
      return [new GetContainerTypeIndexResponseBuilder().build()]
    })
    mock.method(SapGetIsCostCenterRequiredUseCase.prototype, 'execute', () => {
      return false
    })
    mock.method(SapGetIsPoReferenceRequiredUseCase.prototype, 'execute', () => {
      return false
    })
    mock.method(SapGetContractLineIndexUseCase.prototype, 'execute', () => {
      return [new GetContractLineIndexResponseBuilder().build()]
    })
    mock.method(SapGetCustomerIndexUseCase.prototype, 'execute', () => {
      return new SapPaginatedResponseBuilder<SapGetCustomerIndexResponse>()
        .addItem(new GetCustomerIndexResponseBuilder().build())
        .build()
    })
    mock.method(SapGetCustomerSalesOrganizationIndexUseCase.prototype, 'execute', () => {
      return new SapPaginatedResponseBuilder<SapGetCustomerSalesOrganizationIndexResponse>()
        .addItem(new GetCustomerSalesOrganizationIndexResponseBuilder().build())
        .build()
    })
    mock.method(SapGetDraftInvoiceIndexUseCase.prototype, 'execute', () => {
      return [new SapGetDraftInvoiceIndexResponseBuilder().build()]
    })
    mock.method(SapGetEwcCodeIndexUseCase.prototype, 'execute', () => {
      return [new GetEwcCodeIndexResponseBuilder().build()]
    })
    mock.method(SapGetIndascanDetailUseCase.prototype, 'execute', () => {
      return [new SapGetIndascanDetailResponseBuilder().build()]
    })
    mock.method(SapGetInvoiceIndexUseCase.prototype, 'execute', () => {
      return [new SapGetInvoiceIndexResponseBuilder().build()]
    })
    mock.method(SapGetPackagingTypeIndexUseCase.prototype, 'execute', () => {
      return [new GetPackagingTypeIndexResponseBuilder().build()]
    })
    mock.method(SapGetPickUpAddressIndexUseCase.prototype, 'execute', () => {
      return new SapPaginatedResponseBuilder<SapGetPickUpAddressIndexResponse>()
        .addItem(new GetPickUpAddressIndexResponseBuilder().build())
        .build()
    })
    mock.method(SapGetPickUpRequestAttachmentsUseCase.prototype, 'execute', () => {
      return [new GetPickUpRequestAttachmentsResponseBuilder().build()]
    })
    mock.method(SapGetPickUpRequestCommentUseCase.prototype, 'execute', () => {
      return [new GetPickUpRequestCommentResponseBuilder().build()]
    })
    mock.method(SapGetPickUpRequestContactsUseCase.prototype, 'execute', () => {
      return [new GetPickUpRequestContactsResponseBuilder().build()]
    })
    mock.method(SapGetPickUpRequestDetailUseCase.prototype, 'execute', () => {
      return [new SapGetPickUpRequestDetailResponseBuilder().build()]
    })
    mock.method(SapGetPickUpRequestIndexUseCase.prototype, 'execute', () => {
      return [new GetPickUpRequestIndexResponseBuilder().build()]
    })
    mock.method(SapGetSalesOrganisationIndexUseCase.prototype, 'execute', () => {
      return [new GetSalesOrganisationIndexResponseBuilder().build()]
    })
    mock.method(SapGetTankerTypeIndexUseCase.prototype, 'execute', () => {
      return [new GetTankerTypeIndexResponseBuilder().build()]
    })
    mock.method(SapGetTransportTypeIndexUseCase.prototype, 'execute', () => {
      return [new GetTransportTypeIndexResponseBuilder().build()]
    })
    mock.method(SapGetUnNumberIndexForPickUpRequestUseCase.prototype, 'execute', () => {
      return [new GetUnNumberIndexForPickUpRequestResponseBuilder().build()]
    })
    mock.method(SapGetUnNumberIndexUseCase.prototype, 'execute', () => {
      return {
        items: new GetUnNumberIndexResponseBuilder().build(),
        total: 0
      }
    })
    mock.method(SapGetWasteInquiryIndexUseCase.prototype, 'execute', () => {
      return new SapPaginatedResponseBuilder<SapGetWasteInquiryIndexResponse>()
        .addItem(new GetWasteInquiryIndexResponseBuilder().build())
        .build()
    })
    mock.method(SapGetWasteProducerIndexUseCase.prototype, 'execute', () => {
      return new SapPaginatedResponseBuilder<SapGetWasteProducerIndexResponse>()
        .addItem(new GetWasteProducerIndexResponseBuilder().build())
        .build()
    })
    mock.method(SapRejectDraftInvoiceCustomerUseCase.prototype, 'execute', () => {})
    mock.method(SapRejectDraftInvoiceSalesRepUseCase.prototype, 'execute', () => {})
    mock.method(SapUpdatePickUpRequestUseCase.prototype, 'execute', () => {})
    mock.method(SapGetGuidanceLetterIndexUseCase.prototype, 'execute', () => {
      return [new SapGetGuidanceLetterIndexResponseBuilder().build()]
    })
    mock.method(SapGetDocumentsIndexUseCase.prototype, 'execute', () => {
      return [new GetDocumentIndexResponseBuilder().build()]
    })
    mock.method(SapUploadDocumentUseCase.prototype, 'execute', () => {
      return [new SapUploadDocumentResponseBuilder().build()]
    })
    mock.method(SapCreateDocumentUseCase.prototype, 'execute', () => {
      return Promise.resolve()
    })
  }

  private static mockSharepoint (): void {
    mock.method(SharepointGetDocumentFiltersUseCase.prototype, 'execute', () => {
      return new GetDocumentFiltersResponseBuilder().build()
    })
    mock.method(GetDocumentIndexUseCae.prototype, 'execute', () => {
      return new GetDocumentIndexResponseBuilder().build()
    })
    mock.method(GetUserSiteIndexUseCase.prototype, 'execute', () => {
      return [new GetUserSiteIndexResponseBuilder().build()]
    })
    mock.method(SharepointDownloadDocumentUseCase.prototype, 'execute', () => {
      return 'http://localhost:3000'
    })
  }

  private static extendExpect (): void {
    expect.extend({
      uuid,
      toHaveErrorCode,
      toHaveStatus,
      isEnumValue,
      toHaveApiError,
      toHaveValidationErrors,
      toHaveEmitted,
      ISO8601
    })
  }
}
